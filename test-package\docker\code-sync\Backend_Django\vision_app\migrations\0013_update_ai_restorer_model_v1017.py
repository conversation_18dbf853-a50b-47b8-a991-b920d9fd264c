# Generated by Django 5.2.1 on 2025-05-29 03:00

from django.db import migrations
from django.utils import timezone

def add_multiple_ai_restorer_models(apps, schema_editor):
    """
    添加多个版本的AI修复模型到数据库
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    # 定义要添加的模型版本
    models_to_add = [
        {
            'name': 'AI_Restorer_V*******',
            'description': 'AI图像修复模型 - 版本******* (稳定版)',
            'model_file': 'AI_Restorer_NCHW_1x1x256x256_V*******.onnx',
            'version': '*******'
        },
        {
            'name': 'AI_Restorer_V*******',
            'description': 'AI图像修复模型 - 版本******* (最新版)',
            'model_file': 'AI_Restorer_NCHW_1x1x256x256_V*******.onnx',
            'version': '*******'
        }
    ]

    # 添加每个模型
    for model_info in models_to_add:
        _, created = AIModel.objects.using(db_alias).update_or_create(
            name=model_info['name'],
            model_type='ai_restored',
            is_system_model=True,
            defaults={
                'description': model_info['description'],
                'model_file': model_info['model_file'],
                'version': model_info['version'],
                'uploaded_at': timezone.now()
            }
        )

        if created:
            print(f"Created AI Restorer model: {model_info['name']} v{model_info['version']}")
        else:
            print(f"Updated AI Restorer model: {model_info['name']} v{model_info['version']}")

    # 删除旧的单一模型记录（如果存在）
    try:
        old_model = AIModel.objects.using(db_alias).get(
            name="AI_Restorer_NCHW_1x1x256x256",
            model_type="ai_restored",
            is_system_model=True
        )
        old_model.delete()
        print("Removed old AI Restorer model record")
    except AIModel.DoesNotExist:
        print("No old AI Restorer model record to remove")

def rollback_ai_restorer_model(apps, schema_editor):
    """
    回滚AI修复模型到版本*******
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    try:
        ai_restorer_model = AIModel.objects.using(db_alias).get(
            name="AI_Restorer_NCHW_1x1x256x256",
            model_type="ai_restored",
            is_system_model=True
        )
        
        # 回滚到旧版本
        ai_restorer_model.model_file = "AI_Restorer_NCHW_1x1x256x256_V*******.onnx"
        ai_restorer_model.version = "*******"
        ai_restorer_model.description = "系统内置的 AI 图像修复模型。"
        ai_restorer_model.save()
        
        print("Rolled back AI Restorer model to version *******")
        
    except AIModel.DoesNotExist:
        print("AI Restorer model not found during rollback")

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0012_populate_pp_ocrv4_mobile_models'),
    ]

    operations = [
        migrations.RunPython(add_multiple_ai_restorer_models, rollback_ai_restorer_model),
    ]
