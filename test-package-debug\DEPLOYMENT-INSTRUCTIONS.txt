﻿=== AI Vision App - Hot Reload Server Deployment Guide ===
Generated: 2025-08-08 15:45:05

QUICK HOT RELOAD DEPLOYMENT STEPS:

1. Copy Deployment Package
   Copy the entire deployment package to server, recommended path: C:\ai_vision_app\

2. Build Docker Images (Required for first deployment):
   cd docker
   .\scripts\build-images-deployment.ps1

   Build options:
   - Full build: .\scripts\build-images-deployment.ps1
   - Backend only: .\scripts\build-images-deployment.ps1 -BackendOnly
   - Frontend only: .\scripts\build-images-deployment.ps1 -FrontendOnly
   - No cache: .\scripts\build-images-deployment.ps1 -NoCache

3. Import Docker Images (Alternative to building):
   If you have pre-built images:
   .\scripts\import-images.ps1

4. Hot Reload Deployment:

   Method A - Auto build and start:
   .\scripts\deploy-hotreload.ps1 -Mode start

   Method B - Manual frontend build:
   .\scripts\build-frontend-hotreload.ps1
   .\scripts\deploy-hotreload.ps1 -Mode start

5. Configure Auto-Start (Recommended for Production):
   .\scripts\enable-auto-start.ps1

   This ensures containers automatically restart when Docker service starts.
   Verify with: docker inspect <container-name> --format '{{.HostConfig.RestartPolicy.Name}}'

6. Configure Network Settings (Optional):
   Copy and modify environment files for your network:
   - .env.example -> .env (main configuration)
   - Backend_Django/.env.lan.example -> Backend_Django/.env (LAN access)
   - Frontend/.env.lan.example -> Frontend/.env.local (frontend LAN)

7. Configure Scanner Device (Optional):
   Set scanner environment variables in .env:
   - SCANNER_ENABLED=true
   - SCANNER_DEFAULT_IP=************
   - SCANNER_DEFAULT_PORT=8080

8. Access Application:
   Frontend: http://localhost:8080 or http://ServerIP:8080
   Backend:  http://localhost:8000 or http://ServerIP:8000

9. Cross-Computer Code Sync (Development Collaboration):
   On Dev Machine A: .\scripts\setup-code-sharing-simple.ps1 setup
   On Deploy Machine B: .\scripts\sync-from-dev.ps1 -DevMachineIP "MachineA_IP" -Mode watch

HOT RELOAD DIRECTORY STRUCTURE:
docker/
|-- code-sync/                    (Hot reload code sync directory)
|   |-- Backend_Django/           (Backend code, auto-reload)
|   +-- Frontend/                 (Frontend source and build output)
|       +-- dist/                 (Frontend build output, served by nginx)
|-- data/                         (Persistent data directory)
|   |-- db/                       (Database files - includes complete model records)
|   |-- models/                   (AI model files - system and custom models)
|   |-- media/                    (Media files)
|   +-- logs/                     (Log files)
+-- scripts/                      (Management scripts)

HOT RELOAD FEATURES:
- Backend code changes auto-reload (Django runserver)
- Frontend code changes need rebuild (build-frontend-hotreload.ps1)
- AI model files persistent storage
- Database and logs persistence
- Cross-computer code sync support

DEVELOPMENT WORKFLOW:
1. Modify backend code -> Auto effective (Django hot reload)
2. Modify frontend code -> Run build-frontend-hotreload.ps1 -> Refresh browser
3. Cross-computer sync -> sync-from-dev.ps1 auto sync code changes

TROUBLESHOOTING:

Common Build Issues:
- If build fails with "No module named 'channels'": Run .\scripts\build-images-deployment.ps1 -NoCache
- If frontend build fails with package.json not found: Ensure code-sync/Frontend/ directory exists
- If backend build fails: Check code-sync/Backend_Django/requirements_docker.txt exists

Environment Checks:
- Environment check: .\scripts\check\check-docker-env.ps1
- Network check: .\scripts\check\check-network.ps1
- LAN access check: .\scripts\check\check-lan-access.ps1

Container Issues:
- View backend logs: docker logs ai-vision-backend-hotreload
- View frontend logs: docker logs ai-vision-frontend-hotreload
- Restart containers: .\scripts\deploy-hotreload.ps1 -Mode restart

DEPLOYMENT PACKAGE CONTENTS:
- docker/                              (Docker configs and scripts)
  |-- code-sync/                       (Hot reload source code)
  |   |-- Backend_Django/              (Backend source code for hot reload)
  |   +-- Frontend/                    (Frontend source code for hot reload)
  |-- data/                            (Persistent data storage)
  |-- docker-compose.*.yml             (Multiple deployment configs)
  |-- Dockerfile.*                     (Image build files)
  |-- nginx*.conf                      (Nginx configurations)
  |-- scripts/                         (Management scripts)
  |   |-- Core deployment scripts (4)
  |   |-- Image management scripts (3)
  |   |-- Development workflow scripts (2)
  |   |-- System config scripts (4)
  |   +-- check/ (Diagnostic tools 3)
  +-- docker-images/ (Docker image files)
- .env.example                         (Environment config template)

DETAILED DOCUMENTATION:
- Complete deployment guide: docker/README.md
- Frontend development docs: docker/code-sync/Frontend/README.md
- Backend development docs: docker/code-sync/Backend_Django/README.md

RECOMMENDED WORKFLOWS:
1. Daily development: deploy-hotreload.ps1 -Mode start
2. Production deployment: start-container.ps1
3. Cross-computer collaboration: setup-code-sharing-simple.ps1 + sync-from-dev.ps1
4. Troubleshooting: Diagnostic scripts in check/ directory

TIPS: All scripts support -? parameter for help information

COMMON COMMANDS:
- Start hot reload: .\scripts\deploy-hotreload.ps1 -Mode start
- Stop services: .\scripts\deploy-hotreload.ps1 -Mode stop
- Enable auto-start: .\scripts\enable-auto-start.ps1
- Build frontend: .\scripts\build-frontend-hotreload.ps1
- Check environment: .\scripts\check\check-docker-env.ps1
- Check network: .\scripts\check\check-network.ps1

SYSTEM REQUIREMENTS:
- Windows 10/11
- Docker Desktop
- PowerShell 5.1 or later
- Node.js 18+ (for frontend building)
- At least 8GB RAM
- At least 20GB free disk space

SUPPORT:
- Check logs in docker/data/logs/
- Use diagnostic scripts in docker/scripts/check/
- Review README files for detailed information