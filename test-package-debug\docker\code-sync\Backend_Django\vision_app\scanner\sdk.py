#!python
from typing import Callable
import struct
import socket
import threading
import time
import abc
import os
import datetime


class AutoSdk(abc.ABC):
    """Abstract base class for the C-proxy functionality"""

    def __init__(self):
        self.callbacks = {}  # Dictionary to store callbacks, keyed by unique ID
        self.next_id = 0  # Auto-increment ID for generating unique identifiers
        self.callbacks_lock = threading.Lock()  # Lock to protect callbacks dictionary
        self.running = True
        # Start receiving thread
        self.receive_thread = threading.Thread(target=self._receive_loop)
        self.receive_thread.daemon = True
        self.receive_thread.start()

    def __enter__(self):
        """Supports 'with' statement"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up resources when exiting 'with' block"""
        self.running = False
        self._close_connection()
        self.receive_thread.join(timeout=1.0)  # Wait for receive thread to end
        return False  # Do not suppress exceptions

    @abc.abstractmethod
    def _close_connection(self):
        """Close the underlying connection"""
        pass

    @abc.abstractmethod
    def _receive_data(self, length):
        """Try to receive a chunk of data

        Args:
            length: Maximum number of bytes to receive

        Returns:
            Bytes received, or None if connection closed
        """
        pass

    @abc.abstractmethod
    def _receive_view(self, view):
        """Receive data directly into a memoryview

        Args:
            view: memoryview to receive data into

        Returns:
            Number of bytes received, or -1 if connection closed
        """
        pass

    @abc.abstractmethod
    def _send_data(self, data):
        """Send data over the connection

        Args:
            data: The data to send

        Returns:
            Number of bytes sent
        """
        pass

    def _receive_packet(self, data):
        # First, get the header (at least 16 bytes)
        while len(data) < 16:
            data += self._receive_data(128)

        # Parse packet length from header
        packet_len = max(struct.unpack_from('I', data, 4)[0], 16)

        # If we already have the full packet, return it
        if len(data) >= packet_len:
            return data[:packet_len], data[packet_len:]

        # Need more data - create a buffer of exact size needed
        full_packet = bytearray(packet_len)

        # Copy what we already have
        full_packet[:len(data)] = data

        # Create a view for receiving the rest
        view = memoryview(full_packet)[len(data):]
        remaining = packet_len - len(data)
        received = 0

        # Receive remaining data directly into the buffer
        while received < remaining:
            bytes_read = self._receive_view(view[received:])
            if bytes_read < 0:  # Connection closed
                return None
            received += bytes_read

        # Return the complete packet
        return bytes(full_packet), b''

    def _process_packet(self, data):
        if len(data) < 16:
            return
        if data[0] != 0xfe:
            return
        # Get function index and callback ID
        func_idx = struct.unpack_from('H', data, 2)[0]  # Command ID
        callback_id = struct.unpack_from('B', data, 1)[0]  # Callback ID

        # Find corresponding callback function
        with self.callbacks_lock:
            callback = self.callbacks.get(callback_id, None)
            if callback:
                # Call the appropriate hook function
                if 0 <= func_idx < len(self._autosdk_func_tb_):
                    hook_func = self._autosdk_func_tb_[func_idx]
                    hook_func(self, data, callback)

                # Remove used callback
                del self.callbacks[callback_id]

    def _receive_loop(self):
        extra_data = b''
        # Main receive loop
        while self.running:
            try:
                # Receive one packet
                packet, extra_data = self._receive_packet(extra_data)
                # Process the received packet
                self._process_packet(packet)
            except (socket.error, ConnectionError, OSError):
                break

    def block(self, timeout=None):
        """Wait for all pending callbacks to complete

        Args:
            timeout: Maximum time to wait in seconds, None for no timeout

        Returns:
            True if all callbacks completed, False if timed out
        """
        start_time = time.time()

        while self.running:
            # Check if there are any pending callbacks
            with self.callbacks_lock:
                if not self.callbacks:
                    return True

            # Check timeout
            if timeout is not None and time.time() - start_time > timeout:
                return False

            # Wait a bit before checking again
            time.sleep(0.01)

        return False  # Not running anymore

    def alloc(self, callback: Callable) -> int:
        """Allocate a unique ID and store callback function"""
        with self.callbacks_lock:
            callback_id = self.next_id
            self.next_id = (self.next_id + 1) & 0xFF  # Ensure ID cycles within 0-255 range
            self.callbacks[callback_id] = callback
            return callback_id

    def prior(self) -> None:
        """Prior prepare for write"""
        pass

    def write(self, data: bytes) -> None:
        """Write data to the connection"""
        self._send_data(data)

    def flush(self) -> None:
        """Flush data - implementation depends on connection type"""
        pass

    # functions-py >>
    def MdScanner_Hello(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 0)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_Hello_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_Start(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 1)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_Start_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_Stop(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 2)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_Stop_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_GetImage(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 3)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 36)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_GetImage_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        width = struct.unpack_from('i', _data, 16)[0]  # Read width
        height = struct.unpack_from('i', _data, 20)[0]  # Read height
        format = struct.unpack_from('i', _data, 24)[0]  # Read format
        size = struct.unpack_from('i', _data, 28)[0]  # Read size
        _ptr = 36  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 32)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(width, height, format, size, out, _ret)
        return _ret

    _autosdk_func_tb_ = [
        _MdScanner_Hello_hook_,
        _MdScanner_Start_hook_,
        _MdScanner_Stop_hook_,
        _MdScanner_GetImage_hook_,
]
    # functions-py <<


# TCP implementation of AutoSdk
class MdScanner(AutoSdk):
    def __init__(self, addr):
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect((addr, 9999))
        self.sock.settimeout(1.5)
        self.heartbeat = threading.Timer(1.0, self._heartbeat)
        self.heartbeat.start()
        super().__init__()

    def _close_connection(self):
        self.heartbeat.cancel()
        self.sock.close()

    def _receive_data(self, length):
        chunk = self.sock.recv(length)  # Receive a chunk of reasonable size
        if not chunk:  # Connection closed
            raise socket.error("Connection closed")
        return chunk

    def _receive_view(self, view):
        bytes_read = self.sock.recv_into(view)
        if bytes_read == 0:  # Connection closed
            raise socket.error("Connection closed")
        return bytes_read

    def _send_data(self, data):
        self.heartbeat.cancel()
        ret = self.sock.send(data)
        self.heartbeat = threading.Timer(1.0, self._heartbeat)
        self.heartbeat.start()
        return ret

    def _heartbeat(self):
        try:
            self.sock.sendall(b'\x00'*16)
        except:
            return
        self.heartbeat = threading.Timer(1.0, self._heartbeat)
        self.heartbeat.start()
        
    def start_continuous_capture(self, callback, buffer_size=1024*1280*2):
        """
        开始连续捕获图像
        :param callback: 图像回调函数
        :param buffer_size: 图像缓冲区大小
        """
        self.image_callback = callback
        self.buffer_size = buffer_size
        self.is_capturing = True
        self.capture_semaphore = threading.Semaphore(1)  # 初始化信号量，初始值为1
        self.capture_thread = threading.Thread(target=self._capture_loop)
        self.capture_thread.daemon = True
        self.capture_thread.start()
        
    def stop_continuous_capture(self):
        """
        停止连续捕获图像
        """
        self.is_capturing = False
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join()
            
    def _capture_loop(self):
        """
        图像捕获循环
        """
        while self.is_capturing and self.running:
            try:
                self.capture_semaphore.acquire()  # 等待信号量
                # 请求图像数据
                self.MdScanner_GetImage(self.buffer_size, self._internal_image_callback)
            except Exception as e:
                print(f"图像捕获错误: {e}")
                self.capture_semaphore.release()  # 发生错误时释放信号量
                time.sleep(1)
                
    def _internal_image_callback(self, width, height, format, size, out, ret):
        """
        内部队像回调函数，用于连续捕获
        """
        try:
            if self.image_callback:
                self.image_callback(width, height, format, size, out, ret)
        finally:
            self.capture_semaphore.release()  # 确保信号量总是被释放


def _demo_(hand):
    # 创建输出目录
    output_dir = "output_image"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")
    
    semaphore = threading.Semaphore(0)

    def _hello_callback_(out, ret):
        print(f"hello result: {ret}, out: {out}")
        semaphore.release()
    hand.MdScanner_Hello(100, _hello_callback_)  # 100 bytes of out buffer
    semaphore.acquire()

    def _start_callback_(ret):
        print(f"scanner started: {ret}")
        semaphore.release()
    hand.MdScanner_Start(_start_callback_)
    semaphore.acquire()

    def _image_callback_(width, height, format, size, out, ret):
        nonlocal stamp
        curr = time.time()
        fps = 1 / (curr - stamp)
        stamp = curr
        print(f"image: {len(out)}, {width}x{height}@{fps:.2f}fps")
        
        # 保存图像数据
        if ret == 0 and len(out) > 0:  # 成功获取图像
            try:
                # 生成时间戳文件名
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
                
                # 根据格式确定文件扩展名
                if format == 0:  # 假设0为raw格式
                    file_ext = "raw"
                elif format == 1:  # 假设1为bitmap格式
                    file_ext = "bmp"
                else:
                    file_ext = f"fmt{format}"  # 未知格式使用格式号
                
                filename = f"image_{timestamp}_{width}x{height}.{file_ext}"
                filepath = os.path.join(output_dir, filename)
                
                # 写入图像数据
                with open(filepath, 'wb') as f:
                    f.write(out)
                
                print(f"Image saved: {filename} ({len(out)} bytes)")
                
            except Exception as e:
                print(f"Failed to save image: {e}")
        
        semaphore.release()
    stamp = time.time()
    for _ in range(10):
        hand.MdScanner_GetImage(1024*1280*2, _image_callback_)
        semaphore.acquire()

    def _stop_callback_(ret):
        print(f"scanner stopped: {ret}")
        semaphore.release()
    hand.MdScanner_Stop(_stop_callback_)
    semaphore.acquire()


if __name__ == "__main__":
    with MdScanner("192.168.77.1") as hand:
        _demo_(hand)