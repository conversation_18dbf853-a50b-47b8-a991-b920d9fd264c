#!/usr/bin/env node
/**
 * 前端配置检查脚本
 *
 * 用于验证前端网络配置的正确性
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径 (ES模块中的 __dirname 替代)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvFile(filePath, description) {
  log(`\n🔍 检查 ${description}`, 'blue');
  
  if (!fs.existsSync(filePath)) {
    log(`❌ 文件不存在: ${filePath}`, 'red');
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  const config = {};
  const issues = [];
  
  lines.forEach((line, index) => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
      const [key, value] = trimmed.split('=', 2);
      config[key.trim()] = value.trim();
    }
  });
  
  // 检查必要的配置项（根据文件名判断）
  let requiredVars = ['VITE_API_BASE_URL'];

  // 只有开发环境和示例文件需要 VITE_BACKEND_URL
  if (description.includes('开发环境') || description.includes('示例') || description.includes('局域网')) {
    requiredVars.push('VITE_BACKEND_URL');
  }
  
  const optionalVars = [
    'VITE_BACKEND_HOST',
    'VITE_BACKEND_PORT',
    'VITE_FRONTEND_HOST',
    'VITE_FRONTEND_PORT'
  ];
  
  // 检查必要配置
  requiredVars.forEach(varName => {
    if (!config[varName]) {
      issues.push(`缺少必要配置: ${varName}`);
    } else {
      log(`✅ ${varName}: ${config[varName]}`, 'green');
    }
  });
  
  // 检查可选配置
  optionalVars.forEach(varName => {
    if (config[varName]) {
      log(`✅ ${varName}: ${config[varName]}`, 'green');
    } else {
      log(`ℹ️ ${varName}: 未设置 (使用默认值)`, 'yellow');
    }
  });
  
  // 验证端口号
  if (config.VITE_BACKEND_PORT) {
    const port = parseInt(config.VITE_BACKEND_PORT);
    if (isNaN(port) || port < 1 || port > 65535) {
      issues.push(`无效的后端端口: ${config.VITE_BACKEND_PORT}`);
    }
  }
  
  if (config.VITE_FRONTEND_PORT) {
    const port = parseInt(config.VITE_FRONTEND_PORT);
    if (isNaN(port) || port < 1 || port > 65535) {
      issues.push(`无效的前端端口: ${config.VITE_FRONTEND_PORT}`);
    }
  }
  
  // 验证URL格式
  if (config.VITE_BACKEND_URL) {
    try {
      new URL(config.VITE_BACKEND_URL);
    } catch (e) {
      issues.push(`无效的后端URL: ${config.VITE_BACKEND_URL}`);
    }
  }
  
  // 显示问题
  if (issues.length > 0) {
    log(`\n❌ 发现 ${issues.length} 个问题:`, 'red');
    issues.forEach(issue => {
      log(`   • ${issue}`, 'red');
    });
    return false;
  } else {
    log(`✅ 配置检查通过`, 'green');
    return true;
  }
}

function checkPackageJson() {
  log(`\n🔍 检查 package.json 脚本`, 'blue');
  
  const packagePath = path.join(__dirname, '../package.json');
  if (!fs.existsSync(packagePath)) {
    log(`❌ package.json 不存在`, 'red');
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const scripts = packageJson.scripts || {};
  
  // 检查脚本中的硬编码
  const hardcodedPatterns = [
    /\d+\.\d+\.\d+\.\d+:\d+/,  // IP:端口
    /localhost:\d+/,           // localhost:端口 (除了常见的)
    /:5173(?!\s|$)/,          // 非标准的5173端口使用
    /:8000(?!\s|$)/,          // 非标准的8000端口使用
  ];
  
  let hasIssues = false;
  
  Object.entries(scripts).forEach(([scriptName, scriptContent]) => {
    hardcodedPatterns.forEach(pattern => {
      if (pattern.test(scriptContent)) {
        log(`⚠️ 脚本 "${scriptName}" 可能包含硬编码地址: ${scriptContent}`, 'yellow');
        hasIssues = true;
      }
    });
  });
  
  if (!hasIssues) {
    log(`✅ package.json 脚本检查通过`, 'green');
  }
  
  return !hasIssues;
}

function checkViteConfig() {
  log(`\n🔍 检查 vite.config.ts`, 'blue');
  
  const viteConfigPath = path.join(__dirname, '../vite.config.ts');
  if (!fs.existsSync(viteConfigPath)) {
    log(`❌ vite.config.ts 不存在`, 'red');
    return false;
  }
  
  const content = fs.readFileSync(viteConfigPath, 'utf8');
  
  // 检查是否使用环境变量
  if (content.includes('process.env.VITE_BACKEND_URL')) {
    log(`✅ Vite配置使用环境变量`, 'green');
  } else {
    log(`⚠️ Vite配置可能未使用环境变量`, 'yellow');
  }
  
  // 检查硬编码
  const hardcodedPatterns = [
    /\d+\.\d+\.\d+\.\d+:\d+/,  // IP:端口
  ];
  
  let hasHardcoded = false;
  hardcodedPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      log(`⚠️ Vite配置可能包含硬编码地址`, 'yellow');
      hasHardcoded = true;
    }
  });
  
  if (!hasHardcoded) {
    log(`✅ Vite配置检查通过`, 'green');
  }
  
  return !hasHardcoded;
}

function main() {
  log('🚀 前端配置检查', 'bright');
  log('='.repeat(50), 'blue');
  
  const results = [];
  
  // 检查环境配置文件
  const envFiles = [
    ['.env.development', '开发环境配置'],
    ['.env.production', '生产环境配置'],
    ['.env.hotreload', 'Docker热重载配置'],
    ['.env.example', '配置示例文件'],
    ['.env.lan.example', '局域网配置示例'],
  ];
  
  envFiles.forEach(([file, desc]) => {
    const filePath = path.join(__dirname, '..', file);
    results.push(checkEnvFile(filePath, desc));
  });
  
  // 检查其他配置
  results.push(checkPackageJson());
  results.push(checkViteConfig());
  
  // 总结
  log(`\n📊 检查结果总结`, 'bright');
  log('='.repeat(50), 'blue');
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  if (passed === total) {
    log(`🎉 所有检查通过 (${passed}/${total})`, 'green');
    log(`\n💡 建议:`, 'blue');
    log(`   1. 运行 npm run dev 启动开发服务器`, 'reset');
    log(`   2. 检查后端是否运行在配置的端口`, 'reset');
    log(`   3. 验证API请求是否正常`, 'reset');
  } else {
    log(`⚠️ 部分检查失败 (${passed}/${total})`, 'yellow');
    log(`\n🔧 建议修复:`, 'blue');
    log(`   1. 检查并修复上述配置问题`, 'reset');
    log(`   2. 参考 .env.example 文件`, 'reset');
    log(`   3. 确保前后端配置一致`, 'reset');
  }
  
  process.exit(passed === total ? 0 : 1);
}

// 直接运行主函数
main();
