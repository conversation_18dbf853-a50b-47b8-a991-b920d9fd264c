﻿import onnxruntime
import numpy as np
from PIL import Image, UnidentifiedImageError
import io
import base64
import os
import logging
from django.conf import settings

# Configure logger for this module
logger = logging.getLogger(__name__)

class AIRestoredOnnxRuntimePredictor:
    """
    A predictor class for AI image restoration using an ONNX model.
    It handles model loading, image preprocessing, inference, and postprocessing.
    """
    def __init__(self, model_path=None, **kwargs):
        """
        Initializes the AI Restored ONNX Runtime Predictor.

        Args:
            model_path (str, optional): Path to the ONNX model file. 
                                        If None, uses a default path.
            **kwargs: Additional keyword arguments.
        """
        self.model_path = model_path
        self.target_size = (256, 256) # Default, will be updated if model provides different info

        if self.model_path is None:
            # Use os.path.join for platform-independent path construction
            self.model_path = os.path.join(
                settings.BASE_DIR,
                "models", # This needs to match the actual location relative to BASE_DIR
                "system_models",
                "ai_restored",
                "AI_Restorer_NCHW_1x1x256x256_V1.0.1.1.onnx"
            )
            logger.info(f"Model path not provided, using default: {self.model_path}")

        if not os.path.exists(self.model_path):
            error_msg = f"ONNX model file not found at: {self.model_path}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        try:
            # 统一使用CPU推理
            providers = ['CPUExecutionProvider']
            self.session = onnxruntime.InferenceSession(self.model_path, providers=providers)
            
            # Get model input details
            input_meta = self.session.get_inputs()[0]
            self.input_name = input_meta.name
            # Expected shape format: [batch_size, channels, height, width]
            # e.g., [None, 1, 256, 256] or [1, 1, 256, 256]
            input_shape = input_meta.shape 
            if len(input_shape) == 4 and isinstance(input_shape[2], int) and isinstance(input_shape[3], int):
                 self.target_size = (input_shape[3], input_shape[2]) # (width, height) for PIL, (height, width) for np
            else:
                logger.warning(f"Could not determine target size from model input shape {input_shape}. Using default {self.target_size}.")


            # Get model output details
            self.output_name = self.session.get_outputs()[0].name
            
            logger.info(f"ONNX model '{self.model_path}' loaded successfully.")
            logger.info(f"Model Input Name: {self.input_name}, Shape: {input_shape}")
            logger.info(f"Model Output Name: {self.output_name}, Shape: {self.session.get_outputs()[0].shape}")
            logger.info(f"Using target size for preprocessing: H={self.target_size[1]}, W={self.target_size[0]}")
            logger.info("Using CPU for inference.")

        except Exception as e:
            logger.error(f"Error loading ONNX model or creating session: {e}", exc_info=True)
            raise

    def _preprocess_image(self, image_input):
        """
        Loads, validates, and preprocesses the input image to match the ONNX model's requirements.

        Args:
            image_input: Can be a file path (str), image bytes, or a PIL.Image object.

        Returns:
            tuple: (preprocessed_image_np, original_mode, original_size)
                   - preprocessed_image_np: NumPy array (float32) ready for model input.
                   - original_mode: Mode of the original PIL image.
                   - original_size: (width, height) of the original PIL image.
        Raises:
            ValueError: If the image_input type is invalid.
            FileNotFoundError: If image_input is a path and the file doesn't exist.
            UnidentifiedImageError: If PIL cannot identify or open the image.
        """
        img = None
        original_mode = None
        original_size = None

        if isinstance(image_input, str): # File path
            if not os.path.exists(image_input):
                raise FileNotFoundError(f"Input image file not found: {image_input}")
            try:
                img = Image.open(image_input)
            except UnidentifiedImageError:
                raise UnidentifiedImageError(f"Cannot identify image file: {image_input}")
        elif isinstance(image_input, bytes): # Image bytes
            try:
                img = Image.open(io.BytesIO(image_input))
            except UnidentifiedImageError:
                raise UnidentifiedImageError("Cannot identify image from bytes.")
        elif isinstance(image_input, Image.Image): # PIL Image object
            img = image_input
        else:
            raise ValueError("Invalid image_input type. Must be a file path, bytes, or PIL.Image object.")

        original_mode = img.mode
        original_size = img.size # (width, height)
        logger.debug(f"Original image loaded: size={original_size}, mode={original_mode}")

        # 1. Convert to grayscale if not already (model expects 1 channel)
        if img.mode != 'L':
            img = img.convert('L')
            logger.debug(f"Converted image to grayscale ('L' mode).")

        # 2. Resize to model's target input size (height, width for np; width, height for PIL)
        # self.target_size is (width, height) from model, PIL resize also expects (width, height)
        if img.size != self.target_size:
            img = img.resize(self.target_size, Image.Resampling.LANCZOS)
            logger.debug(f"Resized image to {self.target_size}.")

        # 3. Convert to NumPy array
        img_np = np.array(img, dtype=np.float32) # Shape: (H, W)

        # 4. Normalize pixel values from [0, 255] to [0.0, 1.0]
        img_np = img_np / 255.0

        # 5. Add batch and channel dimensions: (H, W) -> (1, 1, H, W) for NCHW format
        img_np = np.expand_dims(img_np, axis=0)  # (1, H, W)
        img_np = np.expand_dims(img_np, axis=0)  # (1, 1, H, W)

        logger.debug(f"Preprocessed image: shape={img_np.shape}, dtype={img_np.dtype}")
        return img_np.astype(np.float32), original_mode, original_size

    def _postprocess_output(self, output_tensor, original_mode='L', original_size=None):
        """
        Postprocesses the model's output tensor back into a PIL Image.

        Args:
            output_tensor (np.ndarray): The raw output tensor from the model.
            original_mode (str, optional): The mode of the original image. Defaults to 'L'.
            original_size (tuple, optional): The (width, height) of the original image. 
                                             Currently unused for resizing back.

        Returns:
            PIL.Image: The restored image.
        """
        # 1. Squeeze batch and channel dimensions (1, 1, H, W) -> (H, W)
        if output_tensor.ndim == 4:
            output_image_np = output_tensor.squeeze(axis=(0,1)) # More robust squeeze
        elif output_tensor.ndim == 2: # Already (H,W)
            output_image_np = output_tensor
        else:
            logger.warning(f"Unexpected output tensor dimension: {output_tensor.ndim}. Squeezing default.")
            output_image_np = output_tensor.squeeze()


        # 2. De-normalize: pixel values from [0.0, 1.0] back to [0, 255]
        #    Clip to ensure values are within the valid range.
        output_image_np = np.clip(output_image_np * 255.0, 0, 255)

        # 3. Convert to uint8
        output_image_np = output_image_np.astype(np.uint8)

        # 4. Create PIL Image object (mode 'L' for grayscale)
        restored_image = Image.fromarray(output_image_np, mode='L')
        logger.debug(f"Postprocessed image: size={restored_image.size}, mode={restored_image.mode}")
        
        # Optionally, resize back to original_size if needed, though this might degrade quality
        # if original_size is not None and restored_image.size != original_size:
        #     logger.debug(f"Resizing restored image back to original size: {original_size}")
        #     restored_image = restored_image.resize(original_size, Image.Resampling.LANCZOS)

        return restored_image

    def predict_restore_image(self, image_input, output_format='PNG'):
        """
        Performs AI image restoration on the input image.

        Args:
            image_input: Image data (file path, bytes, or PIL.Image object).
            output_format (str, optional): Desired output image format ('PNG' or 'JPEG'). 
                                          Defaults to 'PNG'.

        Returns:
            dict: A dictionary containing the status, message, and (if successful)
                  the base64 encoded restored image, along with size information.
                  Example:
                  {
                      'status': 'success'/'error',
                      'message': 'Descriptive message',
                      'image_base64': 'base64_string_of_image', (if success)
                      'original_size': [width, height],
                      'restored_size': [width, height], (size of the model output image)
                      'output_format': 'PNG'/'JPEG'
                  }
        """
        result = {
            'status': 'error',
            'message': 'An unexpected error occurred.',
            'image_base64': None,
            'original_size': None,
            'restored_size': None,
            'output_format': output_format.upper()
        }

        try:
            logger.info(f"Starting image restoration process. Output format: {output_format}")
            
            # 1. Preprocess the image
            preprocessed_tensor, original_mode, original_size = self._preprocess_image(image_input)
            result['original_size'] = list(original_size) # [width, height]

            # 2. Run inference
            logger.debug(f"Running inference with input tensor shape: {preprocessed_tensor.shape}")
            inference_results = self.session.run([self.output_name], {self.input_name: preprocessed_tensor})
            output_tensor = inference_results[0]
            logger.debug(f"Inference complete. Output tensor shape: {output_tensor.shape}")

            # 3. Postprocess the output
            restored_image_pil = self._postprocess_output(output_tensor, original_mode, original_size)
            result['restored_size'] = list(restored_image_pil.size) # [width, height]

            # 4. Encode to Base64
            buffered = io.BytesIO()
            if output_format.upper() not in ['PNG', 'JPEG']:
                logger.warning(f"Invalid output_format '{output_format}', defaulting to PNG.")
                output_format = 'PNG'
                result['output_format'] = 'PNG'
            
            img_save_format = 'JPEG' if output_format.upper() == 'JPEG' else 'PNG'
            restored_image_pil.save(buffered, format=img_save_format)
            base64_image_string = base64.b64encode(buffered.getvalue()).decode('utf-8')
            
            result['status'] = 'success'
            result['message'] = 'Image restoration successful.'
            result['image_base64'] = base64_image_string
            logger.info("Image restoration completed successfully.")

        except FileNotFoundError as e:
            logger.error(f"File not found during restoration: {e}", exc_info=True)
            result['message'] = str(e)
        except UnidentifiedImageError as e:
            logger.error(f"Cannot identify image during restoration: {e}", exc_info=True)
            result['message'] = str(e)
        except ValueError as e: # For invalid input types or other value errors
            logger.error(f"Value error during restoration: {e}", exc_info=True)
            result['message'] = str(e)
        except onnxruntime.capi.onnxruntime_pybind11_state.RuntimeException as e:
            logger.error(f"ONNX Runtime error during inference: {e}", exc_info=True)
            result['message'] = f"ONNX Runtime inference failed: {e}"
        except Exception as e:
            logger.error(f"An unexpected error occurred during image restoration: {e}", exc_info=True)
            result['message'] = f"An unexpected error occurred: {e}"
            
        return result

# Example usage (can be commented out or removed for Django integration)
if __name__ == "__main__":
    # Configure basic logging for standalone testing
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # --- Create a dummy settings.BASE_DIR for testing if Django settings are not available ---
    # This is a hack for standalone execution. In Django, settings.BASE_DIR will be set.
    class DummySettings:
        BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Should point to Backend_Django
                                                                                # if this script is in Backend_Django/vision_app/
    
    if not hasattr(settings, 'BASE_DIR'):
        settings.configure(BASE_DIR=DummySettings.BASE_DIR)
        logger.info(f"Using dummy BASE_DIR for standalone test: {settings.BASE_DIR}")
    
    # Ensure the dummy model path exists or provide a real one for testing.
    # For this test, let's try to create a dummy ONNX model file if one doesn't exist
    # Note: Creating a valid ONNX model programmatically is complex.
    # This example assumes a model exists at the default path or you provide one.
    
    # Path to a test image (replace with your actual image path)
    # Create a dummy image for testing if it doesn't exist
    test_image_name = "test_restore_input.png"
    test_image_path = os.path.join(settings.BASE_DIR, "vision_app", test_image_name) # Place it inside vision_app for testing
    
    if not os.path.exists(test_image_path):
        try:
            logger.info(f"Creating dummy test image at: {test_image_path}")
            dummy_img = Image.new('RGB', (512, 384), color = (128, 128, 128)) # A gray image
            dummy_img.save(test_image_path, "PNG")
        except Exception as e:
            logger.error(f"Could not create dummy test image: {e}")
            # sys.exit(1) # Exit if cannot create test image and model needs it.

    # Default model path that the predictor will try to load
    # Make sure this path is correct relative to your BASE_DIR or provide an absolute one.
    default_model_dir_relative_to_base = os.path.join(
        "models", "system_models", "ai_restored"
    )
    default_model_full_dir = os.path.join(settings.BASE_DIR, default_model_dir_relative_to_base)
    
    if not os.path.exists(default_model_full_dir):
        os.makedirs(default_model_full_dir)
        logger.info(f"Created directory for dummy model: {default_model_full_dir}")

    # Check if the model file exists, if not, this test will fail unless you have a valid model
    # For now, we assume the predictor's __init__ will handle FileNotFoundError if model is missing.
    # model_file_path_for_test = os.path.join(default_model_full_dir, "AI_Restorer_NCHW_1x1x256x256_V1.0.1.1.onnx")
    # if not os.path.exists(model_file_path_for_test):
    #    logger.warning(f"Test model {model_file_path_for_test} not found. Predictor initialization might fail.")
    #    # You might want to create a placeholder empty file for path testing, but it won't be a valid ONNX model.
    #    # open(model_file_path_for_test, 'a').close()


    logger.info("--- Testing AIRestoredOnnxRuntimePredictor ---")
    try:
        # Initialize predictor (will use default model path)
        # If default model doesn't exist, this will raise FileNotFoundError.
        predictor = AIRestoredOnnxRuntimePredictor(model_path=None)
        
        if os.path.exists(test_image_path):
            logger.info(f"Testing with image path: {test_image_path}")
            result_path = predictor.predict_restore_image(test_image_path, output_format='PNG')
            
            if result_path['status'] == 'success':
                logger.info("Prediction from path successful.")
                logger.info(f"Original size: {result_path['original_size']}")
                logger.info(f"Restored_size: {result_path['restored_size']}")
                # Save the base64 image to a file for verification
                output_image_data = base64.b64decode(result_path['image_base64'])
                output_file_path = os.path.join(settings.BASE_DIR, "vision_app", "test_restored_output.png")
                with open(output_file_path, "wb") as f:
                    f.write(output_image_data)
                logger.info(f"Restored image saved to: {output_file_path}")
            else:
                logger.error(f"Prediction from path failed: {result_path['message']}")

            # Test with image bytes
            logger.info(f"\nTesting with image bytes from: {test_image_path}")
            with open(test_image_path, "rb") as f_bytes:
                image_bytes_content = f_bytes.read()
            
            result_bytes = predictor.predict_restore_image(image_bytes_content, output_format='JPEG')
            if result_bytes['status'] == 'success':
                logger.info("Prediction from bytes successful.")
                # Save for verification
                output_image_data_bytes = base64.b64decode(result_bytes['image_base64'])
                output_file_path_bytes = os.path.join(settings.BASE_DIR, "vision_app", "test_restored_output_bytes.jpg")
                with open(output_file_path_bytes, "wb") as f_b:
                    f_b.write(output_image_data_bytes)
                logger.info(f"Restored image from bytes saved to: {output_file_path_bytes}")
            else:
                logger.error(f"Prediction from bytes failed: {result_bytes['message']}")
        else:
            logger.warning(f"Test image {test_image_path} not found. Skipping image content tests.")

    except FileNotFoundError as fnf_e:
        logger.error(f"Initialization or test failed: Model or test image file not found. {fnf_e}")
        logger.error("Please ensure the ONNX model file exists at the expected path for testing,")
        logger.error(f"e.g., {os.path.join(default_model_full_dir, 'AI_Restorer_NCHW_1x1x256x256_V1.0.1.1.onnx')}")
        logger.error("and a test image (e.g., test_restore_input.png) is available.")
    except Exception as e:
        logger.error(f"An error occurred during the test: {e}", exc_info=True)