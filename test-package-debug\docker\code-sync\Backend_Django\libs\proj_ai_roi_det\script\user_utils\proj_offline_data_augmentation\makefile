ROOT := $(shell pwd)

SUBDIR := $(ROOT)

SRC_PATH := $(SUBDIR)/ctools

BIN := $(SUBDIR)/bin
OUTPUT := $(SUBDIR)/build

INCS := $(foreach dir,$(SUBDIR),-I$(dir))

# AIEngine
INCS += -I$(SRC_PATH)/Common/include

# OpenCV
INCS += $(shell pkg-config --cflags opencv4)
LIBS += $(shell pkg-config --libs opencv4)

all: create_output_directory rotate_and_crop_exe sliding_window_segment_exe

create_output_directory:
	@mkdir -p $(BIN)

rotate_and_crop_exe:
	@g++ $(SRC_PATH)/ocr_rec_preprocess.cpp -o $(BIN)/ocr_rec_prep_rotate_and_crop $(LIBS) $(INCS) -O3 -DPREP_TYPE_ROTATE_AND_CROP
	@echo compile $@ complete!

sliding_window_segment_exe:
	@g++ $(SRC_PATH)/ocr_rec_preprocess.cpp -o $(BIN)/ocr_rec_prep_sliding_window_segment $(LIBS) $(INCS) -O3 -DPREP_TYPE_SLIDING_WINDOW_SEGMENT
	@echo compile $@ complete!

.PHONY : clean install

clean:
	@echo try to clean...
	@rm -r $(OUTPUT)
	@echo complete!