import cv2
import math
import random
import onnxruntime
import numpy as np
import time

# --- 核心算法函数 (从 run.py 迁移和重构) ---

def grid_nms(scores, grid=4, threshold=8):
    # 对特征点做NMS - 与run.py保持完全一致的实现
    c, h, w = scores.shape
    
    # 验证grid参数的有效性
    total_elements = c * h * w
    grid_elements = grid * grid
    
    # 检查是否能够整除
    if total_elements % grid_elements != 0:
        print(f"⚠️ grid_nms警告: 无法将shape {scores.shape} 重塑为 (-1, {grid}, {grid})")
        print(f"   总元素数: {total_elements}, 网格元素数: {grid_elements}")
        print(f"   建议的grid值: {[i for i in [1, 2, 4, 8, 16] if (total_elements % (i*i)) == 0]}")
        
        # 回退到较小的grid值
        for fallback_grid in [4, 2, 1]:
            if total_elements % (fallback_grid * fallback_grid) == 0:
                print(f"   使用回退值: grid={fallback_grid}")
                grid = fallback_grid
                break
        else:
            # 如果都不行，直接返回原始scores
            print(f"   无法回退，返回原始scores")
            return scores
    
    scores_grid = scores.reshape(-1, grid, grid)
    max_grid = []
    # 遍历每个grid
    for grid_block in scores_grid:
        max_value = np.max(grid_block)
        if max_value < threshold:
            max_grid.append(np.zeros_like(grid_block))
            continue

        grid_with_max = np.where(grid_block == max_value, grid_block, 0)
        max_grid.append(grid_with_max)

    socres_nms = np.array(max_grid).reshape(c, h, w)
    return socres_nms

def extract_keypoints(scores):
    keypoints = []
    for i in range(scores.shape[1]):
        for j in range(scores.shape[2]):
            if scores[0, i, j] > 0:
                keypoints.append({"score": scores[0, i, j], "coord": (j, i)})
    return keypoints

def extract_descriptors(keypoints, descriptors, img_shape):
    n, c, h, w = descriptors.shape
    # 逐个特征点提取描述子
    for keypoint in keypoints:
        # 获取关键点在原图中的坐标
        x_orig, y_orig = keypoint["coord"]
        
        # 将原图坐标映射到描述子特征图坐标
        # 注意：img_shape是(1, 1, H, W)格式，所以img_shape[2]是高度，img_shape[3]是宽度
        x_desc = (x_orig / img_shape[3]) * w  # 映射到描述子宽度
        y_desc = (y_orig / img_shape[2]) * h  # 映射到描述子高度
        
        # 确保坐标在有效范围内
        x_desc = max(0, min(x_desc, w - 1))
        y_desc = max(0, min(y_desc, h - 1))
        
        # 双线性插值的四个邻近点
        x0 = int(math.floor(x_desc))
        y0 = int(math.floor(y_desc))
        x1 = min(x0 + 1, w - 1)
        y1 = min(y0 + 1, h - 1)
        
        # 计算插值权重
        dx = x_desc - x0
        dy = y_desc - y0
        
        # 获取四个邻近点的描述子
        d00 = descriptors[0, :, y0, x0]  # 左上
        d01 = descriptors[0, :, y0, x1]  # 右上  
        d10 = descriptors[0, :, y1, x0]  # 左下
        d11 = descriptors[0, :, y1, x1]  # 右下
        
        # 双线性插值
        descriptor = (1 - dx) * (1 - dy) * d00 + \
                    dx * (1 - dy) * d01 + \
                    (1 - dx) * dy * d10 + \
                    dx * dy * d11
        
        # L2归一化
        norm = np.linalg.norm(descriptor)
        if norm > 0:
            descriptor = descriptor / norm
        
        keypoint["descriptor"] = descriptor

def debug_descriptors(keypoints, name=""):
    """
    调试描述子质量
    """
    if not keypoints:
        print(f"{name} 没有关键点")
        return
        
    descriptors_list = []
    for kp in keypoints:
        if "descriptor" in kp:
            descriptors_list.append(kp["descriptor"])
    
    if not descriptors_list:
        print(f"{name} 没有描述子")
        return
        
    descriptors_array = np.array(descriptors_list)
    
    print(f"\n=== {name} 描述子统计 ===")
    print(f"描述子数量: {len(descriptors_list)}")
    print(f"描述子维度: {descriptors_array.shape[1]}")
    print(f"描述子范围: [{descriptors_array.min():.4f}, {descriptors_array.max():.4f}]")
    print(f"描述子均值: {descriptors_array.mean():.4f}")
    print(f"描述子标准差: {descriptors_array.std():.4f}")
    
    # 检查是否有全零描述子
    zero_descriptors = np.sum(np.sum(np.abs(descriptors_array), axis=1) < 1e-6)
    print(f"全零描述子数量: {zero_descriptors}")
    
    # 检查L2范数
    norms = np.linalg.norm(descriptors_array, axis=1)
    print(f"L2范数范围: [{norms.min():.4f}, {norms.max():.4f}]")
    print(f"L2范数均值: {norms.mean():.4f}")
    
    # 计算描述子之间的相似性
    if len(descriptors_list) > 1:
        # 计算前10个描述子之间的余弦相似度
        n_sample = min(10, len(descriptors_list))
        sample_desc = descriptors_array[:n_sample]
        similarities = np.dot(sample_desc, sample_desc.T)
        # 去除对角线元素
        similarities = similarities[np.triu_indices_from(similarities, k=1)]
        print(f"描述子相似度范围: [{similarities.min():.4f}, {similarities.max():.4f}]")
        print(f"描述子相似度均值: {similarities.mean():.4f}")

def match_descriptors(keypoints1, keypoints2, max_distance=0.7):
    """
    基于描述子进行特征匹配
    Args:
        keypoints1: 第一张图的关键点列表
        keypoints2: 第二张图的关键点列表  
        max_distance: 最大匹配距离阈值
    Returns:
        matches: 匹配对列表，每个元素为(idx1, idx2, distance)
    """
    matches = []
    
    for i, kp1 in enumerate(keypoints1):
        if "descriptor" not in kp1:
            continue
            
        best_distance = float('inf')
        second_best_distance = float('inf')
        best_match_idx = -1
        
        for j, kp2 in enumerate(keypoints2):
            if "descriptor" not in kp2:
                continue
                
            # 计算欧氏距离
            distance = np.linalg.norm(kp1["descriptor"] - kp2["descriptor"])
            
            if distance < best_distance:
                second_best_distance = best_distance
                best_distance = distance
                best_match_idx = j
            elif distance < second_best_distance:
                second_best_distance = distance
        
        # 添加匹配对
        if best_match_idx != -1 and best_distance < max_distance:
            matches.append((i, best_match_idx, best_distance))
    
    return matches

def filter_matches(matches, ratio_threshold=0.8):
    """
    使用Lowe's ratio test过滤匹配
    Args:
        matches: 原始匹配列表
        ratio_threshold: 比值阈值
    Returns:
        filtered_matches: 过滤后的匹配列表
    """
    # 按第一个关键点索引分组
    matches_by_kp1 = {}
    for match in matches:
        idx1, idx2, distance = match
        if idx2 not in matches_by_kp1:
            matches_by_kp1[idx2] = []
        matches_by_kp1[idx2].append(match)
    
    filtered_matches = []
    for idx1, kp2_matches in matches_by_kp1.items():
        if len(kp2_matches) < 2:
            # 如果只有一个匹配，直接添加
            filtered_matches.extend(kp2_matches)
            continue
            
        # 按距离排序
        kp2_matches.sort(key=lambda x: x[2])
        best_match = kp2_matches[0]
        second_best_match = kp2_matches[1]
        
        # Lowe's ratio test
        if best_match[2] / second_best_match[2] < ratio_threshold:
            filtered_matches.append(best_match)
    
    return filtered_matches

def ransac_homography(matches, keypoints1, keypoints2, threshold=3.0, max_iterations=1000, min_inliers=4):
    """
    使用RANSAC算法估计单应性矩阵并过滤匹配
    Args:
        matches: 匹配对列表
        keypoints1: 第一张图的关键点
        keypoints2: 第二张图的关键点
        threshold: 内点阈值（像素）
        max_iterations: 最大迭代次数
        min_inliers: 最小内点数量
    Returns:
        best_homography: 最佳单应性矩阵
        inlier_matches: 内点匹配
    """
    if len(matches) < min_inliers:
        return None, []
    
    best_homography = None
    best_inliers = []
    max_inlier_count = 0
    
    # 提取匹配点坐标
    pts1 = np.array([keypoints1[match[0]]["coord"] for match in matches], dtype=np.float32)
    pts2 = np.array([keypoints2[match[1]]["coord"] for match in matches], dtype=np.float32)
    
    for iteration in range(max_iterations):
        # 随机选择4个点
        if len(matches) < 4:
            break
            
        random_indices = random.sample(range(len(matches)), 4)
        sample_pts1 = pts1[random_indices]
        sample_pts2 = pts2[random_indices]
        
        # 计算单应性矩阵
        try:
            H = cv2.getPerspectiveTransform(sample_pts1, sample_pts2)
        except:
            continue
        
        # 计算所有点的重投影误差
        pts1_homogeneous = np.column_stack([pts1, np.ones(len(pts1))])
        projected_pts = H @ pts1_homogeneous.T
        projected_pts = projected_pts[:2] / projected_pts[2]  # 归一化
        projected_pts = projected_pts.T
        
        # 计算误差
        errors = np.linalg.norm(pts2 - projected_pts, axis=1)
        inliers = errors < threshold
        inlier_count = np.sum(inliers)
        
        # 更新最佳结果
        if inlier_count > max_inlier_count:
            max_inlier_count = inlier_count
            best_homography = H
            best_inliers = [matches[i] for i in range(len(matches)) if inliers[i]]
    
    return best_homography, best_inliers

def transform_rotated_box(box_points, homography):
    """
    使用单应性矩阵变换旋转框
    Args:
        box_points: 旋转框的四个角点 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
        homography: 单应性矩阵
    Returns:
        transformed_points: 变换后的四个角点
    """
    if homography is None:
        return None
    
    # 转换为齐次坐标
    points = np.array(box_points, dtype=np.float32)
    points_homogeneous = np.column_stack([points, np.ones(len(points))])
    
    # 应用单应性变换
    transformed = homography @ points_homogeneous.T
    transformed = transformed[:2] / transformed[2]  # 归一化
    transformed = transformed.T
    
    return transformed.astype(int)

def draw_rotated_box(img, box_points, color=(0, 255, 0), thickness=2):
    """
    在图像上绘制旋转框
    Args:
        img: 图像
        box_points: 四个角点
        color: 颜色
        thickness: 线条粗细
    """
    if box_points is None:
        return img
    
    # 绘制四条边
    for i in range(4):
        pt1 = tuple(box_points[i])
        pt2 = tuple(box_points[(i + 1) % 4])
        cv2.line(img, pt1, pt2, color, thickness)
    
    # 绘制角点
    for pt in box_points:
        cv2.circle(img, tuple(pt), 5, color, -1)
    
    return img

def interactive_box_selection(image_path):
    """
    交互式选择旋转框
    Args:
        image_path: 图像路径
    Returns:
        box_points: 四个角点坐标
    """
    img = cv2.imread(image_path)
    clone = img.copy()
    points = []
    
    def mouse_callback(event, x, y, flags, param):
        nonlocal points, img
        
        if event == cv2.EVENT_LBUTTONDOWN:
            if len(points) < 4:
                points.append([x, y])
                cv2.circle(img, (x, y), 5, (0, 0, 255), -1)
                cv2.putText(img, str(len(points)), (x+10, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                
                # 如果有两个点，画线
                if len(points) >= 2:
                    cv2.line(img, tuple(points[-2]), tuple(points[-1]), (0, 255, 0), 2)
                
                # 如果有四个点，闭合框
                if len(points) == 4:
                    cv2.line(img, tuple(points[-1]), tuple(points[0]), (0, 255, 0), 2)
                
                cv2.imshow("Select Box Points", img)
        
        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右键重置
            points = []
            img = clone.copy()
            cv2.imshow("Select Box Points", img)
    
    cv2.namedWindow("Select Box Points")
    cv2.setMouseCallback("Select Box Points", mouse_callback)
    cv2.imshow("Select Box Points", img)
    
    print("请在图像上点击四个点来定义旋转框（按顺序点击）")
    print("右键重置，ESC键完成选择")
    
    while True:
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC键
            break
        elif key == ord('r'):  # R键重置
            points = []
            img = clone.copy()
            cv2.imshow("Select Box Points", img)
    
    cv2.destroyAllWindows()
    
    if len(points) == 4:
        return np.array(points)
    else:
        print("未选择完整的四个点")
        return None

def visualize_matches(img1_path, img2_path, keypoints1, keypoints2, matches):
    """
    可视化匹配结果
    Args:
        img1_path: 第一张图片路径
        img2_path: 第二张图片路径
        keypoints1: 第一张图的关键点
        keypoints2: 第二张图的关键点
        matches: 匹配对列表
    """
    # 读取图片
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    # 创建拼接图片
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    
    # 统一高度
    if h1 > h2:
        img2 = cv2.resize(img2, (int(w2 * h1 / h2), h1))
    elif h2 > h1:
        img1 = cv2.resize(img1, (int(w1 * h2 / h1), h2))
    
    h = max(h1, h2)
    combined_img = np.zeros((h, w1 + w2, 3), dtype=np.uint8)
    combined_img[:img1.shape[0], :img1.shape[1]] = img1
    combined_img[:img2.shape[0], img1.shape[1]:] = img2
    
    # 绘制匹配线
    colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
    
    for i, (idx1, idx2, distance) in enumerate(matches):
        color = colors[i % len(colors)]
        
        # 获取关键点坐标
        pt1 = keypoints1[idx1]["coord"]
        pt2 = keypoints2[idx2]["coord"]
        pt2_shifted = (pt2[0] + img1.shape[1], pt2[1])  # 第二张图的坐标需要偏移
        
        # 绘制关键点
        cv2.circle(combined_img, pt1, 5, color, 2)
        cv2.circle(combined_img, pt2_shifted, 5, color, 2)
        
        # 绘制连接线
        cv2.line(combined_img, pt1, pt2_shifted, color, 2)
        
        # 显示匹配距离
        mid_point = ((pt1[0] + pt2_shifted[0]) // 2, (pt1[1] + pt2_shifted[1]) // 2)
        cv2.putText(combined_img, f"{distance:.3f}", mid_point, 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    # 显示结果
    cv2.imshow(f"Matches ({len(matches)} found)", combined_img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return combined_img


# --- 预测器类 ---

class FeatureMatchingPredictor:
    def __init__(self, model_path):
        """
        初始化预测器，加载ONNX模型。
        Args:
            model_path (str): SuperPoint ONNX模型的路径。
        """
        try:
            self.session = onnxruntime.InferenceSession(model_path)
            self.input_name = self.session.get_inputs()[0].name
            _, _, self.h, self.w = self.session.get_inputs()[0].shape
        except Exception as e:
            raise IOError(f"无法加载ONNX模型: {model_path}") from e

    def _preprocess(self, image):
        """
        对输入图像进行预处理。
        Args:
            image (np.ndarray): BGR格式的图像。
        Returns:
            tuple: (预处理后的图像, 原始高, 原始宽)
        """
        orig_h, orig_w = image.shape[:2]
        
        # 转换为灰度图并缩放
        gray_img = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        resized_img = cv2.resize(gray_img, (self.w, self.h), interpolation=cv2.INTER_NEAREST)
        
        # 归一化并调整维度
        tensor_img = resized_img.reshape(1, 1, self.h, self.w).astype(np.float32) / 255.0
        
        return tensor_img, orig_h, orig_w

    def _extract_features(self, image, n_keypoints=500):
        """
        从单个图像中提取关键点和描述子。
        Args:
            image (np.ndarray): BGR格式的图像。
            n_keypoints (int): 要提取的关键点数量。
        Returns:
            list: 包含关键点信息（坐标、分数、描述子）的字典列表。
        """
        tensor_img, orig_h, orig_w = self._preprocess(image)
        
        # ONNX推理
        scores, descriptors = self.session.run(None, {self.input_name: tensor_img})
        
        # 后处理
        scores = scores * 255  # 恢复分数范围
        scores_nms = grid_nms(scores)
        keypoints = extract_keypoints(scores_nms)
        
        # 按分数排序并选择top-N
        keypoints = sorted(keypoints, key=lambda x: x["score"], reverse=True)[:n_keypoints]
        
        # 提取描述子
        extract_descriptors(keypoints, descriptors, tensor_img.shape)
        
        # 坐标映射回原图
        w_ratio = orig_w / self.w
        h_ratio = orig_h / self.h
        for kp in keypoints:
            kp["coord"] = (int(kp["coord"][0] * w_ratio), int(kp["coord"][1] * h_ratio))
            
        return keypoints

    def _filter_matches_by_roi(self, matches, roi):
        """
        根据ROI区域过滤匹配点，只保留模板图像ROI区域内的匹配点
        
        Args:
            matches (list): 匹配点列表
            roi (dict): ROI区域 {"x": x, "y": y, "width": w, "height": h}
        
        Returns:
            list: 过滤后的匹配点列表
        """
        if not roi or not matches:
            return matches
        
        x, y, w, h = roi['x'], roi['y'], roi['width'], roi['height']
        
        filtered_matches = []
        for match in matches:
            pt1 = match["point1"]  # 模板图像中的点
            
            # 检查点是否在ROI区域内
            if x <= pt1[0] <= x + w and y <= pt1[1] <= y + h:
                filtered_matches.append(match)
        
        return filtered_matches

    def predict(self, template_image, target_image, template_roi=None, 
                keypoints_count=100, nms_grid_size=4, nms_threshold=8.0/255.0,
                match_ratio_threshold=0.8, min_match_count=4, ransac_threshold=5.0):
        """
        执行特征匹配预测，与run.py逻辑完全一致
        
        重要：与run.py保持一致，始终对完整图像进行特征提取，ROI仅用于后处理阶段
        
        Args:
            template_image (np.ndarray): 模板图像 (BGR格式) - 使用完整图像
            target_image (np.ndarray): 目标图像 (BGR格式) - 使用完整图像
            template_roi (dict, optional): 模板图像的感兴趣区域，仅用于结果后处理和框变换
            keypoints_count (int): 每张图提取的关键点数量
            nms_grid_size (int): NMS网格大小
            nms_threshold (float): NMS阈值
            match_ratio_threshold (float): Lowe's ratio test阈值
            min_match_count (int): 最小匹配点数量
            ransac_threshold (float): RANSAC阈值
        
        Returns:
            dict: 匹配结果
        """
        start_time = time.time()
        
        try:
            # 🔥 关键修正：始终对完整图像进行特征提取（与run.py一致）
            print(f"\n🏠 按run.py流程进行特征匹配")
            print(f"模板图像尺寸: {template_image.shape}")
            print(f"目标图像尺寸: {target_image.shape}")
            print(f"ROI区域: {template_roi} (仅用于后处理)")
            
            # 1. 对完整图像进行特征提取 (与run.py完全一致)
            print(f"🔥 直接使用完整图像进行特征提取")
            
            # 使用自身的_extract_features方法
            keypoints1 = self._extract_features(template_image, keypoints_count)
            keypoints2 = self._extract_features(target_image, keypoints_count)
            
            print(f"模板图关键点数量: {len(keypoints1)}")
            print(f"目标图关键点数量: {len(keypoints2)}")
            
            # 简单匹配逻辑
            matches = match_descriptors(keypoints1, keypoints2, max_distance=0.7)
            filtered_matches = filter_matches(matches, ratio_threshold=match_ratio_threshold)
            
            # RANSAC几何校验
            homography, inlier_matches = ransac_homography(
                filtered_matches, keypoints1, keypoints2, 
                threshold=ransac_threshold, min_inliers=min_match_count
            )
            
            # 准备匹配结果
            final_matches = []
            if inlier_matches:
                for idx1, idx2, distance in inlier_matches:
                    pt1 = keypoints1[idx1]["coord"]
                    pt2 = keypoints2[idx2]["coord"]
                    final_matches.append({
                        "template_point": pt1, 
                        "target_point": pt2, 
                        "distance": distance
                    })
            
            success = homography is not None
            match_count = len(inlier_matches) if inlier_matches else 0
            
            # 2. ROI后处理（可选）
            # 注意：这里可以添加基于ROI的结果过滤逻辑，但不影响核心匹配
            if template_roi and success:
                print(f"\n📍 ROI后处理 (目前暂未实现，保持原始匹配结果)")
                # 可以在这里添加基于ROI的匹配点过滤
                # 例如：只保留ROI区域内的匹配点
                pass
            
            # 3. 计算处理时间
            processing_time = time.time() - start_time
            
            return {
                "status": "success" if success else "failure",
                "message": "特征匹配完成" if success else "特征匹配失败",
                "data": {
                    "match_count": match_count,
                    "matches": final_matches,
                    "homography": homography.tolist() if homography is not None else None,
                    "template_keypoints_count": len(keypoints1),
                    "target_keypoints_count": len(keypoints2),
                    "processing_time": processing_time
                }
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"特征匹配预测失败: {str(e)}"
            print(f"❌ {error_msg}")
            
            return {
                "status": "error",
                "message": error_msg,
                "data": {
                    "match_count": 0,
                    "matches": [],
                    "homography": None,
                    "template_keypoints_count": 0,
                    "target_keypoints_count": 0,
                    "processing_time": processing_time
                }
            }

    def inference(self, image_path, N=100):
        """
        与run.py中的inference函数保持一致的接口
        Args:
            image_path (str): 图像路径
            N (int): 要提取的关键点数量
        Returns:
            list: 包含关键点信息的字典列表
        """
        # 读取图片
        img_color = cv2.imread(image_path)
        if img_color is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        return self._extract_features(img_color, N)


# --- 后端API兼容类 ---

class ModelBasedFeatureMatcher:
    """
    后端API兼容类，封装FeatureMatchingPredictor以提供与现有API一致的接口。
    
    这个类是为了保持与现有后端API的向后兼容性而创建的包装器。
    """
    
    def __init__(self, model_path):
        """
        初始化模型特征匹配器
        Args:
            model_path (str): SuperPoint ONNX模型的路径
        """
        self.predictor = FeatureMatchingPredictor(model_path)
        self.model_path = model_path
    
    def predict(self, template_image, target_image, template_roi=None, 
                keypoints_count=100, nms_grid_size=4, nms_threshold=8.0/255.0,
                match_ratio_threshold=0.8, min_match_count=4, ransac_threshold=5.0):
        """
        执行特征匹配预测，与run.py逻辑完全一致
        
        重要：与run.py保持一致，始终对完整图像进行特征提取，ROI仅用于后处理阶段
        
        Args:
            template_image (np.ndarray): 模板图像 (BGR格式) - 使用完整图像
            target_image (np.ndarray): 目标图像 (BGR格式) - 使用完整图像
            template_roi (dict, optional): 模板图像的感兴趣区域，仅用于结果后处理和框变换
            keypoints_count (int): 每张图提取的关键点数量
            nms_grid_size (int): NMS网格大小
            nms_threshold (float): NMS阈值
            match_ratio_threshold (float): Lowe's ratio test阈值
            min_match_count (int): 最小匹配点数量
            ransac_threshold (float): RANSAC阈值
        
        Returns:
            dict: 匹配结果
        """
        start_time = time.time()
        
        try:
            # 🔥 关键修正：始终对完整图像进行特征提取（与run.py一致）
            print(f"\n🏠 按run.py流程进行特征匹配 (ModelBasedFeatureMatcher)")
            print(f"模板图像尺寸: {template_image.shape}")
            print(f"目标图像尺寸: {target_image.shape}")
            print(f"ROI区域: {template_roi} (仅用于后处理)")
            
            # 1. 对完整图像进行特征提取 (与run.py完全一致)
            result = self._predict_with_params(
                template_image,      # 完整模板图像
                target_image,        # 完整目标图像
                keypoints_count, nms_grid_size, nms_threshold,
                match_ratio_threshold, min_match_count, ransac_threshold
            )
            
            # 2. ROI后处理（可选）
            # 注意：这里添加基于ROI的结果过滤逻辑，但不影响核心匹配算法
            if template_roi and result["success"]:
                print(f"\n📍 ROI后处理 - 过滤ROI区域外的匹配点")
                original_count = result["match_count"]
                
                # 过滤ROI区域内的匹配点
                filtered_matches = self._filter_matches_by_roi(result["matches"], template_roi)
                
                # 更新结果
                result["match_count"] = len(filtered_matches)
                result["matches"] = filtered_matches
                
                print(f"ROI过滤: {original_count} -> {len(filtered_matches)} 个匹配点")
                
                # 如果过滤后匹配点太少，可能需要重新计算单应性矩阵
                if len(filtered_matches) < min_match_count:
                    print(f"⚠️  ROI过滤后匹配点不足 ({len(filtered_matches)} < {min_match_count})")
                    result["success"] = False
                    result["homography"] = None
            
            # 3. 计算处理时间
            processing_time = time.time() - start_time
            
            return {
                "status": "success" if result["success"] else "failure",
                "message": "特征匹配完成" if result["success"] else "特征匹配失败",
                "data": {
                    "match_count": result["match_count"],
                    "matches": result["matches"],
                    "homography": result["homography"],
                    "template_keypoints_count": result["keypoints1_count"],
                    "target_keypoints_count": result["keypoints2_count"],
                    "processing_time": processing_time
                }
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"特征匹配预测失败: {str(e)}"
            print(f"❌ {error_msg}")
            
            return {
                "status": "error",
                "message": error_msg,
                "data": {
                    "match_count": 0,
                    "matches": [],
                    "homography": None,
                    "template_keypoints_count": 0,
                    "target_keypoints_count": 0,
                    "processing_time": processing_time
                }
            }

    def _predict_with_params(self, template_image, target_image, keypoints_count, 
                            nms_grid_size, nms_threshold, match_ratio_threshold, 
                            min_match_count, ransac_threshold):
        """
        带参数的预测方法，实现与run.py完全一致的多策略匹配逻辑
        """
        # 1. 提取特征 (使用自定义参数)
        keypoints1 = self._extract_features_with_params(template_image, keypoints_count, nms_grid_size, nms_threshold)
        keypoints2 = self._extract_features_with_params(target_image, keypoints_count, nms_grid_size, nms_threshold)
        
        # 2. 实现run.py的多策略匹配逻辑
        print(f"模板图关键点数量: {len(keypoints1)}")
        print(f"目标图关键点数量: {len(keypoints2)}")
        
        # 定义与run.py完全一致的匹配参数
        match_params = [
            {"max_distance": 0.3, "ratio_threshold": 0.8, "name": "严格匹配"},
            {"max_distance": 0.5, "ratio_threshold": 0.8, "name": "中等匹配"},
            {"max_distance": 0.7, "ratio_threshold": 0.9, "name": "宽松匹配"},
        ]
        
        best_matches = []
        best_param_name = ""
        
        for param in match_params:
            print(f"\n--- {param['name']} ---")
            matches = match_descriptors(keypoints1, keypoints2, 
                                      max_distance=param['max_distance'])
            print(f"初始匹配数量: {len(matches)}")
            
            filtered_matches = filter_matches(matches, ratio_threshold=param['ratio_threshold'])
            print(f"过滤后匹配数量: {len(filtered_matches)}")
            
            if len(filtered_matches) > len(best_matches):
                best_matches = filtered_matches
                best_param_name = param['name']
        
        print(f"\n最佳匹配参数: {best_param_name}")
        print(f"最佳匹配数量: {len(best_matches)}")
        
        # 3. RANSAC几何校验 (使用自定义参数)
        print(f"\n=== RANSAC过滤 ===")
        homography, inlier_matches = ransac_homography(
            best_matches, keypoints1, keypoints2, 
            threshold=ransac_threshold, min_inliers=min_match_count,
            max_iterations=2000  # 使用与run.py相同的迭代次数
        )
        
        if homography is not None:
            print(f"RANSAC后匹配数量: {len(inlier_matches)}")
            print(f"内点比例: {len(inlier_matches)/len(best_matches)*100:.1f}%")
            
            # 计算RANSAC匹配质量统计
            if inlier_matches:
                ransac_distances = [match[2] for match in inlier_matches]
                print(f"RANSAC匹配质量统计:")
                print(f"平均距离: {np.mean(ransac_distances):.4f}")
                print(f"距离标准差: {np.std(ransac_distances):.4f}")
                print(f"最小距离: {min(ransac_distances):.4f}")
                print(f"最大距离: {max(ransac_distances):.4f}")
        else:
            print("RANSAC失败，无法估计单应性矩阵")
            print("可能原因：匹配点太少或匹配质量不好")
        
        # 4. 准备输出
        final_matches = []
        if inlier_matches:
            for idx1, idx2, distance in inlier_matches:
                pt1 = keypoints1[idx1]["coord"]
                pt2 = keypoints2[idx2]["coord"]
                final_matches.append({"point1": pt1, "point2": pt2, "distance": distance})

        result = {
            "success": homography is not None,
            "homography": homography.tolist() if homography is not None else None,
            "match_count": len(inlier_matches),
            "matches": final_matches,
            "original_box": None,
            "transformed_box": None,
            "keypoints1_count": len(keypoints1),
            "keypoints2_count": len(keypoints2),
        }
        
        return result
    
    def _extract_features_with_params(self, image, n_keypoints, nms_grid_size, nms_threshold):
        """
        使用自定义参数提取特征，与run.py保持完全一致
        """
        tensor_img, orig_h, orig_w = self.predictor._preprocess(image)
        
        # ONNX推理
        scores, descriptors = self.predictor.session.run(None, {self.predictor.input_name: tensor_img})
        
        # 后处理 (使用自定义参数，与run.py完全一致)
        scores = scores * 255  # 恢复分数范围
        
        # 确保NMS阈值处理与run.py一致
        # 如果nms_threshold < 1，说明是归一化值，需要乘以255
        actual_threshold = nms_threshold * 255 if nms_threshold < 1 else nms_threshold
        
        scores_nms = grid_nms(scores, grid=nms_grid_size, threshold=actual_threshold)
        keypoints = extract_keypoints(scores_nms)
        
        # 按分数排序并选择top-N
        keypoints = sorted(keypoints, key=lambda x: x["score"], reverse=True)[:n_keypoints]
        
        # 提取描述子
        extract_descriptors(keypoints, descriptors, tensor_img.shape)
        
        # 坐标映射回原图
        w_ratio = orig_w / self.predictor.w
        h_ratio = orig_h / self.predictor.h
        for kp in keypoints:
            kp["coord"] = (int(kp["coord"][0] * w_ratio), int(kp["coord"][1] * h_ratio))
            
        return keypoints

    def _filter_matches_by_roi(self, matches, roi):
        """
        根据ROI区域过滤匹配点，只保留模板图像ROI区域内的匹配点
        
        Args:
            matches (list): 匹配点列表
            roi (dict): ROI区域 {"x": x, "y": y, "width": w, "height": h}
        
        Returns:
            list: 过滤后的匹配点列表
        """
        if not roi or not matches:
            return matches
        
        x, y, w, h = roi['x'], roi['y'], roi['width'], roi['height']
        
        filtered_matches = []
        for match in matches:
            pt1 = match["point1"]  # 模板图像中的点
            
            # 检查点是否在ROI区域内
            if x <= pt1[0] <= x + w and y <= pt1[1] <= y + h:
                filtered_matches.append(match)
        
        return filtered_matches
