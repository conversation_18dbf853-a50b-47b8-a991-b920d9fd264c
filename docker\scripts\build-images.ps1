# AI Vision App Docker Build Script
# 统一构建脚本，支持开发和部署环境

param(
    [switch]$BackendOnly,
    [switch]$FrontendOnly,
    [switch]$NoCache,
    [switch]$DeploymentMode
)

Write-Host "=== AI Vision App Docker Build Script ===" -ForegroundColor Cyan
Write-Host ""

# 检测运行环境
$currentDir = Get-Location
$isDeploymentPackage = (Test-Path "code-sync") -or ($currentDir.Path -like "*server-deploy-package*")
if ($isDeploymentPackage) {
    Write-Host "📦 Detected deployment package environment" -ForegroundColor Yellow
    $DeploymentMode = $true
} else {
    Write-Host "🔧 Detected development environment" -ForegroundColor Yellow
}

# Check Docker environment
Write-Host "Checking Docker environment..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✓ Docker version: $dockerVersion" -ForegroundColor Green
}
catch {
    Write-Host "✗ Docker not installed or not running" -ForegroundColor Red
    exit 1
}

# 设置项目路径
if ($DeploymentMode) {
    # 部署包模式：当前目录应该是 docker 目录
    $projectRoot = Split-Path -Parent $PWD
    Write-Host "Deployment package root: $projectRoot" -ForegroundColor Cyan

    if (-not (Test-Path "code-sync\Frontend") -or -not (Test-Path "code-sync\Backend_Django")) {
        Write-Host "✗ Deployment package structure is incorrect" -ForegroundColor Red
        Write-Host "Expected: code-sync/Frontend and code-sync/Backend_Django" -ForegroundColor Yellow
        exit 1
    }
} else {
    # 开发模式：从脚本位置推断项目根目录
    $projectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
    Write-Host "Development project root: $projectRoot" -ForegroundColor Cyan

    if (-not (Test-Path "$projectRoot\Frontend") -or -not (Test-Path "$projectRoot\Backend_Django")) {
        Write-Host "✗ Development project structure is incorrect" -ForegroundColor Red
        exit 1
    }

    # 切换到 docker 目录
    Set-Location "$projectRoot\docker"
}

Write-Host "✓ Project directory check passed" -ForegroundColor Green

# Set build arguments
$buildArgs = @()
if ($NoCache) {
    $buildArgs += "--no-cache"
}

# 设置构建上下文和Dockerfile路径
if ($DeploymentMode) {
    # 部署模式：从上级目录构建，包含 code-sync 目录
    $buildContext = ".."
    $frontendDockerfile = "docker/Dockerfile.frontend"
    $backendDockerfile = "docker/Dockerfile.backend"
} else {
    # 开发模式：从当前目录构建
    $buildContext = ".."
    $frontendDockerfile = "Dockerfile.frontend"
    $backendDockerfile = "Dockerfile.backend"
}

# Build frontend
if (-not $BackendOnly) {
    Write-Host ""
    Write-Host "=== Building Frontend Image ===" -ForegroundColor Yellow

    if ($DeploymentMode) {
        $frontendCmd = "docker build " + ($buildArgs -join " ") + " -f $frontendDockerfile -t web_ai_vision_app-frontend:latest $buildContext"
        Write-Host "Build context: $buildContext (deployment mode)" -ForegroundColor DarkCyan
    } else {
        $frontendCmd = "docker build " + ($buildArgs -join " ") + " -f $frontendDockerfile -t web_ai_vision_app-frontend:latest $buildContext"
        Write-Host "Build context: $buildContext (development mode)" -ForegroundColor DarkCyan
    }

    # 确保构建命令格式正确
    if ($buildArgs.Count -eq 0) {
        $frontendCmd = "docker build -f $frontendDockerfile -t web_ai_vision_app-frontend:latest $buildContext"
    } else {
        $frontendCmd = "docker build " + ($buildArgs -join " ") + " -f $frontendDockerfile -t web_ai_vision_app-frontend:latest $buildContext"
    }

    Write-Host "Executing: $frontendCmd" -ForegroundColor Gray

    $frontendStart = Get-Date

    if ($DeploymentMode) {
        # 部署模式：切换到上级目录执行
        Push-Location ..
        try {
            Invoke-Expression $frontendCmd
            $frontendResult = $LASTEXITCODE
        } finally {
            Pop-Location
        }
    } else {
        # 开发模式：直接执行
        Invoke-Expression $frontendCmd
        $frontendResult = $LASTEXITCODE
    }

    $frontendEnd = Get-Date

    if ($frontendResult -eq 0) {
        $frontendDuration = ($frontendEnd - $frontendStart).TotalSeconds
        Write-Host "✅ Frontend image built successfully (Duration: $([math]::Round($frontendDuration, 1))s)" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Frontend image build failed" -ForegroundColor Red
        exit 1
    }
}

# Build backend
if (-not $FrontendOnly) {
    Write-Host ""
    Write-Host "=== Building Backend Image ===" -ForegroundColor Yellow

    if ($DeploymentMode) {
        $backendCmd = "docker build " + ($buildArgs -join " ") + " -f $backendDockerfile -t web_ai_vision_app-backend:latest $buildContext"
        Write-Host "Build context: $buildContext (deployment mode)" -ForegroundColor DarkCyan
    } else {
        $backendCmd = "docker build " + ($buildArgs -join " ") + " -f $backendDockerfile -t web_ai_vision_app-backend:latest $buildContext"
        Write-Host "Build context: $buildContext (development mode)" -ForegroundColor DarkCyan
    }

    # 确保构建命令格式正确
    if ($buildArgs.Count -eq 0) {
        $backendCmd = "docker build -f $backendDockerfile -t web_ai_vision_app-backend:latest $buildContext"
    } else {
        $backendCmd = "docker build " + ($buildArgs -join " ") + " -f $backendDockerfile -t web_ai_vision_app-backend:latest $buildContext"
    }

    Write-Host "Executing: $backendCmd" -ForegroundColor Gray

    $backendStart = Get-Date

    if ($DeploymentMode) {
        # 部署模式：切换到上级目录执行
        Push-Location ..
        try {
            Invoke-Expression $backendCmd
            $backendResult = $LASTEXITCODE
        } finally {
            Pop-Location
        }
    } else {
        # 开发模式：直接执行
        Invoke-Expression $backendCmd
        $backendResult = $LASTEXITCODE
    }

    $backendEnd = Get-Date

    if ($backendResult -eq 0) {
        $backendDuration = ($backendEnd - $backendStart).TotalSeconds
        Write-Host "✅ Backend image built successfully (Duration: $([math]::Round($backendDuration, 1))s)" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Backend image build failed" -ForegroundColor Red
        exit 1
    }
}

# Show build results
Write-Host ""
Write-Host "=== Build Completed ===" -ForegroundColor Green
Write-Host "Docker images:" -ForegroundColor Cyan
docker images | Select-String "web_ai_vision_app"

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Yellow
if ($DeploymentMode) {
    Write-Host "🚀 Start services: .\scripts\deploy-hotreload.ps1 -Mode start" -ForegroundColor Cyan
    Write-Host "📊 Check status: docker ps" -ForegroundColor Cyan
    Write-Host "📋 View logs: docker logs ai-vision-backend-hotreload" -ForegroundColor Cyan
} else {
    Write-Host "🚀 Start services: .\scripts\start-container.ps1" -ForegroundColor Cyan
    Write-Host "🔥 Hot reload: .\scripts\deploy-hotreload.ps1 -Mode start" -ForegroundColor Cyan
    Write-Host "📊 Or use: docker-compose up -d" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "✅ Build script completed!" -ForegroundColor Green
