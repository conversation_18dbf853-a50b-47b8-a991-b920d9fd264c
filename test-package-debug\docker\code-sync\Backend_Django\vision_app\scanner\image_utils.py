import numpy as np
import cv2
from io import BytesIO

def nv12_to_jpeg(nv12_data, width, height):
    """
    将NV12格式的图像数据高效地转换为JPEG格式。
    :param nv12_data: NV12格式的原始图像字节数据
    :param width: 图像宽度
    :param height: 图像高度
    :return: JPEG格式的图像数据 (BytesIO)，如果失败则返回 None
    """
    try:
        # NV12 图像数据的大小应为 width * height * 1.5
        expected_size = width * height * 3 // 2
        if len(nv12_data) != expected_size:
            raise ValueError(f"NV12 data size mismatch: expected {expected_size}, got {len(nv12_data)}")

        # 将字节数据转换为 NumPy 数组
        yuv_data = np.frombuffer(nv12_data, dtype=np.uint8)
        
        # 重塑为 OpenCV 处理 NV12 所需的形状（高度为原始高度的1.5倍）
        yuv_image = yuv_data.reshape((height * 3 // 2, width))
        
        # 使用 OpenCV 将 NV12 颜色空间转换为 BGR
        bgr_image = cv2.cvtColor(yuv_image, cv2.COLOR_YUV2BGR_NV12)
        
        # 将 BGR 图像编码为高质量的 JPEG
        is_success, jpeg_buffer = cv2.imencode('.jpg', bgr_image, [int(cv2.IMWRITE_JPEG_QUALITY), 90])
        
        if not is_success:
            raise RuntimeError("Failed to encode image to JPEG")
            
        return BytesIO(jpeg_buffer.tobytes())
        
    except Exception as e:
        print(f"NV12 to JPEG conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return None