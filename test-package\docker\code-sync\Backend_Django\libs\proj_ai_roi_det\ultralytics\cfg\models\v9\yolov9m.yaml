# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv9m object detection model. For Usage examples see https://docs.ultralytics.com/models/yolov9
# 603 layers, 20216160 parameters, 77.9 GFLOPs

# Parameters
nc: 80 # number of classes

# GELAN backbone
backbone:
  - [-1, 1, Conv, [32, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [64, 3, 2]] # 1-P2/4
  - [-1, 1, RepNCSPELAN4, [128, 128, 64, 1]] # 2
  - [-1, 1, AConv, [240]] # 3-P3/8
  - [-1, 1, RepNCSPELAN4, [240, 240, 120, 1]] # 4
  - [-1, 1, AConv, [360]] # 5-P4/16
  - [-1, 1, Rep<PERSON><PERSON>ELAN4, [360, 360, 180, 1]] # 6
  - [-1, 1, AConv, [480]] # 7-P5/32
  - [-1, 1, RepNC<PERSON>ELAN4, [480, 480, 240, 1]] # 8
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON>, [480, 240]] # 9

head:
  - [-1, 1, nn.Up<PERSON><PERSON>, [None, 2, "nearest"]]
  - [[-1, 6], 1, Con<PERSON>, [1]] # cat backbone P4
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON>4, [360, 360, 180, 1]] # 12

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 4], 1, Concat, [1]] # cat backbone P3
  - [-1, 1, RepNCSPELAN4, [240, 240, 120, 1]] # 15

  - [-1, 1, AConv, [180]]
  - [[-1, 12], 1, Concat, [1]] # cat head P4
  - [-1, 1, RepNCSPELAN4, [360, 360, 180, 1]] # 18 (P4/16-medium)

  - [-1, 1, AConv, [240]]
  - [[-1, 9], 1, Concat, [1]] # cat head P5
  - [-1, 1, RepNCSPELAN4, [480, 480, 240, 1]] # 21 (P5/32-large)

  - [[15, 18, 21], 1, Detect, [nc]] # Detect(P3, P4, P5)
