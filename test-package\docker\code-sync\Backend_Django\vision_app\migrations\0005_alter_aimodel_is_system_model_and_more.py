# Generated by Django 5.2.1 on 2025-05-13 02:29

import django.utils.timezone
import vision_app.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0004_aimodel_is_system_model_aimodel_uploaded_at"),
    ]

    operations = [
        migrations.AlterField(
            model_name="aimodel",
            name="is_system_model",
            field=models.BooleanField(
                default=False, help_text="Is this a system-provided model?"
            ),
        ),
        migrations.AlterField(
            model_name="aimodel",
            name="model_file",
            field=models.FileField(
                blank=True,
                help_text="Path for custom models relative to MEDIA_ROOT/<model_type>/, or just filename for system models.",
                max_length=255,
                null=True,
                upload_to=vision_app.models.get_model_upload_path,
            ),
        ),
        migrations.AlterField(
            model_name="aimodel",
            name="name",
            field=models.<PERSON>r<PERSON>ield(max_length=255),
        ),
        migrations.Alter<PERSON>ield(
            model_name="aimodel",
            name="uploaded_at",
            field=models.DateTimeField(
                default=django.utils.timezone.now,
                help_text="Timestamp when the model record was created or last updated.",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="aimodel",
            unique_together={("name", "model_type", "is_system_model")},
        ),
    ]
