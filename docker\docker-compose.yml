version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    image: web_ai_vision_app-backend:latest
    container_name: ai-vision-backend
    restart: unless-stopped
    environment:
      - DJANGO_SETTINGS_MODULE=backend_project.settings_docker
      - PYTHONPATH=/app
      # 配置pip使用国内镜像
      - PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
      - PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
      # 局域网访问配置
      - DJANGO_ALLOWED_HOSTS=*
      - CORS_ALLOW_ALL_ORIGINS=True
      # 调试选项
      - PYTHONUNBUFFERED=1
      - DJANGO_DEBUG=True
      # 网络配置统一管理
      - BACKEND_PORT=${BACKEND_PORT:-8000}
      - FRONTEND_DEV_PORT=${FRONTEND_DEV_PORT:-5173}
      - FRONTEND_PROD_PORT=${FRONTEND_PROD_PORT:-8080}
      - LAN_IPS=${LAN_IPS:-}
      - CORS_ORIGINS=${CORS_ORIGINS:-}
      # 扫码枪配置
      - SCANNER_ENABLED=${SCANNER_ENABLED:-false}
      - SCANNER_DEFAULT_IP=${SCANNER_DEFAULT_IP:-}
      - SCANNER_DEFAULT_PORT=${SCANNER_DEFAULT_PORT:-8080}
      - SCANNER_TIMEOUT=${SCANNER_TIMEOUT:-30}
    volumes:
      # 持久化数据库目录
      - ../data/db:/app/db
      # 持久化模型文件
      - ../data/models:/app/models
      # 持久化媒体文件
      - ../data/media:/app/media
      # 持久化日志
      - ../data/logs:/app/logs
    ports:
      - "${BACKEND_PORT:-8000}:${BACKEND_PORT:-8000}"
    networks:
      - ai-vision-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/vision/models/"]
      interval: 30s
      timeout: 30s
      retries: 5
      start_period: 120s

  # 前端服务
  frontend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.frontend
    image: web_ai_vision_app-frontend:latest
    container_name: ai-vision-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PROD_PORT:-8080}:80"
    depends_on:
      - backend
    networks:
      - ai-vision-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  ai-vision-network:
    driver: bridge

volumes:
  # 定义命名卷用于数据持久化
  db_data:
    driver: local
  models_data:
    driver: local
  media_data:
    driver: local
  logs_data:
    driver: local
