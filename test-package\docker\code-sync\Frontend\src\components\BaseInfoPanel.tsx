import React from 'react';
import { Card } from 'antd';

interface BaseInfoPanelProps {
  title: string;
  children: React.ReactNode;
}

const BaseInfoPanel: React.FC<BaseInfoPanelProps> = ({ title, children }) => {
  return (
    <Card
      title={title}
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
      styles={{
        header: {
          minHeight: '32px',
          padding: '4px 12px',
          fontSize: '14px',
          fontWeight: 'bold',
          backgroundColor: '#fafafa',
          textAlign: 'center',
        },
        body: {
          flex: 1,
          padding: '8px 12px',
          overflow: 'auto',
        }
      }}
    >
      {children}
    </Card>
  );
};

export default BaseInfoPanel;