# Package for Server Deployment Script
param(
    [string]$OutputPath = "server-deploy-package",
    [switch]$IncludeImages,
    [switch]$Compress
)

# Set PowerShell encoding for Chinese environment
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Encoding compatibility function
function Write-FileWithEncoding {
    param(
        [string]$FilePath,
        [string]$Content
    )

    try {
        $utf8WithBom = New-Object System.Text.UTF8Encoding($true)
        [System.IO.File]::WriteAllText($FilePath, $Content, $utf8WithBom)
        return $true
    } catch {
        try {
            $Content | Out-File -FilePath $FilePath -Encoding UTF8
            return $true
        } catch {
            $Content | Out-File -FilePath $FilePath -Encoding Default
            return $false
        }
    }
}

Write-Host "=== Package for Server Deployment ===" -ForegroundColor Green
Write-Host ""

# Get project paths
$scriptPath = $PSScriptRoot
$dockerPath = Split-Path -Parent $scriptPath
$projectRoot = Split-Path -Parent $dockerPath
$packageDir = Join-Path $projectRoot $OutputPath

Write-Host "Script path: $scriptPath" -ForegroundColor Gray
Write-Host "Docker path: $dockerPath" -ForegroundColor Gray
Write-Host "Project root: $projectRoot" -ForegroundColor Gray
Write-Host "Package directory: $packageDir" -ForegroundColor Gray
Write-Host ""

# Verify paths
if (-not (Test-Path $projectRoot)) {
    Write-Host "Error: Project root not found: $projectRoot" -ForegroundColor Red
    exit 1
}

$backendPath = Join-Path $projectRoot "Backend_Django"
$frontendPath = Join-Path $projectRoot "Frontend"

if (-not (Test-Path $backendPath)) {
    Write-Host "Error: Backend_Django not found: $backendPath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $frontendPath)) {
    Write-Host "Error: Frontend not found: $frontendPath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $dockerPath)) {
    Write-Host "Error: docker directory not found: $dockerPath" -ForegroundColor Red
    exit 1
}

# Create package directory
if (Test-Path $packageDir) {
    Remove-Item $packageDir -Recurse -Force
}
New-Item -ItemType Directory -Path $packageDir -Force | Out-Null

# Create directory structure
$dockerDir = Join-Path $packageDir "docker"
$scriptsDir = Join-Path $dockerDir "scripts"
$imagesDir = Join-Path $dockerDir "docker-images"

New-Item -ItemType Directory -Path $dockerDir -Force | Out-Null
New-Item -ItemType Directory -Path $scriptsDir -Force | Out-Null
New-Item -ItemType Directory -Path $imagesDir -Force | Out-Null

Write-Host "Step 1: Copying Docker configuration files..." -ForegroundColor Yellow

# Copy Docker configuration files
$dockerFiles = @(
    "docker-compose.yml",
    "docker-compose.hotreload.yml",
    "docker-compose.simple.yml",
    "nginx.conf",
    "nginx-hotreload.conf",
    "Dockerfile.frontend",
    "Dockerfile.backend",
    "pip.conf",
    "daemon.json"
)

foreach ($file in $dockerFiles) {
    $source = Join-Path $dockerPath $file
    $target = Join-Path $dockerDir $file

    if (Test-Path $source) {
        Copy-Item $source $target
        Write-Host "  OK $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Step 1.1: Modifying Dockerfiles for deployment structure..." -ForegroundColor Yellow

# 确保dockerDir变量存在
if (-not $dockerDir) {
    $dockerDir = Join-Path $packageDir "docker"
}

# 修改Dockerfile.frontend以适应部署包结构
$frontendDockerfilePath = Join-Path $dockerDir "Dockerfile.frontend"
Write-Host "  DEBUG: Frontend Dockerfile path: $frontendDockerfilePath" -ForegroundColor Cyan

if ($frontendDockerfilePath -and (Test-Path $frontendDockerfilePath)) {
    try {
        $frontendDockerfileContent = Get-Content $frontendDockerfilePath -Raw

        # 修改路径以匹配部署包结构
        $frontendDockerfileContent = $frontendDockerfileContent -replace 'COPY Frontend/package\*\.json \./', 'COPY code-sync/Frontend/package*.json ./'
        $frontendDockerfileContent = $frontendDockerfileContent -replace 'COPY Frontend/ \.', 'COPY code-sync/Frontend/ .'

        # 写入修改后的内容
        Set-Content -Path $frontendDockerfilePath -Value $frontendDockerfileContent -Encoding UTF8
        Write-Host "  OK Modified Dockerfile.frontend for deployment structure" -ForegroundColor Green
    } catch {
        Write-Host "  ERROR: Failed to modify Dockerfile.frontend: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ERROR: Dockerfile.frontend not found at $frontendDockerfilePath" -ForegroundColor Red
}

# 修改Dockerfile.backend以适应部署包结构
$backendDockerfilePath = Join-Path $dockerDir "Dockerfile.backend"
Write-Host "  DEBUG: Backend Dockerfile path: $backendDockerfilePath" -ForegroundColor Cyan

if ($backendDockerfilePath -and (Test-Path $backendDockerfilePath)) {
    try {
        $backendDockerfileContent = Get-Content $backendDockerfilePath -Raw

        # 修改路径以匹配部署包结构
        $backendDockerfileContent = $backendDockerfileContent -replace 'COPY Backend_Django/requirements_docker\.txt /app/requirements\.txt', 'COPY code-sync/Backend_Django/requirements_docker.txt /app/requirements.txt'
        $backendDockerfileContent = $backendDockerfileContent -replace 'COPY Backend_Django/ /app/', 'COPY code-sync/Backend_Django/ /app/'

        # 写入修改后的内容
        Set-Content -Path $backendDockerfilePath -Value $backendDockerfileContent -Encoding UTF8
        Write-Host "  OK Modified Dockerfile.backend for deployment structure" -ForegroundColor Green
    } catch {
        Write-Host "  ERROR: Failed to modify Dockerfile.backend: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ERROR: Dockerfile.backend not found at $backendDockerfilePath" -ForegroundColor Red
}

Write-Host ""
Write-Host "Step 2: Copying scripts..." -ForegroundColor Yellow

# Copy script files
$scriptFiles = @(
    "build-images.ps1",
    "deploy-hotreload.ps1",
    "enable-auto-start.ps1",
    "start-container.ps1",
    "stop-container.ps1",
    "export-images.ps1",
    "import-images.ps1",
    "package-for-server.ps1",
    "setup-code-sharing-simple.ps1",
    "sync-from-dev.ps1",
    "setup-firewall.bat",
    "generate-nginx-config.ps1",
    "health.json",
    "start-backend.sh"
)

foreach ($file in $scriptFiles) {
    $source = Join-Path $dockerPath "scripts\$file"
    $target = Join-Path $scriptsDir $file

    if (Test-Path $source) {
        Copy-Item $source $target
        Write-Host "  OK $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Step 2.1: Creating deployment-specific build script..." -ForegroundColor Yellow

# 创建适合部署包的构建脚本
$deploymentBuildScript = @"
# AI Vision App - Deployment Build Script
# This script is specifically designed for the deployment package structure

param(
    [switch]`$NoCache = `$false,
    [switch]`$BackendOnly = `$false,
    [switch]`$FrontendOnly = `$false
)

Write-Host "=== AI Vision App Deployment Build Script ===" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the correct directory
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "Error: Please run this script from the docker directory" -ForegroundColor Red
    Write-Host "Current directory: `$(Get-Location)" -ForegroundColor Yellow
    exit 1
}

# Build parameters
`$buildArgs = @()
if (`$NoCache) {
    `$buildArgs += "--no-cache"
}

# Build backend image
if (-not `$FrontendOnly) {
    Write-Host "=== Building Backend Image ===" -ForegroundColor Yellow
    `$backendCmd = "docker build " + (`$buildArgs -join " ") + " -f Dockerfile.backend -t web_ai_vision_app-backend:latest ."
    Write-Host "Executing: `$backendCmd" -ForegroundColor Cyan
    Invoke-Expression `$backendCmd

    if (`$LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend image build successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Backend image build failed" -ForegroundColor Red
        exit 1
    }
}

# Build frontend image
if (-not `$BackendOnly) {
    Write-Host ""
    Write-Host "=== Building Frontend Image ===" -ForegroundColor Yellow
    `$frontendCmd = "docker build " + (`$buildArgs -join " ") + " -f Dockerfile.frontend -t web_ai_vision_app-frontend:latest ."
    Write-Host "Executing: `$frontendCmd" -ForegroundColor Cyan
    Invoke-Expression `$frontendCmd

    if (`$LASTEXITCODE -eq 0) {
        Write-Host "✅ Frontend image build successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Frontend image build failed" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "=== Build Complete ===" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Start services: .\scripts\deploy-hotreload.ps1 -Mode start" -ForegroundColor White
Write-Host "2. Check status: docker ps" -ForegroundColor White
Write-Host "3. View logs: docker logs ai-vision-backend-hotreload" -ForegroundColor White
"@

$deploymentBuildScriptPath = Join-Path $scriptsDir "build-images-deployment.ps1"
Set-Content -Path $deploymentBuildScriptPath -Value $deploymentBuildScript -Encoding UTF8
Write-Host "  OK Created deployment-specific build script" -ForegroundColor Green

# Copy check scripts
Write-Host ""
Write-Host "Step 2.1: Copying diagnostic scripts..." -ForegroundColor Yellow

$checkDir = Join-Path $scriptsDir "check"
New-Item -ItemType Directory -Path $checkDir -Force | Out-Null

$checkFiles = @(
    "check-docker-env.ps1",
    "check-lan-access.ps1",
    "check-network.ps1"
)

foreach ($file in $checkFiles) {
    $source = Join-Path $dockerPath "scripts\check\$file"
    $target = Join-Path $checkDir $file

    if (Test-Path $source) {
        Copy-Item $source $target
        Write-Host "  OK check/$file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING check/$file" -ForegroundColor Red
    }
}

# Copy Docker images if needed
if ($IncludeImages) {
    Write-Host ""
    Write-Host "Step 3: Copying Docker images..." -ForegroundColor Yellow

    $imagePaths = @(
        $dockerPath,                                    # docker/ 目录本身
        (Join-Path $dockerPath "docker-images"),       # docker/docker-images/ 目录
        (Join-Path $dockerPath "images"),              # docker/images/ 目录
        (Join-Path $projectRoot "docker-images")       # 项目根目录的 docker-images/ 目录
    )

    $imageFiles = @()
    foreach ($path in $imagePaths) {
        if (Test-Path $path) {
            # 查找 .tar 和 .tar.gz 格式的镜像文件
            $tarFiles = Get-ChildItem $path -Filter "*.tar" -ErrorAction SilentlyContinue
            $tarGzFiles = Get-ChildItem $path -Filter "*.tar.gz" -ErrorAction SilentlyContinue

            $allFiles = @()
            if ($tarFiles) { $allFiles += $tarFiles }
            if ($tarGzFiles) { $allFiles += $tarGzFiles }

            if ($allFiles.Count -gt 0) {
                $imageFiles += $allFiles
                Write-Host "  Found images in: $path" -ForegroundColor Cyan
                foreach ($file in $allFiles) {
                    $sizeMB = [math]::Round($file.Length / 1MB, 1)
                    Write-Host "    - $($file.Name) ($sizeMB MB)" -ForegroundColor Gray
                }
            }
        }
    }

    if ($imageFiles.Count -gt 0) {
        foreach ($file in $imageFiles) {
            $target = Join-Path $imagesDir $file.Name
            Copy-Item $file.FullName $target

            $sizeMB = [math]::Round($file.Length / 1MB, 1)
            $fileName = $file.Name
            Write-Host "  OK $fileName ($sizeMB MB)" -ForegroundColor Green
        }
        $imageCount = $imageFiles.Count
        Write-Host "  Total images copied: $imageCount" -ForegroundColor Green
    } else {
        Write-Host "  WARNING: No Docker image files found" -ForegroundColor Yellow
        foreach ($path in $imagePaths) {
            Write-Host "    - $path" -ForegroundColor Gray
        }
        Write-Host "    Run export-images.ps1 first to create image files" -ForegroundColor Gray
    }
} else {
    Write-Host ""
    Write-Host "Step 3: Skipping Docker images (use -IncludeImages to include)" -ForegroundColor Gray
}

# Copy environment configuration files
Write-Host ""
Write-Host "Step 3.1: Copying environment configuration..." -ForegroundColor Yellow

$envFiles = @(
    ".env.example"
)

# Copy backend network configuration files
$backendEnvFiles = @(
    "Backend_Django/.env.lan.example",
    "Backend_Django/.env.docker.example",
    "Backend_Django/.env.network.example"
)

foreach ($file in $backendEnvFiles) {
    $source = Join-Path $projectRoot $file
    $target = Join-Path $packageDir $file

    if (Test-Path $source) {
        # Create directory if it doesn't exist
        $targetDir = Split-Path $target -Parent
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        Copy-Item $source $target
        Write-Host "  OK $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING $file" -ForegroundColor Yellow
    }
}

# Copy frontend network configuration files
$frontendEnvFiles = @(
    "Frontend/.env.lan.example"
)

foreach ($file in $frontendEnvFiles) {
    $source = Join-Path $projectRoot $file
    $target = Join-Path $packageDir $file

    if (Test-Path $source) {
        # Create directory if it doesn't exist
        $targetDir = Split-Path $target -Parent
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        Copy-Item $source $target
        Write-Host "  OK $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING $file" -ForegroundColor Yellow
    }
}

foreach ($file in $envFiles) {
    $source = Join-Path $projectRoot $file
    $target = Join-Path $packageDir $file

    if (Test-Path $source) {
        Copy-Item $source $target
        Write-Host "  OK $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Step 4: Creating hot reload directory structure..." -ForegroundColor Yellow

# Create code-sync directory structure for hot reload
$codeSyncDir = Join-Path $dockerDir "code-sync"
$codeSyncBackendDir = Join-Path $codeSyncDir "Backend_Django"
$codeSyncFrontendDir = Join-Path $codeSyncDir "Frontend"
$codeSyncFrontendDistDir = Join-Path $codeSyncFrontendDir "dist"

New-Item -ItemType Directory -Path $codeSyncDir -Force | Out-Null
New-Item -ItemType Directory -Path $codeSyncBackendDir -Force | Out-Null
New-Item -ItemType Directory -Path $codeSyncFrontendDir -Force | Out-Null
New-Item -ItemType Directory -Path $codeSyncFrontendDistDir -Force | Out-Null

# Create data directory structure
$dataDir = Join-Path $dockerDir "data"
$dataDbDir = Join-Path $dataDir "db"
$dataModelsDir = Join-Path $dataDir "models"
$dataMediaDir = Join-Path $dataDir "media"
$dataLogsDir = Join-Path $dataDir "logs"

New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
New-Item -ItemType Directory -Path $dataDbDir -Force | Out-Null
New-Item -ItemType Directory -Path $dataModelsDir -Force | Out-Null
New-Item -ItemType Directory -Path $dataMediaDir -Force | Out-Null
New-Item -ItemType Directory -Path $dataLogsDir -Force | Out-Null

Write-Host "  OK Created hot reload directory structure" -ForegroundColor Green

Write-Host ""
Write-Host "Step 4.1: Validating configuration files..." -ForegroundColor Yellow

# 确保路径变量存在并重新定义
$backendPath = [System.IO.Path]::Combine($projectRoot, 'Backend_Django')
$frontendPath = [System.IO.Path]::Combine($projectRoot, 'Frontend')

Write-Host "  DEBUG: Backend path: $backendPath" -ForegroundColor Cyan
Write-Host "  DEBUG: Frontend path: $frontendPath" -ForegroundColor Cyan

# 验证网络配置文件（在源代码中验证） - 逐步拼接，便于兼容与调试
$backendProjectDir = if ($backendPath) { Join-Path -Path $backendPath -ChildPath 'backend_project' } else { $null }
Write-Host "  DEBUG: backend_project dir: $backendProjectDir" -ForegroundColor DarkCyan
$configDir = if ($backendProjectDir) { Join-Path -Path $backendProjectDir -ChildPath 'config' } else { $null }
Write-Host "  DEBUG: config dir: $configDir" -ForegroundColor DarkCyan
$networkConfigPath = if ($configDir) { Join-Path -Path $configDir -ChildPath 'environment.py' } else { $null }
Write-Host "  DEBUG: Network config path: $networkConfigPath" -ForegroundColor Cyan
if ([string]::IsNullOrWhiteSpace($networkConfigPath)) {
    Write-Host "  WARNING: Network configuration path is empty" -ForegroundColor Yellow
} elseif (Test-Path -LiteralPath $networkConfigPath) {
    Write-Host "  OK Network configuration found" -ForegroundColor Green
} else {
    Write-Host "  WARNING: Network configuration not found at $networkConfigPath" -ForegroundColor Yellow
}

# 验证扫码枪服务（在源代码中验证）
$scannerServicePath = Join-Path $backendPath "vision_app\services\scanner_service.py"
Write-Host "  DEBUG: Scanner service path: $scannerServicePath" -ForegroundColor Cyan
if (Test-Path $scannerServicePath) {
    Write-Host "  OK Scanner service found" -ForegroundColor Green
} else {
    Write-Host "  WARNING: Scanner service not found at $scannerServicePath" -ForegroundColor Yellow
}

# 验证Django Channels配置（在源代码中验证） - 逐步拼接
$asgiPath = if ($backendProjectDir) { Join-Path -Path $backendProjectDir -ChildPath 'asgi.py' } else { $null }
Write-Host "  DEBUG: Channels config path: $asgiPath" -ForegroundColor Cyan
if ([string]::IsNullOrWhiteSpace($asgiPath)) {
    Write-Host "  WARNING: Channels configuration path is empty" -ForegroundColor Yellow
} elseif (Test-Path -LiteralPath $asgiPath) {
    Write-Host "  OK Django Channels configuration found" -ForegroundColor Green
} else {
    Write-Host "  WARNING: Django Channels configuration not found at $asgiPath" -ForegroundColor Yellow
}

# 验证前端网络配置（在源代码中验证） - 逐步拼接
$frontendSrcConfigDir = if ($frontendPath) { Join-Path -Path $frontendPath -ChildPath (Join-Path 'src' 'config') } else { $null }
Write-Host "  DEBUG: Frontend src/config dir: $frontendSrcConfigDir" -ForegroundColor DarkCyan
$frontendNetworkConfigPath = if ($frontendSrcConfigDir) { Join-Path -Path $frontendSrcConfigDir -ChildPath 'networkConfig.ts' } else { $null }
Write-Host "  DEBUG: Frontend network config path: $frontendNetworkConfigPath" -ForegroundColor Cyan
if ([string]::IsNullOrWhiteSpace($frontendNetworkConfigPath)) {
    Write-Host "  WARNING: Frontend network configuration path is empty" -ForegroundColor Yellow
} elseif (Test-Path -LiteralPath $frontendNetworkConfigPath) {
    Write-Host "  OK Frontend network configuration found" -ForegroundColor Green
} else {
    Write-Host "  WARNING: Frontend network configuration not found at $frontendNetworkConfigPath" -ForegroundColor Yellow
}

# 验证requirements_docker.txt文件
$requirementsDockerPath = Join-Path $backendPath "requirements_docker.txt"
Write-Host "  DEBUG: Requirements docker path: $requirementsDockerPath" -ForegroundColor Cyan
if (Test-Path $requirementsDockerPath) {
    Write-Host "  OK requirements_docker.txt found" -ForegroundColor Green
} else {
    Write-Host "  WARNING: requirements_docker.txt not found at $requirementsDockerPath" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Step 5: Copying source code..." -ForegroundColor Yellow

# Copy backend code to code-sync location only (hot reload deployment)
$backendSource = $backendPath

if (Test-Path $backendSource) {
    # Copy to code-sync location for hot reload (excluding database to avoid conflicts)
    robocopy "$backendSource" "$codeSyncBackendDir" /E /XD venv __pycache__ .pytest_cache /XF *.pyc *.pyo db.sqlite3 *.log /NFL /NDL /NJH /NJS | Out-Null
    if ($LASTEXITCODE -le 1) {
        Write-Host "  OK Backend_Django -> code-sync (for hot reload)" -ForegroundColor Green
    } else {
        Write-Host "  ERROR copying Backend_Django to code-sync" -ForegroundColor Red
    }

    # Copy database to data directory for hot reload
    $dbSource = Join-Path $backendSource "db\db.sqlite3"
    $dbTarget = Join-Path $dataDbDir "db.sqlite3"
    if (Test-Path $dbSource) {
        Copy-Item $dbSource $dbTarget -Force
        Write-Host "  OK Database -> data/db (for hot reload)" -ForegroundColor Green
    } else {
        Write-Host "  WARNING: Database file not found at $dbSource" -ForegroundColor Yellow
        Write-Host "    Hot reload mode will create a new database" -ForegroundColor Gray
    }

    # Copy models to data directory
    $modelsSource = Join-Path $backendSource "models"
    if (Test-Path $modelsSource) {
        robocopy "$modelsSource" "$dataModelsDir" /E /NFL /NDL /NJH /NJS | Out-Null
        if ($LASTEXITCODE -le 1) {
            Write-Host "  OK Models -> data/models (for hot reload)" -ForegroundColor Green
        } else {
            Write-Host "  ERROR copying models to data directory" -ForegroundColor Red
        }
    } else {
        Write-Host "  WARNING: Models directory not found at $modelsSource" -ForegroundColor Yellow
    }
} else {
    Write-Host "  MISSING Backend_Django" -ForegroundColor Red
}

# Copy frontend code to code-sync location only (hot reload deployment)
$frontendSource = $frontendPath

if (Test-Path $frontendSource) {
    # Copy frontend source to code-sync location (without dist)
    robocopy "$frontendSource" "$codeSyncFrontendDir" /E /XD node_modules dist .vite /XF *.log /NFL /NDL /NJH /NJS | Out-Null
    if ($LASTEXITCODE -le 1) {
        Write-Host "  OK Frontend -> code-sync (for hot reload)" -ForegroundColor Green
    } else {
        Write-Host "  ERROR copying Frontend to code-sync" -ForegroundColor Red
    }

    # Create placeholder files in dist directory
    $placeholderIndex = Join-Path $codeSyncFrontendDistDir "index.html"
    $placeholderContent = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>AI Vision App - Building...</title>
</head>
<body>
    <h1>Frontend is building...</h1>
    <p>Please run build-frontend-hotreload.ps1 to build the frontend.</p>
</body>
</html>
"@
    # Write HTML file with encoding compatibility
    $writeSuccess = Write-FileWithEncoding -FilePath $placeholderIndex -Content $placeholderContent
    if (-not $writeSuccess) {
        Write-Host "  ERROR: Failed to create placeholder index.html" -ForegroundColor Red
    }
    Write-Host "  OK Created placeholder frontend dist" -ForegroundColor Green
} else {
    Write-Host "  MISSING Frontend" -ForegroundColor Red
}

Write-Host ""
Write-Host "Step 6: Creating deployment instructions..." -ForegroundColor Yellow

$instructionsFile = Join-Path $packageDir "DEPLOYMENT-INSTRUCTIONS.txt"
$currentTime = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
$instructions = @"
=== AI Vision App - Hot Reload Server Deployment Guide ===
Generated: $currentTime

QUICK HOT RELOAD DEPLOYMENT STEPS:

1. Copy Deployment Package
   Copy the entire deployment package to server, recommended path: C:\ai_vision_app\

2. Build Docker Images (Required for first deployment):
   cd docker
   .\scripts\build-images-deployment.ps1

   Build options:
   - Full build: .\scripts\build-images-deployment.ps1
   - Backend only: .\scripts\build-images-deployment.ps1 -BackendOnly
   - Frontend only: .\scripts\build-images-deployment.ps1 -FrontendOnly
   - No cache: .\scripts\build-images-deployment.ps1 -NoCache

3. Import Docker Images (Alternative to building):
   If you have pre-built images:
   .\scripts\import-images.ps1

4. Hot Reload Deployment:

   Method A - Auto build and start:
   .\scripts\deploy-hotreload.ps1 -Mode start

   Method B - Manual frontend build:
   .\scripts\build-frontend-hotreload.ps1
   .\scripts\deploy-hotreload.ps1 -Mode start

5. Configure Auto-Start (Recommended for Production):
   .\scripts\enable-auto-start.ps1

   This ensures containers automatically restart when Docker service starts.
   Verify with: docker inspect <container-name> --format '{{.HostConfig.RestartPolicy.Name}}'

6. Configure Network Settings (Optional):
   Copy and modify environment files for your network:
   - .env.example -> .env (main configuration)
   - Backend_Django/.env.lan.example -> Backend_Django/.env (LAN access)
   - Frontend/.env.lan.example -> Frontend/.env.local (frontend LAN)

7. Configure Scanner Device (Optional):
   Set scanner environment variables in .env:
   - SCANNER_ENABLED=true
   - SCANNER_DEFAULT_IP=************
   - SCANNER_DEFAULT_PORT=8080

8. Access Application:
   Frontend: http://localhost:8080 or http://ServerIP:8080
   Backend:  http://localhost:8000 or http://ServerIP:8000

9. Cross-Computer Code Sync (Development Collaboration):
   On Dev Machine A: .\scripts\setup-code-sharing-simple.ps1 setup
   On Deploy Machine B: .\scripts\sync-from-dev.ps1 -DevMachineIP "MachineA_IP" -Mode watch

HOT RELOAD DIRECTORY STRUCTURE:
docker/
|-- code-sync/                    (Hot reload code sync directory)
|   |-- Backend_Django/           (Backend code, auto-reload)
|   +-- Frontend/                 (Frontend source and build output)
|       +-- dist/                 (Frontend build output, served by nginx)
|-- data/                         (Persistent data directory)
|   |-- db/                       (Database files - includes complete model records)
|   |-- models/                   (AI model files - system and custom models)
|   |-- media/                    (Media files)
|   +-- logs/                     (Log files)
+-- scripts/                      (Management scripts)

HOT RELOAD FEATURES:
- Backend code changes auto-reload (Django runserver)
- Frontend code changes need rebuild (build-frontend-hotreload.ps1)
- AI model files persistent storage
- Database and logs persistence
- Cross-computer code sync support

DEVELOPMENT WORKFLOW:
1. Modify backend code -> Auto effective (Django hot reload)
2. Modify frontend code -> Run build-frontend-hotreload.ps1 -> Refresh browser
3. Cross-computer sync -> sync-from-dev.ps1 auto sync code changes

TROUBLESHOOTING:

Common Build Issues:
- If build fails with "No module named 'channels'": Run .\scripts\build-images-deployment.ps1 -NoCache
- If frontend build fails with package.json not found: Ensure code-sync/Frontend/ directory exists
- If backend build fails: Check code-sync/Backend_Django/requirements_docker.txt exists

Environment Checks:
- Environment check: .\scripts\check\check-docker-env.ps1
- Network check: .\scripts\check\check-network.ps1
- LAN access check: .\scripts\check\check-lan-access.ps1

Container Issues:
- View backend logs: docker logs ai-vision-backend-hotreload
- View frontend logs: docker logs ai-vision-frontend-hotreload
- Restart containers: .\scripts\deploy-hotreload.ps1 -Mode restart

DEPLOYMENT PACKAGE CONTENTS:
- docker/                              (Docker configs and scripts)
  |-- code-sync/                       (Hot reload source code)
  |   |-- Backend_Django/              (Backend source code for hot reload)
  |   +-- Frontend/                    (Frontend source code for hot reload)
  |-- data/                            (Persistent data storage)
  |-- docker-compose.*.yml             (Multiple deployment configs)
  |-- Dockerfile.*                     (Image build files)
  |-- nginx*.conf                      (Nginx configurations)
  |-- scripts/                         (Management scripts)
  |   |-- Core deployment scripts (4)
  |   |-- Image management scripts (3)
  |   |-- Development workflow scripts (2)
  |   |-- System config scripts (4)
  |   +-- check/ (Diagnostic tools 3)
  +-- docker-images/ (Docker image files)
- .env.example                         (Environment config template)

DETAILED DOCUMENTATION:
- Complete deployment guide: docker/README.md
- Frontend development docs: docker/code-sync/Frontend/README.md
- Backend development docs: docker/code-sync/Backend_Django/README.md

RECOMMENDED WORKFLOWS:
1. Daily development: deploy-hotreload.ps1 -Mode start
2. Production deployment: start-container.ps1
3. Cross-computer collaboration: setup-code-sharing-simple.ps1 + sync-from-dev.ps1
4. Troubleshooting: Diagnostic scripts in check/ directory

TIPS: All scripts support -? parameter for help information

COMMON COMMANDS:
- Start hot reload: .\scripts\deploy-hotreload.ps1 -Mode start
- Stop services: .\scripts\deploy-hotreload.ps1 -Mode stop
- Enable auto-start: .\scripts\enable-auto-start.ps1
- Build frontend: .\scripts\build-frontend-hotreload.ps1
- Check environment: .\scripts\check\check-docker-env.ps1
- Check network: .\scripts\check\check-network.ps1

SYSTEM REQUIREMENTS:
- Windows 10/11
- Docker Desktop
- PowerShell 5.1 or later
- Node.js 18+ (for frontend building)
- At least 8GB RAM
- At least 20GB free disk space

SUPPORT:
- Check logs in docker/data/logs/
- Use diagnostic scripts in docker/scripts/check/
- Review README files for detailed information
"@

# Write deployment instructions with encoding compatibility
$writeSuccess = Write-FileWithEncoding -FilePath $instructionsFile -Content $instructions
if ($writeSuccess) {
    Write-Host "  OK DEPLOYMENT-INSTRUCTIONS.txt (UTF-8 encoding)" -ForegroundColor Green
} else {
    Write-Host "  ERROR: Failed to create DEPLOYMENT-INSTRUCTIONS.txt" -ForegroundColor Red
}

# Compress if needed
if ($Compress) {
    Write-Host ""
    Write-Host "Step 7: Compressing package..." -ForegroundColor Yellow

    $zipFile = "$packageDir.zip"
    if (Test-Path $zipFile) {
        Remove-Item $zipFile -Force
    }

    Compress-Archive -Path "$packageDir\*" -DestinationPath $zipFile

    $zipSize = [math]::Round((Get-Item $zipFile).Length / 1MB, 1)
    Write-Host "  OK Created $zipFile ($zipSize MB)" -ForegroundColor Green
}

# Show results
Write-Host ""
Write-Host "=== Package Complete ===" -ForegroundColor Cyan
Write-Host "Package location: $packageDir" -ForegroundColor White

if ($Compress) {
    Write-Host "Compressed file: $packageDir.zip" -ForegroundColor White
}

Write-Host ""
Write-Host "Package contents:" -ForegroundColor Cyan
Get-ChildItem $packageDir -Recurse | ForEach-Object {
    if ($_.PSIsContainer) {
        $relativePath = $_.FullName.Replace($packageDir, "")
        Write-Host "  FOLDER $relativePath" -ForegroundColor Yellow
    } else {
        $relativePath = $_.FullName.Replace($packageDir, "")
        if ($_.Length -gt 1MB) {
            $sizeValue = [math]::Round($_.Length / 1MB, 1)
            $size = "$sizeValue MB"
        } else {
            $sizeValue = [math]::Round($_.Length / 1KB, 1)
            $size = "$sizeValue KB"
        }
        Write-Host "  FILE $relativePath ($size)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Yellow
Write-Host "1. 📦 Transfer the package to target server" -ForegroundColor White
Write-Host "2. 📖 Follow DEPLOYMENT-INSTRUCTIONS.txt on server" -ForegroundColor White
Write-Host "3. � Build images: .\scripts\build-images-deployment.ps1" -ForegroundColor White
Write-Host "4. �🔥 Start hot reload deployment: .\scripts\deploy-hotreload.ps1 -Mode start" -ForegroundColor White
Write-Host "5. 🔄 Configure auto-start: .\scripts\enable-auto-start.ps1" -ForegroundColor White
Write-Host "6. 🔄 Optional: Setup cross-computer code sync" -ForegroundColor White
Write-Host ""
Write-Host "🔥 Hot Reload Features:" -ForegroundColor Cyan
Write-Host "- Backend auto-reload on code changes" -ForegroundColor Gray
Write-Host "- Frontend build script for quick updates" -ForegroundColor Gray
Write-Host "- AI models and data persistence" -ForegroundColor Gray
Write-Host "- Cross-computer development sync" -ForegroundColor Gray
Write-Host ""
Write-Host "📚 Documentation:" -ForegroundColor Cyan
Write-Host "- Hot reload guide: docker/HOT-RELOAD-GUIDE.md" -ForegroundColor Gray
Write-Host "- Troubleshooting: docker/scripts/check/" -ForegroundColor Gray
Write-Host "- Frontend build: docker/scripts/build-frontend-hotreload.ps1" -ForegroundColor Gray
