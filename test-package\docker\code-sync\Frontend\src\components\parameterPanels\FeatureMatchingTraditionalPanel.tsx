import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Divider, InputNumber, Row, Select, Space, Upload, Alert, App } from 'antd';
import { InboxOutlined, PictureOutlined } from '@ant-design/icons';
import { useFeatureMatchingTraditional } from '../../contexts/FeatureMatchingTraditionalContext';
import { useImageWorkspace } from '../../contexts/ImageWorkspaceContext';
import { RcFile } from 'antd/es/upload';
import { featureMatchingTraditionalApi } from '../../services/api';
import ExampleImagesModal from '../ExampleImagesModal'; // 导入示例图片模态框

const { Dragger } = Upload;

// --- 内部组件 ---

interface ParameterSettingsProps {
    handleRunMatching: () => void;
    isMatching: boolean;
    templateImage: File | null;
    targetImage: File | null;
}

const ParameterSettings: React.FC<ParameterSettingsProps> = ({
    handleRunMatching,
    isMatching,
    templateImage,
    targetImage,
}) => {
    const {
        algorithm, setAlgorithm,
        matchRatioThreshold, setMatchRatioThreshold,
        minMatchCount, setMinMatchCount,
    } = useFeatureMatchingTraditional();

    return (
        <Card title="2. 设置参数并执行" size="small">
            <Row gutter={[16, 8]} align="middle">
                <Col span={8}>算法选择:</Col>
                <Col span={16}>
                    <Select value={algorithm} onChange={setAlgorithm} style={{ width: '100%' }}>
                        <Select.Option value="SIFT">SIFT</Select.Option>
                        <Select.Option value="ORB">ORB</Select.Option>
                    </Select>
                </Col>

                <Col span={8}>匹配率阈值:</Col>
                <Col span={16}>
                    <InputNumber
                        min={0.1}
                        max={1.0}
                        step={0.05}
                        value={matchRatioThreshold}
                        onChange={(value) => setMatchRatioThreshold(value ?? 0.7)}
                        style={{ width: '100%' }}
                    />
                </Col>

                <Col span={8}>最少匹配点:</Col>
                <Col span={16}>
                    <InputNumber
                        min={1}
                        max={100}
                        step={1}
                        value={minMatchCount}
                        onChange={(value) => setMinMatchCount(value ?? 10)}
                        style={{ width: '100%' }}
                    />
                </Col>
            </Row>
            <Divider style={{ margin: '12px 0' }} />
            <Button
                type="primary"
                onClick={handleRunMatching}
                loading={isMatching}
                style={{ width: '100%' }}
                disabled={!templateImage || !targetImage}
            >
                开始匹配
            </Button>
        </Card>
    );
};

// --- 主面板组件 ---

const FeatureMatchingTraditionalPanel: React.FC = () => {
    const { message } = App.useApp();
    const {
        templateImage, setTemplateImage,
        templatePreview, setTemplatePreview,
        targetImage, setTargetImage,
        targetPreview, setTargetPreview,
        roi, setRoi,
        algorithm, matchRatioThreshold, minMatchCount,
        isMatching, setIsMatching,
        setMatchingResult,
    } = useFeatureMatchingTraditional();

    const {
        croppedImageDataUrl,
        selectedRoi,
        loadImage,
        setDetectionResults,
        imageList,
        currentImageIndex
    } = useImageWorkspace();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [modalCaller, setModalCaller] = useState<'template' | 'target' | null>(null);

    // 监听从主工作区截取的ROI
    useEffect(() => {
        if (croppedImageDataUrl && selectedRoi && imageList[currentImageIndex]) {
            console.log("ROI data received from workspace:", selectedRoi);
            // 预览图使用裁剪后的数据URL
            setTemplatePreview(croppedImageDataUrl);
            // 实际要上传的模板文件是当前工作区的原始大图
            const originalImageFile = imageList[currentImageIndex];
            setTemplateImage(originalImageFile);
            // 保存ROI坐标
            setRoi(selectedRoi);
            message.success('已从主视图截取模板');
        }
    }, [croppedImageDataUrl, selectedRoi, imageList, currentImageIndex, setTemplateImage, setTemplatePreview, setRoi, message]);

    const handleImageUpload = (file: RcFile, type: 'template' | 'target') => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            if (type === 'template') {
                setTemplateImage(file);
                setTemplatePreview(imageUrl);
                setRoi(null); // 通过上传方式清除ROI信息
            } else {
                setTargetImage(file);
                setTargetPreview(imageUrl);
            }
        };
        reader.readAsDataURL(file);
        return false; // 阻止 antd 的自动上传
    };

    const handleOpenModal = (caller: 'template' | 'target') => {
        setModalCaller(caller);
        setIsModalVisible(true);
    };

    const handleImageFromModal = (imageFile: File) => {
        if (modalCaller) {
            handleImageUpload(imageFile as RcFile, modalCaller);
        }
    };

    const handleRunMatching = async () => {
        if (!templateImage) {
            message.error('请先提供模板图片!');
            return;
        }
        if (!targetImage) {
            message.error('请提供目标图片!');
            return;
        }

        setIsMatching(true);
        setMatchingResult(null);
        const key = 'matching';
        message.loading({ content: '正在执行匹配...', key });

        try {
            const formData = new FormData();
            formData.append('template_image', templateImage);
            formData.append('target_image', targetImage);
            
            // 无论如何都发送ROI参数。如果模板是上传的，ROI为null，则发送一个全图ROI
            const roiToSend = roi || { x: 0, y: 0, width: 0, height: 0 };
            formData.append('template_roi', JSON.stringify(roiToSend));

            formData.append('algorithm', algorithm);
            formData.append('match_ratio_threshold', String(matchRatioThreshold));
            formData.append('min_match_count', String(minMatchCount));

            const result = await featureMatchingTraditionalApi(formData);
            setMatchingResult(result);

            if (result.status === 'success' && result.match_result) {
                message.success({ content: '匹配成功! 正在主视图中展示结果...', key });
                // 在主视图中显示结果
                await loadImage(targetImage); // 加载目标图片到主视图
                await new Promise(resolve => setTimeout(resolve, 200)); // 等待图片加载

                const { box_corners } = result.match_result;
                const detection = {
                    box: [
                        Math.min(box_corners[0][0], box_corners[1][0], box_corners[2][0], box_corners[3][0]),
                        Math.min(box_corners[0][1], box_corners[1][1], box_corners[2][1], box_corners[3][1]),
                        Math.max(box_corners[0][0], box_corners[1][0], box_corners[2][0], box_corners[3][0]),
                        Math.max(box_corners[0][1], box_corners[1][1], box_corners[2][1], box_corners[3][1]),
                    ] as [number, number, number, number],
                    polygon: box_corners,
                    label: `匹配区域 (置信度: ${result.keypoints_info.good_matches})`,
                    type: 'general_object' as const,
                };
                await setDetectionResults([detection]);

            } else {
                message.error({ content: `匹配失败: ${result.message}`, key });
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            // 在捕获到错误时，也设置一个包含处理时间的结果对象
            setMatchingResult({
                status: 'error',
                message: errorMessage,
                algorithm_used: algorithm,
                keypoints_info: { template_keypoints: 0, target_keypoints: 0, good_matches: 0 },
                processing_time: 0 // 客户端错误，处理时间计为0
            });
            message.error({ content: `匹配异常: ${errorMessage}`, key });
        } finally {
            setIsMatching(false);
        }
    };

    const renderUploadCard = (title: string, type: 'template' | 'target') => {
        const preview = type === 'template' ? templatePreview : targetPreview;
        const fileName = type === 'template' ? templateImage?.name : targetImage?.name;

        return (
            <Card title={title} size="small">
                <Dragger
                    name={type}
                    multiple={false}
                    beforeUpload={(file) => handleImageUpload(file, type)}
                    showUploadList={false}
                    accept="image/*"
                    height={preview ? undefined : 150} // 自动调整高度
                    style={{ marginBottom: '10px', padding: '10px' }}
                >
                    {preview ? (
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ height: '120px', position: 'relative', marginBottom: '8px' }}>
                                <img
                                    src={preview}
                                    alt={`${type} preview`}
                                    style={{
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        maxWidth: '100%',
                                        maxHeight: '100%',
                                        objectFit: 'contain'
                                    }}
                                />
                            </div>
                            <Alert message={fileName} type="success" style={{ fontSize: '12px', wordBreak: 'break-all' }} />
                            <p className="ant-upload-text" style={{ fontSize: '12px', color: '#888', marginTop: '8px' }}>
                                可再次拖拽或点击此区域替换图片
                            </p>
                        </div>
                    ) : (
                        <div style={{ transform: 'translateY(-10px)' }}>
                            <p className="ant-upload-drag-icon"><InboxOutlined /></p>
                            <p className="ant-upload-text">点击或拖拽文件到此区域</p>
                        </div>
                    )}
                </Dragger>
                <Button icon={<PictureOutlined />} style={{ width: '100%' }} onClick={() => handleOpenModal(type)}>
                    从示例库选择
                </Button>
            </Card>
        );
    };

    return (
        <>
            <div style={{ padding: '16px' }}>
                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                    <Card title="1. 提供图片" size="small">
                        <Alert
                            message="操作提示"
                            description="您可以通过主菜单栏的 '编辑' -> '框选工具' 在主图上绘制矩形, 该区域将自动作为模板图片载入。也可以选择传入图片"
                            type="info"
                            showIcon
                            closable
                            style={{ marginBottom: '16px' }}
                        />
                        <Row gutter={16}>
                            <Col span={12}>{renderUploadCard('模板 (Template)', 'template')}</Col>
                            <Col span={12}>{renderUploadCard('目标 (Target)', 'target')}</Col>
                        </Row>
                    </Card>
                    <ParameterSettings
                        handleRunMatching={handleRunMatching}
                        isMatching={isMatching}
                        templateImage={templateImage}
                        targetImage={targetImage}
                    />
                </Space>
            </div>
            <ExampleImagesModal
                visible={isModalVisible}
                onCancel={() => {
                    setIsModalVisible(false);
                    setModalCaller(null);
                }}
                selectionMode="single"
                onSelectImage={handleImageFromModal}
            />
        </>
    );
};

export default FeatureMatchingTraditionalPanel;