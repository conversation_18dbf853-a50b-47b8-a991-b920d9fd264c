import React, { useState } from 'react';

interface FpsMonitorProps {
  backendFps: number | null;
  frontendFps: number | null;
  isVisible: boolean;
}

const FpsMonitor: React.FC<FpsMonitorProps> = ({ backendFps, frontendFps, isVisible }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!isVisible) return null;

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div 
      className="fps-monitor"
      style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        borderRadius: '6px',
        padding: isExpanded ? '12px' : '8px 12px',
        fontSize: '12px',
        fontFamily: 'monospace',
        cursor: 'pointer',
        userSelect: 'none',
        zIndex: 1000,
        minWidth: '80px',
        transition: 'all 0.3s ease',
        backdropFilter: 'blur(4px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
      }}
      onClick={toggleExpanded}
    >
      <div 
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: isExpanded ? '8px' : '0',
        }}
      >
        <span style={{ fontWeight: 'bold', color: '#4CAF50' }}>FPS</span>
        <span 
          style={{
            fontSize: '10px',
            color: '#888',
            transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease',
          }}
        >
          ▼
        </span>
      </div>
      
      {isExpanded && (
        <div 
          style={{
            fontSize: '11px',
            lineHeight: '1.4',
            animation: 'fadeIn 0.3s ease',
          }}
        >
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            marginBottom: '4px',
          }}>
            <span style={{ color: '#FFB74D' }}>后端:</span>
            <span style={{ 
              color: backendFps !== null ? '#4CAF50' : '#f44336',
              fontWeight: 'bold',
            }}>
              {backendFps !== null ? `${backendFps.toFixed(1)}` : '--'}
            </span>
          </div>
          
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between',
          }}>
            <span style={{ color: '#64B5F6' }}>前端:</span>
            <span style={{ 
              color: frontendFps !== null ? '#4CAF50' : '#f44336',
              fontWeight: 'bold',
            }}>
              {frontendFps !== null ? `${frontendFps.toFixed(1)}` : '--'}
            </span>
          </div>
          
          {/* 性能指示器 */}
          {backendFps !== null && frontendFps !== null && (
            <div style={{ 
              marginTop: '6px',
              padding: '2px 6px',
              borderRadius: '3px',
              fontSize: '10px',
              textAlign: 'center',
              backgroundColor: Math.abs(backendFps - frontendFps) < 1 ? 
                'rgba(76, 175, 80, 0.2)' : 'rgba(255, 193, 7, 0.2)',
              color: Math.abs(backendFps - frontendFps) < 1 ? '#4CAF50' : '#FFC107',
            }}>
              {Math.abs(backendFps - frontendFps) < 1 ? '✓ 同步' : '⚠ 延迟'}
            </div>
          )}
        </div>
      )}
      
      {/* 简化显示（收起状态） */}
      {!isExpanded && (
        <span style={{ 
          fontSize: '11px',
          color: '#4CAF50',
          fontWeight: 'bold',
        }}>
          {frontendFps !== null ? frontendFps.toFixed(1) : '--'}
        </span>
      )}
      
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-5px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        .fps-monitor:hover {
          backgroundColor: rgba(0, 0, 0, 0.8) !important;
        }
      `}</style>
    </div>
  );
};

export default FpsMonitor; 