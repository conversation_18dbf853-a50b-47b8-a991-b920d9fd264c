// Frontend/src/components/imageDisplay/SvgOverlay.tsx
// import React, { forwardRef } from 'react'; // Import forwardRef
import { forwardRef } from 'react';
import { ImageInfo, DisplayableDetection, Roi, FeatureMatch } from '../../contexts/ImageWorkspaceContext';
import { calculateTextRotationFromPolygon, calculateOptimalTextPosition, calculateAdaptiveFontSize, MAX_ADAPTIVE_FONT_SIZE } from '../../utils/textRotationUtils';

interface SvgOverlayProps {
  width: number;
  height: number;
  drawingRoi: Roi | null;
  selectedRoi: Roi | null;
  detections: DisplayableDetection[] | null;
  featureMatches?: FeatureMatch[] | null; // 新增：特征匹配线
  currentImageInfo: ImageInfo | null;
}

// 在组件的外部定义固定值
const fixedStrokeWidth = 8.0; // 未检测调整后的固定描边宽度
const fixedTextYOffset = -4; // 未检测调整后的文本相对于y1的固定Y偏移量
const fixedTextStrokeWidth = '5.0px'; // 未检测调整后的固定文本描边宽度
const fixedRoiStrokeWidth = 5; // 调整后的固定感兴趣区域（ROI）描边宽度
const fixedRoiDashArray = "5 2.5"; // 调整后的固定感兴趣区域（ROI）虚线数组

// Use React.ForwardedRef for the ref type
export const SvgOverlay = forwardRef<SVGSVGElement, SvgOverlayProps>(({
  width,
  height,
  drawingRoi,
  selectedRoi,
  detections,
  featureMatches,
  currentImageInfo,
}, ref) => {
  if (!currentImageInfo) {
    return null;
  }

  return (
    <svg
      ref={ref}
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        pointerEvents: 'none',
      }}
    >
      {/* Render Detection Results */}
      {detections && detections.length > 0 && (
        <g className="detections">
          {detections.map((detection, index) => {
            if (detection.box.some(isNaN)) {
              console.warn("Skipping detection with NaN coordinates:", detection);
              return null;
            }
            const [x1, y1, x2, y2] = detection.box;
            const boxWidth = x2 - x1;
            const boxHeight = y2 - y1;
            const sequenceNumber = detection.displayIndex !== undefined ? detection.displayIndex : (index + 1);
            
            // 根据检测类型决定标签文本内容
            let labelText: string;
            if (detection.type === 'ocr_line' || detection.type === 'ocr_char') {
              // OCR 检测：只显示序号和识别的文本内容，不显示置信度
              labelText = `${sequenceNumber}. ${detection.label}`;
            } else {
              // 其他类型检测（如条码）：保持原有格式，包含置信度
              labelText = `${sequenceNumber}. ${detection.class_name || detection.label}${detection.confidence ? ` (${detection.confidence.toFixed(3)})` : ''}`;
            }

            const barcodeColorMap: { [key: string]: string } = {
              '1D': 'rgba(0, 255, 0, 0.7)',
              'QR_CODE': 'rgba(0, 0, 255, 0.7)',
              'DATA_MATRIX': 'rgba(255, 255, 0, 0.7)',
              '2D_QR': 'rgba(0, 0, 255, 0.7)',
              '2D_DM': 'rgba(255, 255, 0, 0.7)',
              '2D_Other': 'rgba(128, 0, 128, 0.7)',
            };

            let strokeColor = 'rgba(255, 165, 0, 0.7)'; // Default orange

            if (detection.type === 'barcode') {
              strokeColor = (detection.class_name && barcodeColorMap[detection.class_name])
                ? barcodeColorMap[detection.class_name]
                : 'rgba(255, 0, 0, 0.7)';
            } else if (detection.type === 'ocr_char') {
              strokeColor = 'rgba(0, 0, 255, 0.7)';
            } else if (detection.type === 'ocr_line') {
              strokeColor = 'rgba(0, 128, 0, 0.7)';
            }

            // Constants are now defined at the component's top level scope
            if (boxWidth < 0 || boxHeight < 0) {
                console.warn("Skipping detection with negative width/height:", detection);
                return null;
            }

            // Render polygon for ocr_line if available, otherwise fall back to rectangle
            // 逻辑重构：分离形状渲染和文本渲染
            // 1. 准备形状 (Shape)
            let shapeElement;

            // 使用更强的类型保护，直接在 if 条件中检查
            if (detection.polygon && detection.polygon.length >= 3) {
                const points = detection.polygon.map(p => p.join(',')).join(' ');
                let fillColor = strokeColor.replace(/, ?([0-9.]+)\)$/, ', 0.2)');
                if (fillColor === strokeColor) {
                    if (strokeColor.startsWith('rgba')) {
                        fillColor = strokeColor.substring(0, strokeColor.lastIndexOf(',') + 1) + ' 0.2)';
                    } else if (strokeColor.startsWith('rgb')) {
                        fillColor = strokeColor.replace('rgb', 'rgba').replace(')', ', 0.2)');
                    }
                }
                shapeElement = (
                    <polygon
                        points={points}
                        fill={fillColor}
                        stroke={strokeColor}
                        strokeWidth={fixedStrokeWidth}
                    />
                );
            } else {
                shapeElement = (
                    <rect
                        x={x1}
                        y={y1}
                        width={boxWidth}
                        height={boxHeight}
                        fill="none"
                        stroke={strokeColor}
                        strokeWidth={fixedStrokeWidth}
                    />
                );
            }

            // 2. 准备文本 (Text)
            let textElement;
            // 只有 'ocr_line' 类型需要计算文本旋转
            if (detection.type === 'ocr_line' && detection.polygon && detection.polygon.length >= 3) {
                const rotationInfo = calculateTextRotationFromPolygon(detection.polygon);
                const textPosition = calculateOptimalTextPosition(detection.polygon, rotationInfo.rotationAngle);
                const adaptiveFontSize = calculateAdaptiveFontSize(
                    detection.label,
                    detection.polygon,
                    MAX_ADAPTIVE_FONT_SIZE
                );
                textElement = (
                    <text
                        x={textPosition.x}
                        y={textPosition.y}
                        fill={strokeColor}
                        fontSize={adaptiveFontSize}
                        textAnchor={textPosition.textAnchor}
                        dominantBaseline={textPosition.dominantBaseline}
                        transform={`rotate(${rotationInfo.rotationAngle}, ${textPosition.x}, ${textPosition.y})`}
                        style={{
                            paintOrder: 'stroke',
                            stroke: '#ffffff',
                            strokeWidth: fixedTextStrokeWidth,
                            strokeLinecap: 'butt',
                            strokeLinejoin: 'miter'
                        }}
                    >
                        {labelText}
                    </text>
                );
            } else {
                // 其他所有类型（包括带多边形的 general_object）都使用标准的水平文本
                textElement = (
                    <text
                        x={x1}
                        y={y1 + fixedTextYOffset}
                        fill={strokeColor}
                        fontSize={MAX_ADAPTIVE_FONT_SIZE}
                        style={{ paintOrder: 'stroke', stroke: '#ffffff', strokeWidth: fixedTextStrokeWidth, strokeLinecap: 'butt', strokeLinejoin: 'miter' }}
                    >
                        {labelText}
                    </text>
                );
            }

            // 3. 组合渲染
            return (
                <g key={detection.id || `${detection.label}-${x1}-${y1}`}>
                    {shapeElement}
                    {textElement}
                </g>
            );
          })}
        </g>
      )}

      {/* Render Feature Matches */}
      {featureMatches && featureMatches.length > 0 && (
        <g className="feature-matches">
          {featureMatches.map((match, index) => (
            <line
              key={`match-${index}`}
              x1={match.point1[0]}
              y1={match.point1[1]}
              x2={match.point2[0]}
              y2={match.point2[1]}
              stroke="rgba(255, 0, 255, 0.7)" // 品红色
              strokeWidth={2}
            />
          ))}
        </g>
      )}

      {/* Render Drawing ROI */}
      {drawingRoi && (
        <rect
          x={drawingRoi.x}
          y={drawingRoi.y}
          width={drawingRoi.width}
          height={drawingRoi.height}
          fill="rgba(0, 120, 255, 0.3)"
          stroke="rgba(0, 120, 255, 0.8)"
          strokeWidth={fixedRoiStrokeWidth}
          strokeDasharray={fixedRoiDashArray}
        />
      )}

      {/* Render Selected ROI */}
      {selectedRoi && (
         <rect
           x={selectedRoi.x}
           y={selectedRoi.y}
           width={selectedRoi.width}
           height={selectedRoi.height}
           fill="rgba(255, 200, 0, 0.2)"
           stroke="rgba(255, 150, 0, 0.7)"
           strokeWidth={fixedRoiStrokeWidth}
           strokeDasharray={fixedRoiDashArray}
         />
      )}
    </svg>
  );
});

SvgOverlay.displayName = 'SvgOverlay'; // Optional: for better debugging

export default SvgOverlay;