/**
 * 前端配置入口文件
 * 
 * 统一导出所有配置相关的功能。
 */

import {
  networkConfig,
  getNetworkConfig as _getNetworkConfig,
  getNetworkConfigSync as _getNetworkConfigSync,
  reloadNetworkConfig as _reloadNetworkConfig
} from './networkConfig';

export {
  networkConfig,
  _getNetworkConfig as getNetworkConfig,
  _getNetworkConfigSync as getNetworkConfigSync,
  _reloadNetworkConfig as reloadNetworkConfig
};

export type { NetworkConfig, BackendNetworkInfo } from './networkConfig';

/**
 * 初始化前端配置
 * 
 * 在应用启动时调用，确保配置正确加载。
 */
export async function initializeConfig(): Promise<void> {
  try {
    console.log('🚀 初始化前端配置...');

    // 预加载网络配置
    const config = await _getNetworkConfig();
    
    console.log('✅ 前端配置初始化完成');
    console.log('📊 配置摘要:', {
      environment: config.environment,
      backendUrl: config.backendUrl,
      apiBaseUrl: config.apiBaseUrl,
      wsUrl: config.wsUrl,
    });
    
  } catch (error) {
    console.error('❌ 前端配置初始化失败:', error);
    // 不抛出错误，让应用继续运行
  }
}

/**
 * 获取配置摘要信息
 */
export function getConfigSummary(): string {
  const config = _getNetworkConfigSync();
  return `Environment: ${config.environment}, Backend: ${config.backendUrl}, API: ${config.apiBaseUrl}`;
}
