---
description: Explore and utilize the Ultralytics download utilities to handle URLs, zip/unzip files, and manage GitHub assets effectively.
keywords: Ultralytics, download utilities, URL validation, zip directory, unzip file, check disk space, Google Drive, GitHub assets, YOLO, machine learning
---

# Reference for `ultralytics/utils/downloads.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/downloads.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/downloads.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/downloads.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.downloads.is_url

<br><br><hr><br>

## ::: ultralytics.utils.downloads.delete_dsstore

<br><br><hr><br>

## ::: ultralytics.utils.downloads.zip_directory

<br><br><hr><br>

## ::: ultralytics.utils.downloads.unzip_file

<br><br><hr><br>

## ::: ultralytics.utils.downloads.check_disk_space

<br><br><hr><br>

## ::: ultralytics.utils.downloads.get_google_drive_file_info

<br><br><hr><br>

## ::: ultralytics.utils.downloads.safe_download

<br><br><hr><br>

## ::: ultralytics.utils.downloads.get_github_assets

<br><br><hr><br>

## ::: ultralytics.utils.downloads.attempt_download_asset

<br><br><hr><br>

## ::: ultralytics.utils.downloads.download

<br><br>
