import React from 'react';
import { useScanner } from '../contexts/ScannerContext';
import { Button, Spin } from 'antd';
import { VideoCameraOutlined } from '@ant-design/icons';
import FpsMonitor from './FpsMonitor';

const VideoStreamPlayer: React.FC = () => {
  const { isConnected, isStreaming, frameData, startStream, stopStream, frontendFps, backendFps } = useScanner();

  const renderContent = () => {
    if (!isConnected) {
      return <p>设备未连接</p>;
    }
    if (isStreaming) {
      if (frameData) {
        return <img src={frameData} alt="Video Stream" style={{ maxWidth: '100%', maxHeight: '100%' }} />;
      }
      return (
        <Spin tip="正在加载视频流..." size="large">
          <div style={{ minHeight: '200px', minWidth: '300px' }} />
        </Spin>
      );
    }
    return <p>视频流已停止</p>;
  };

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#000', color: '#fff' }}>
      <div style={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%' }}>
        {renderContent()}
      </div>
      
      {/* FPS监控组件 - 仅在推流时显示 */}
      <FpsMonitor 
        backendFps={backendFps}
        frontendFps={frontendFps}
        isVisible={isStreaming}
      />
      
      {isConnected && (
        <div style={{ 
          position: 'absolute',
          bottom: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1000
        }}>
          <Button
            type="primary"
            icon={<VideoCameraOutlined />}
            onClick={isStreaming ? stopStream : startStream}
            danger={isStreaming}
          >
            {isStreaming ? '停止推流' : '开始推流'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default VideoStreamPlayer;