# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv8 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 4 # number of classes
ch: 1
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.125, 1024] # YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs

# YOLOv8.0n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Ghost<PERSON>onv, [128, 3, 2]] # 1-P2/4
  - [-1, 1, C3k2, [128, True]]  # 2
  - [-1, 1, Ghost<PERSON>onv, [256, 3, 2]] # 3-P3/8
  - [-1, 1, C3k2, [256, True]]  # 4
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 1, C3k2, [512, True]]  # 6
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 1, C3k2, [1024, True]]  # 8
  - [-1, 1, SPPF, [1024, 5]] # 9

# YOLOv8.0n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]  # 10
  - [[-1, 6], 1, Concat, [1]]  # cat backbone P4  # 11
  - [-1, 1, C3k2, [512]]  # 12

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]  # 13
  - [[-1, 4], 1, Concat, [1]]  # cat backbone P3  # 14
  - [-1, 2, C3k2, [256]]  # 15 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]  # 16
  - [[-1, 12], 1, Concat, [1]]  # cat head P4  # 17
  - [-1, 2, C3k2, [512]]  # 18 (P4/16-medium)

  - [-1, 1, SCDown, [512, 3, 2]]  # 19
  - [[-1, 9], 1, Concat, [1]]  # cat head P5  # 20
  - [-1, 1, C3k2, [1024]]  # 21 (P5/32-large)

  - [[15, 18, 21], 1, Detect, [nc]]  # Detect(P3, P4, P5)
