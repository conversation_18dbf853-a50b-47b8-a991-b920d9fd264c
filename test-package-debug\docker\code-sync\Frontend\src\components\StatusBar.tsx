import React, { useState, useEffect } from 'react';
import { Layout } from 'antd';
import { useImageWorkspace } from '../contexts/ImageWorkspaceContext'; // Import the hook

const { Footer } = Layout;

const StatusBar: React.FC = () => {
  const { currentImageInfo, mouseImageCoordinates } = useImageWorkspace(); // Get state from context
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    // 添加事件监听器
    window.addEventListener('resize', handleResize);

    // 清理函数：组件卸载时移除监听器
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const displayX = mouseImageCoordinates && currentImageInfo ? mouseImageCoordinates.x.toFixed(0) : '--';
  const displayY = mouseImageCoordinates && currentImageInfo ? mouseImageCoordinates.y.toFixed(0) : '--';

  return (
    <Footer style={{
      padding: '0 12px',
      height: '24px',
      lineHeight: '22px',
      backgroundColor: '#3280FC', // 用户指定的新蓝色 RGB(50, 128, 252)
      borderTop: '1px solid #2A6EDC', // 相应的边框色
      color: '#ffffff', // 白色文字
      fontSize: '12px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <span>就绪</span>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ marginRight: '20px' }}>{`X: ${displayX}, Y: ${displayY}`}</span>
        <span>窗口大小: {windowSize.width} x {windowSize.height}</span>
      </div>
    </Footer>
  );
};

export default StatusBar; 