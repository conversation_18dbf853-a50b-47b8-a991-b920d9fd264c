# Generated by Django 5.2.1 on 2025-05-21 05:51

from django.db import migrations
from django.utils import timezone # 导入 timezone


def populate_identity_card_models(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    # 身份证检测模型记录
    AIModel.objects.using(db_alias).create(
        name='identity_card_number_cn', # 任务名称
        description='中文身份证号码检测模型 (PaddleOCR)', # 描述
        # model_file 存储相对于 SYSTEM_MODELS_ROOT/<model_type>/ 的路径或文件名
        # 这里存储 inference 目录相对于 SYSTEM_MODELS_ROOT/ocr/ 的相对路径
        model_file='Identity_card_number_cn/Identity_card_det_model',
        version='1.0', # 版本号
        model_type='ocr',
        is_system_model=True,
        ocr_role='detection',
        uploaded_at=timezone.now()
    )

    # 身份证识别模型记录
    AIModel.objects.using(db_alias).create(
        name='identity_card_number_cn', # 任务名称
        description='中文身份证号码识别模型 (PaddleOCR)', # 描述
         # model_file 存储相对于 SYSTEM_MODELS_ROOT/<model_type>/ 的路径或文件名
        # 这里存储 inference 目录相对于 SYSTEM_MODELS_ROOT/ocr/ 的相对路径
        model_file='Identity_card_number_cn/Identity_card_rec_model',
        version='1.0', # 版本号
        model_type='ocr',
        is_system_model=True,
        ocr_role='recognition',
        uploaded_at=timezone.now()
    )


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0008_populate_general_ocr_models"),
    ]

    operations = [
        migrations.RunPython(populate_identity_card_models),
    ]
