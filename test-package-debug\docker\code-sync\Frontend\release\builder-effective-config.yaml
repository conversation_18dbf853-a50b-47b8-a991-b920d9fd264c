directories:
  output: release
  buildResources: build
appId: com.aivision.app
productName: AI Vision App
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - package.json
      - public/**/*
extraResources:
  - from: ../Backend_Django/dist/backend.exe
    to: backend.exe
win:
  icon: public/favicon.ico
  target:
    - target: nsis
      arch:
        - x64
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: AI Vision App
mac:
  icon: public/favicon.ico
  target:
    - dmg
linux:
  icon: public/favicon.ico
  target:
    - AppImage
  category: Development
electronVersion: 36.4.0
