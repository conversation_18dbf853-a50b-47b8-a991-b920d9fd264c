# Generated manually for updating mobile model file paths

from django.db import migrations

def update_mobile_model_paths(apps, schema_editor):
    """
    更新mobile模型的文件路径以匹配新的文件夹结构
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始更新mobile模型文件路径...")
    
    # 1. 更新general_text_mobile_ch_en模型路径
    print("1. 更新general_text_mobile_ch_en模型路径...")
    ch_en_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_ch_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in ch_en_models:
        old_path = model.model_file.name
        if model.ocr_role == 'detection':
            new_path = 'ocr/general_ocr_mobile_ch_en/PP-OCRv4_mobile_det_inference'
            model.model_file = new_path
            model.save()
            print(f"   更新检测模型 ID {model.id}: {old_path} -> {new_path}")
        elif model.ocr_role == 'recognition':
            new_path = 'ocr/general_ocr_mobile_ch_en/PP-OCRv4_mobile_rec_inference_ch_en'
            model.model_file = new_path
            model.save()
            print(f"   更新识别模型 ID {model.id}: {old_path} -> {new_path}")
    
    # 2. 更新general_text_mobile_en模型路径
    print("2. 更新general_text_mobile_en模型路径...")
    en_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in en_models:
        old_path = model.model_file.name
        if model.ocr_role == 'detection':
            # 英文模型使用中英文的检测模型
            new_path = 'ocr/general_ocr_mobile_ch_en/PP-OCRv4_mobile_det_inference'
            model.model_file = new_path
            model.save()
            print(f"   更新英文检测模型 ID {model.id}: {old_path} -> {new_path}")
        elif model.ocr_role == 'recognition':
            new_path = 'ocr/general_ocr_mobile_en/PP-OCRv4_mobile_rec_inference_en'
            model.model_file = new_path
            model.save()
            print(f"   更新英文识别模型 ID {model.id}: {old_path} -> {new_path}")
    
    print("Mobile模型文件路径更新完成！")

def reverse_update_mobile_model_paths(apps, schema_editor):
    """
    回滚操作：恢复原始文件路径
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始回滚mobile模型文件路径...")
    
    # 回滚general_text_mobile_ch_en模型路径
    ch_en_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_ch_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in ch_en_models:
        if model.ocr_role == 'detection':
            model.model_file = 'ocr/general_ocr_mobile_ch_en/PP-OCRv4_mobile_det_inference'
        elif model.ocr_role == 'recognition':
            model.model_file = 'ocr/general_ocr_mobile_ch_en/PP-OCRv4_mobile_rec_inference'
        model.save()
    
    # 回滚general_text_mobile_en模型路径
    en_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in en_models:
        if model.ocr_role == 'detection':
            model.model_file = 'ocr/general_ocr_mobile_en/inference'
        elif model.ocr_role == 'recognition':
            model.model_file = 'ocr/general_ocr_mobile_en/inference'
        model.save()
    
    print("Mobile模型文件路径回滚完成！")

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0016_add_general_text_mobile_en_and_update_descriptions'),
    ]

    operations = [
        migrations.RunPython(
            update_mobile_model_paths,
            reverse_update_mobile_model_paths
        ),
    ]
