---
description: Explore detailed metrics and utility functions for model validation and performance analysis with Ultralytics' metrics module.
keywords: Ultralytics, metrics, model validation, performance analysis, IoU, confusion matrix
---

# Reference for `ultralytics/utils/metrics.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/metrics.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/metrics.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/metrics.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.metrics.ConfusionMatrix

<br><br><hr><br>

## ::: ultralytics.utils.metrics.Metric

<br><br><hr><br>

## ::: ultralytics.utils.metrics.DetMetrics

<br><br><hr><br>

## ::: ultralytics.utils.metrics.SegmentMetrics

<br><br><hr><br>

## ::: ultralytics.utils.metrics.PoseMetrics

<br><br><hr><br>

## ::: ultralytics.utils.metrics.ClassifyMetrics

<br><br><hr><br>

## ::: ultralytics.utils.metrics.OBBMetrics

<br><br><hr><br>

## ::: ultralytics.utils.metrics.bbox_ioa

<br><br><hr><br>

## ::: ultralytics.utils.metrics.box_iou

<br><br><hr><br>

## ::: ultralytics.utils.metrics.bbox_iou

<br><br><hr><br>

## ::: ultralytics.utils.metrics.mask_iou

<br><br><hr><br>

## ::: ultralytics.utils.metrics.kpt_iou

<br><br><hr><br>

## ::: ultralytics.utils.metrics._get_covariance_matrix

<br><br><hr><br>

## ::: ultralytics.utils.metrics.probiou

<br><br><hr><br>

## ::: ultralytics.utils.metrics.batch_probiou

<br><br><hr><br>

## ::: ultralytics.utils.metrics.smooth_BCE

<br><br><hr><br>

## ::: ultralytics.utils.metrics.smooth

<br><br><hr><br>

## ::: ultralytics.utils.metrics.plot_pr_curve

<br><br><hr><br>

## ::: ultralytics.utils.metrics.plot_mc_curve

<br><br><hr><br>

## ::: ultralytics.utils.metrics.compute_ap

<br><br><hr><br>

## ::: ultralytics.utils.metrics.ap_per_class

<br><br>
