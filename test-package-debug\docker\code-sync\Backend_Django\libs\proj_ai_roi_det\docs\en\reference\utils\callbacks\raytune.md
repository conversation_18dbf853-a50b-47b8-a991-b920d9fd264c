---
description: Learn how to integrate Ray Tune with Ultralytics YOLO for efficient hyperparameter tuning and performance tracking.
keywords: Ultralytics, Ray Tune, hyperparameter tuning, YOLO, machine learning, deep learning, callbacks, integration, training metrics
---

# Reference for `ultralytics/utils/callbacks/raytune.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/raytune.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/raytune.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/raytune.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.raytune.on_fit_epoch_end

<br><br>
