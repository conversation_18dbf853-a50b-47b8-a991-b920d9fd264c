# Generated by Django 5.2.1 on 2025-05-21 07:16

from django.db import migrations

def populate_ai_restorer_model(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    model_name = "AI_Restorer_NCHW_1x1x256x256"
    model_description = "系统内置的 AI 图像修复模型。"
    model_file_name = "AI_Restorer_NCHW_1x1x256x256_V*******.onnx"
    model_version = "*******"
    model_type_value = "ai_restored"
    is_system = True

    AIModel.objects.using(db_alias).update_or_create(
        name=model_name,
        model_type=model_type_value,
        is_system_model=is_system,
        defaults={
            'description': model_description,
            'model_file': model_file_name,
            'version': model_version,
            'ocr_role': None,
        }
    )

class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0010_correct_identity_card_model_paths"),
    ]

    operations = [
        migrations.RunPython(populate_ai_restorer_model),
    ]
