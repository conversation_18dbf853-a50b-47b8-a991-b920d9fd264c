# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv8 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 4 # number of classes
ch: 1
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.100, 768] # YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs

# YOLOv8.0n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]           # 0-P1/2
  - [-1, 1, Ghost<PERSON>onv, [128, 3, 2]]     # 1-P2/4
  - [-1, 1, SCDown, [128, 3, 1]]        # 2
  - [-1, 1, Ghost<PERSON>onv, [256, 3, 2]]     # 3-P3/8
  - [-1, 1, SCDown, [256, 3, 1]]        # 4
  - [-1, 1, SCD<PERSON>, [512, 3, 2]]        # 5-P4/16
  - [-1, 1, C3k2, [512, True]]          # 6
  - [-1, 1, SCDown, [1024, 3, 2]]       # 7-P5/32
  - [-1, 1, C3k2, [1024, True]]         # 8
  - [-1, 1, SPPF, [1024, 5]]            # 9

# YOLOv8.0n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]    # 10
  - [[-1, 6], 1, Concat, [1]]                     # cat backbone P4  # 11
  - [-1, 1, C3k2, [384]]                          # 12 (P4/16-medium)

  - [-1, 1, SCDown, [384, 3, 2]]                  # 13
  - [[-1, 9], 1, Concat, [1]]                     # cat head P5  # 14
  - [-1, 1, C3k2, [768]]                          # 15 (P5/32-large)

  - [[12, 15], 1, Detect, [nc]]                   # Detect(P4, P5) 