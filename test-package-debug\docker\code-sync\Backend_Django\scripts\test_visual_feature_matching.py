#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化特征匹配测试脚本
测试后端API的全图特征点匹配和ROI处理功能
"""

import os
import sys
import cv2
import numpy as np
import json
import requests
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'vision_app'))

# 导入后端预测器进行对比
from vision_app.feature_matching_onnxruntime_model_predictor import (
    ModelBasedFeatureMatcher, 
    FeatureMatchingPredictor
)

def draw_keypoints(image, keypoints, color=(0, 255, 0), radius=3):
    """在图像上绘制关键点"""
    img_draw = image.copy()
    for kp in keypoints:
        coord = kp["coord"]
        cv2.circle(img_draw, coord, radius, color, -1)
        # 可选：绘制分数
        score = kp.get("score", 0)
        cv2.putText(img_draw, f"{score:.1f}", 
                   (coord[0]+5, coord[1]-5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
    return img_draw

def draw_matches_lines(img1, img2, matches, offset_x=0):
    """绘制匹配线条"""
    # 创建拼接图像
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    
    # 统一高度
    max_h = max(h1, h2)
    img1_padded = np.zeros((max_h, w1, 3), dtype=np.uint8)
    img2_padded = np.zeros((max_h, w2, 3), dtype=np.uint8)
    
    img1_padded[:h1, :w1] = img1
    img2_padded[:h2, :w2] = img2
    
    # 水平拼接
    combined = np.hstack([img1_padded, img2_padded])
    
    # 绘制匹配线
    for match in matches:
        pt1 = tuple(map(int, match["point1"]))
        pt2 = tuple(map(int, [match["point2"][0] + w1, match["point2"][1]]))
        
        # 根据匹配质量选择颜色
        distance = match.get("distance", 0)
        if distance < 0.3:
            color = (0, 255, 0)  # 绿色 - 高质量
        elif distance < 0.5:
            color = (0, 255, 255)  # 黄色 - 中等质量
        else:
            color = (0, 0, 255)  # 红色 - 低质量
            
        cv2.line(combined, pt1, pt2, color, 1)
        cv2.circle(combined, pt1, 3, (255, 0, 0), -1)
        cv2.circle(combined, pt2, 3, (255, 0, 0), -1)
    
    return combined

def draw_roi_box(image, roi_dict, color=(255, 0, 0), thickness=2):
    """绘制ROI框"""
    if not roi_dict:
        return image
    
    img_draw = image.copy()
    x = roi_dict['x']
    y = roi_dict['y'] 
    w = roi_dict['width']
    h = roi_dict['height']
    
    # 绘制矩形框
    cv2.rectangle(img_draw, (x, y), (x+w, y+h), color, thickness)
    
    # 添加标签
    cv2.putText(img_draw, "ROI", (x, y-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
    
    return img_draw

def test_api_with_visualization():
    """测试API并可视化结果"""
    print("🎨 开始可视化特征匹配测试...")
    
    # 检查测试图片
    image1_path = "sample4/1.bmp"
    image2_path = "sample4/3.bmp"
    
    if not os.path.exists(image1_path) or not os.path.exists(image2_path):
        print(f"❌ 测试图片不存在: {image1_path} 或 {image2_path}")
        return False
    
    # 读取图片
    img1 = cv2.imread(image1_path)
    img2 = cv2.imread(image2_path)
    
    print(f"模板图像尺寸: {img1.shape}")
    print(f"目标图像尺寸: {img2.shape}")
    
    # 定义几个不同的ROI区域进行测试
    test_cases = [
        {
            "name": "无ROI - 全图匹配",
            "roi": None
        },
        {
            "name": "中心ROI",
            "roi": {"x": 400, "y": 300, "width": 480, "height": 424}
        },
        {
            "name": "左上角ROI", 
            "roi": {"x": 100, "y": 100, "width": 300, "height": 200}
        },
        {
            "name": "右下角ROI",
            "roi": {"x": 800, "y": 600, "width": 400, "height": 300}
        }
    ]
    
    # 创建本地预测器进行对比
    model_path = "superpoint.onnx"
    if os.path.exists(model_path):
        local_predictor = ModelBasedFeatureMatcher(model_path)
    else:
        local_predictor = None
        print("⚠️  本地模型文件不存在，跳过本地预测器对比")
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n{'='*60}")
        print(f"📋 测试案例 {i+1}: {test_case['name']}")
        print(f"{'='*60}")
        
        roi = test_case["roi"]
        
        # 1. 本地预测器测试（参考）
        local_result = None
        if local_predictor:
            print("\n🏠 本地预测器测试...")
            try:
                local_result = local_predictor.predict(
                    template_image=img1,
                    target_image=img2,
                    template_roi=roi,
                    keypoints_count=100,
                    nms_grid_size=4,
                    nms_threshold=8.0/255.0,
                    match_ratio_threshold=0.8,
                    min_match_count=4,
                    ransac_threshold=3.0
                )
                print(f"本地结果: {local_result['data']['match_count']} 个匹配点")
            except Exception as e:
                print(f"❌ 本地预测器失败: {str(e)}")
        
        # 2. API测试
        print("\n🌐 API测试...")
        api_result = test_api_call(img1, img2, roi)
        
        # 3. 结果对比和可视化
        if api_result and api_result.get('status') == 'success':
            print(f"API结果: {api_result['data']['match_count']} 个匹配点")
            
            # 可视化结果
            visualize_results(img1, img2, api_result, roi, test_case['name'], i+1)
            
            results.append({
                "test_case": test_case['name'],
                "roi": roi,
                "api_success": True,
                "api_matches": api_result['data']['match_count'],
                "local_matches": local_result['data']['match_count'] if local_result else None
            })
        else:
            print("❌ API测试失败")
            results.append({
                "test_case": test_case['name'],
                "roi": roi,
                "api_success": False,
                "api_matches": 0,
                "local_matches": local_result['data']['match_count'] if local_result else None
            })
    
    # 生成测试报告
    generate_test_report(results)
    
    return True

def test_api_call(img1, img2, roi):
    """调用API进行测试"""
    api_url = "http://localhost:8000/vision_app/detect/feature-matching/model/"
    
    try:
        # 保存临时图片文件
        temp_img1 = "temp_template.jpg"
        temp_img2 = "temp_target.jpg"
        cv2.imwrite(temp_img1, img1)
        cv2.imwrite(temp_img2, img2)
        
        # 准备请求数据
        with open(temp_img1, 'rb') as f1, open(temp_img2, 'rb') as f2:
            files = {
                'template_image': ('template.jpg', f1, 'image/jpeg'),
                'target_image': ('target.jpg', f2, 'image/jpeg')
            }
            
            data = {
                'model_id': '1',
                'keypoints_count': '100',
                'nms_grid_size': '4', 
                'nms_threshold': str(8.0/255.0),
                'match_ratio_threshold': '0.8',
                'min_match_count': '4',
                'ransac_threshold': '3.0'
            }
            
            if roi:
                data['template_roi'] = json.dumps(roi)
            
            print("发送API请求...")
            response = requests.post(api_url, files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result
            else:
                print(f"API请求失败: {response.status_code} - {response.text}")
                return None
        
        # 清理临时文件
        if os.path.exists(temp_img1):
            os.remove(temp_img1)
        if os.path.exists(temp_img2):
            os.remove(temp_img2)
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Django服务器")
        print("请确保Django服务器正在运行: python manage.py runserver")
        return None
    except Exception as e:
        print(f"❌ API调用失败: {str(e)}")
        return None

def visualize_results(img1, img2, api_result, roi, test_name, case_num):
    """可视化匹配结果"""
    print(f"\n🎨 生成可视化结果...")
    
    # 获取匹配数据
    matches = api_result['data'].get('matches', [])
    
    # 1. 绘制ROI区域（如果有）
    img1_with_roi = draw_roi_box(img1, roi) if roi else img1.copy()
    
    # 2. 绘制匹配线条
    match_visualization = draw_matches_lines(img1_with_roi, img2, matches)
    
    # 3. 保存结果图像
    output_path = f"test_result_{case_num}_{test_name.replace(' ', '_')}.jpg"
    cv2.imwrite(output_path, match_visualization)
    
    print(f"✅ 可视化结果已保存: {output_path}")
    
    # 4. 显示统计信息
    if matches:
        distances = [m.get('distance', 0) for m in matches]
        print(f"📊 匹配质量统计:")
        print(f"  总匹配数: {len(matches)}")
        print(f"  平均距离: {np.mean(distances):.4f}")
        print(f"  最小距离: {min(distances):.4f}")
        print(f"  最大距离: {max(distances):.4f}")
        
        # 质量分布
        high_quality = sum(1 for d in distances if d < 0.3)
        medium_quality = sum(1 for d in distances if 0.3 <= d < 0.5)
        low_quality = sum(1 for d in distances if d >= 0.5)
        
        print(f"  质量分布:")
        print(f"    高质量(<0.3): {high_quality}")
        print(f"    中等质量(0.3-0.5): {medium_quality}")
        print(f"    低质量(>=0.5): {low_quality}")

def generate_test_report(results):
    """生成测试报告"""
    print(f"\n{'='*60}")
    print("📋 测试报告汇总")
    print(f"{'='*60}")
    
    print(f"{'测试案例':<20} {'ROI':<15} {'API成功':<10} {'API匹配数':<10} {'本地匹配数':<10}")
    print("-" * 70)
    
    for result in results:
        roi_str = "是" if result['roi'] else "否"
        api_success = "✅" if result['api_success'] else "❌"
        api_matches = result['api_matches']
        local_matches = result['local_matches'] if result['local_matches'] is not None else "N/A"
        
        print(f"{result['test_case']:<20} {roi_str:<15} {api_success:<10} {api_matches:<10} {local_matches:<10}")
    
    # 成功率统计
    success_count = sum(1 for r in results if r['api_success'])
    total_count = len(results)
    success_rate = success_count / total_count * 100
    
    print(f"\n📊 总体统计:")
    print(f"  测试案例总数: {total_count}")
    print(f"  成功案例数: {success_count}")
    print(f"  成功率: {success_rate:.1f}%")
    
    # ROI影响分析
    no_roi_results = [r for r in results if not r['roi']]
    with_roi_results = [r for r in results if r['roi']]
    
    if no_roi_results and with_roi_results:
        avg_no_roi = np.mean([r['api_matches'] for r in no_roi_results if r['api_success']])
        avg_with_roi = np.mean([r['api_matches'] for r in with_roi_results if r['api_success']])
        
        print(f"\n🔍 ROI影响分析:")
        print(f"  无ROI平均匹配数: {avg_no_roi:.1f}")
        print(f"  有ROI平均匹配数: {avg_with_roi:.1f}")
        
        if avg_no_roi > avg_with_roi:
            print(f"  结论: 全图匹配效果更好 (+{avg_no_roi-avg_with_roi:.1f}个匹配点)")
        else:
            print(f"  结论: ROI限制匹配效果相当")

def main():
    """主函数"""
    print("🎨 可视化特征匹配测试")
    print("测试API的全图特征点匹配和ROI处理功能")
    print("="*60)
    
    # 切换到scripts目录
    os.chdir(os.path.dirname(__file__))
    
    # 检查Django服务器
    try:
        response = requests.get("http://localhost:8000", timeout=5)
        print("✅ Django服务器连接正常")
    except:
        print("❌ Django服务器未启动")
        print("请先启动Django服务器: python manage.py runserver")
        return False
    
    # 执行可视化测试
    success = test_api_with_visualization()
    
    if success:
        print("\n🎉 可视化测试完成！")
        print("请查看生成的可视化图像文件")
    else:
        print("\n❌ 测试失败，请检查配置")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 