import cv2
import json
import tqdm
import copy
import random
import shutil
import numpy as np

from multiprocessing import Pool
from typing import TypedDict, get_type_hints
from pathlib import Path

class LabelmeShape(TypedDict):
    """
    Labelme中的shapes属性

    Args:
        label (str): 目标类别
        points (list[list[int, int]]): 目标的坐标
        group_id (str): 分组
        shape_type (str): 坐标类型
        flags (dict): 标志
    """
    label: str
    points: list[list[int, int]]
    group_id: str
    shape_type: str
    flags: dict


class LabelmeDict(TypedDict):
    """
    Labelme标签

    Args:
        version (str): Labelme版本号
        flags (list): 标志
        shapes (list[LabelmeShape]): 各个目标的形状
        imagePath (str): 图片相对路径
        imageData (str): 图片数据
        imageHeight (int): 图片高度
        imageWidth (int): 图片宽度
    """
    version: str
    flags: dict
    shapes: list[LabelmeShape]
    imagePath: str
    imageData: str
    imageHeight: int
    imageWidth: int


class DatasetLatentDiffusion():
    def __init__(
            self,
            classes:list[str],
            label_file: str = "label.json",
            label_img: str = "label.png",
            prompt_file: str = "prompt.txt",
            suffix: str = ".png",
        ) -> None:
        """
        初始化

        Args:
            classes (list[str]): 检测目标类别
            label_file (str): 标签文件名
            label_img (str): 标签图片名
            prompt_file (str): 提示词文件名
            suffix (str): 保存图片后缀
        """
        self.classes = classes
        self.label_file = label_file
        self.label_img = label_img
        self.prompt_file = prompt_file
        self.suffix = suffix

    def _read_labelme_file(
            self,
            label_file: Path,
        ) -> tuple[LabelmeDict, list[LabelmeShape]]:
        """
        读Labelme标签文件

        Args:
            label_file (Path): Labelme标签文件路径

        Returns:
            (tuple[LabelmeDict, LabelmeShape]): Labelme标签文件内容，和仅保留需要类别的LabelmeShape列表
        """
        # 读取标签内容
        label_content:LabelmeDict = {}
        with open(label_file, "r") as file:
            label_content = json.loads(file.read())

        # 获取需要的类别
        shapes:list[LabelmeShape] = [x for x in label_content["shapes"] if x["label"] in self.classes]

        # 返回结果
        return label_content, shapes

    def _read_prompt_file(
            self,
            prompt_path: Path,
        ) -> str:
        """
        读取提示词

        Args:
            prompt_path (Path): 提示词文件路径

        Returns:
            (str): 提示词
        """
        # 读取提示词
        prompt:str = ""
        with open(prompt_path, "r") as file:
            prompt = file.read()

        return prompt

    def _rotate(
            self,
            image: cv2.Mat,
            angle: float,
            shapes: list[LabelmeShape] = None,
        ) -> tuple[cv2.Mat, list[LabelmeShape] | None]:
        """
        旋转图片

        Args:
            image (cv2.Mat): 要旋转的图像
            angle (float): 旋转角度
            shapes (list[LabelmeShape]): 目标形状信息

        Returns:
            (tuple[cv2.Mat, list[LabelmeShape] | None]): 旋转后的图像和坐标点
        """
        # 获取图像中心
        height, width = image.shape[:2]
        center = (width // 2, height // 2)

        # 计算旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

        # 计算旋转后图像的新尺寸
        abs_cos = abs(rotation_matrix[0, 0])
        abs_sin = abs(rotation_matrix[0, 1])
        new_width = int(height * abs_sin + width * abs_cos)
        new_height = int(height * abs_cos + width * abs_sin)

        # 调整旋转矩阵
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]

        # 应用旋转变换
        rotated_image = cv2.warpAffine(
            image,
            rotation_matrix,
            (new_width, new_height),
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=255  # 白色背景
        )

        # 将旋转应用到坐标点上
        new_shapes = copy.deepcopy(shapes)
        if shapes is not None:
            for shape in new_shapes:
                points = np.array(shape['points'])
                points = points.astype(np.float32).reshape(-1, 1, 2)
                rotated_points = cv2.transform(points, rotation_matrix).reshape(-1, 2).tolist()
                shape['points'] = rotated_points

        # 返回结果
        return rotated_image, new_shapes

    def _get_perspective_transform_matrix(
            self,
            size: tuple[int],
            max_transform,
        ):
        """
        透视变换

        Args:
            size (tuple[int]): 图像高度和宽度
            max_transform (int): 最大变换像素距离

        Returns:
            (tuple[cv2.Mat, list[LabelmeShape] | None]): 旋转后的图像和坐标点
        """
        height, width = size
        # 原始四个角点
        src_points = np.float32([
            [0, 0],
            [width-1, 0],
            [0, height-1],
            [width-1, height-1]
        ])

        # 生成随机目标点
        dst_points = src_points.copy()
        for i in range(4):
            dst_points[i][0] += random.uniform(-max_transform, max_transform)
            dst_points[i][1] += random.uniform(-max_transform, max_transform)

        # 计算透视变换矩阵
        perspective_matrix = cv2.getPerspectiveTransform(src_points, dst_points)

        return perspective_matrix

    def _perspective_transform(
            self,
            image: cv2.Mat,
            perspective_matrix,
            shapes: list[LabelmeShape] = None,
        ) -> tuple[cv2.Mat, list[LabelmeShape] | None]:
        """
        透视变换

        Args:
            image (cv2.Mat): 要做透视变换的图像
            perspective_matrix: 透视变换矩阵
            shapes (list[LabelmeShape]): 目标形状信息

        Returns:
            (tuple[cv2.Mat, list[LabelmeShape] | None]): 透视变换后的图像和坐标点
        """
        # 获取图像尺寸
        height, width = image.shape[0:2]

        # 应用透视变换
        transformed_image = cv2.warpPerspective(image, perspective_matrix, (width, height), borderMode=cv2.BORDER_CONSTANT, borderValue=255)

        # 将旋转应用到坐标点上
        new_shapes = copy.deepcopy(shapes)
        if shapes is not None:
            for shape in new_shapes:
                points = np.array(shape['points'])
                points = points.astype(np.float32).reshape(-1, 1, 2)
                rotated_points = cv2.perspectiveTransform(points, perspective_matrix).reshape(-1, 2).tolist()
                shape['points'] = rotated_points

        # 返回结果
        return transformed_image, new_shapes

    def _crop_single(
            self,
            image: cv2.Mat,
            size: tuple[int],
            mode: str,
            shape: LabelmeShape,
        ) -> cv2.Mat:
        """
        裁剪单个目标

        Args:
            image (cv2.Mat): 要裁剪的图像
            size (tuple[int]): 目标尺寸（H, W）
            mode (str): 裁剪模式，可选["fix", "dilate"]
            shape (LabelmeShape): 目标形状信息

        Returns:
            (tuple[cv2.Mat, LabelmeShape]): 裁剪后的图像和坐标点
        """
        img_sz = image.shape[0:2]
        # 图片尺寸小于裁剪尺寸时返回原图
        if mode == 'fix':
            if img_sz[0] <= size[0] or img_sz[1] <= size[1]:
                return image, shape

        # 计算中心点坐标和目标高宽
        cp = [0, 0] # x, y
        bbox_xmin = img_sz[1]
        bbox_ymin = img_sz[0]
        bbox_xmax = 0
        bbox_ymax = 0
        for point in shape['points']:
            cp[0] += point[0]
            cp[1] += point[1]
            bbox_xmin = round(min(bbox_xmin, point[0]))
            bbox_ymin = round(min(bbox_ymin, point[1]))
            bbox_xmax = round(max(bbox_xmax, point[0]))
            bbox_ymax = round(max(bbox_ymax, point[1]))
        cp[0] /= len(shape['points'])
        cp[1] /= len(shape['points'])
        bbox_h = bbox_ymax - bbox_ymin + 1
        bbox_w = bbox_xmax - bbox_xmin + 1

        # 获取裁剪区域坐标
        if mode == 'fix':
            x_min = round(max(cp[0] - size[1]/2, 0))
            x_max = min(x_min + size[1]-1, img_sz[1]-1)
            y_min = round(max(cp[1] - size[0]/2, 0))
            y_max = min(y_min + size[0]-1, img_sz[0]-1)
        elif mode == 'dilate':
            x_min = round(max(cp[0] - size[1]/2 - bbox_w/2, 0))
            x_max = round(min(x_min + size[1]-1 + bbox_w, img_sz[1]-1))
            y_min = round(max(cp[1] - size[0]/2 - bbox_h/2, 0))
            y_max = round(min(y_min + size[0]-1 + bbox_h, img_sz[0]-1))
        else:
            print('Unsupported crop mode!')
            exit()

        # 裁剪图像
        crop_img = image.copy()[y_min:y_max+1, x_min:x_max+1] # 裁剪图像
        return crop_img

    def _Perspective_Transform_And_Crop_Process(
            self,
            args: tuple,
        ) -> None:
        """
        对图像做透视变换并裁剪目标区域执行线程

        Args:
            args (tuple): 参数列表

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 获取参数
        index, img, label_file, label_img, prompt_file, dst_image, dst_label, size, resize, mode, quantity, max_transform_range, angle_range, seed = args
        # 设置随机种子
        random.seed(seed)

        # 读取图片和标签
        image = cv2.imread(img, cv2.IMREAD_GRAYSCALE)
        label = cv2.imread(label_img, cv2.IMREAD_GRAYSCALE)
        # 读取标签
        _, shapes = self._read_labelme_file(label_file)
        prompt = self._read_prompt_file(prompt_file)

        label_list:list[str] = []
        for i in range(quantity):
            # 旋转图片
            angle = random.uniform(*angle_range)
            t_image, t_shapes = self._rotate(image, angle, shapes)
            t_label, _ = self._rotate(label, angle)

            # 透视变换
            max_transform = random.randint(*max_transform_range)
            perspective_matrix = self._get_perspective_transform_matrix(image.shape[0:2], max_transform)
            t_image, t_shapes = self._perspective_transform(t_image, perspective_matrix, t_shapes)
            t_label, _ = self._perspective_transform(t_label, perspective_matrix)

            # 裁剪并保存图像
            for shape in t_shapes:
                crop_image = self._crop_single(t_image, size, mode, shape)
                crop_label = self._crop_single(t_label, size, mode, shape)

                # resize
                if resize is not None:
                    crop_image = cv2.resize(crop_image, (resize[1], resize[0]))
                    crop_label = cv2.resize(crop_label, (resize[1], resize[0]))

                file_name = f"{index}_{i}{self.suffix}"
                cv2.imwrite(Path(dst_image, file_name).as_posix(), crop_image)
                cv2.imwrite(Path(dst_label, file_name).as_posix(), crop_label)

                row = {
                    "text": prompt,
                    "image": file_name,
                    "conditioning_image": file_name,
                    "ip_adapter_image": file_name,
                }
                label_list.append(json.dumps(row))

        return label_list

    def Perspective_Transform_And_Crop(
            self,
            src_pathes: list[str],
            size: tuple[int],
            quantity: int,
            dst_path: str,
            resize: tuple[int] | None = None,
            save_label: str | None = None,
            max_transform_range: int = (100, 200),
            angle_range: float = (-180, 180),
            num_processes: int = 20,
            seed: int = 20241211,
            mode: str = "fix",
        ) -> str | None:
        """
        对图像做透视变换并裁剪目标区域

        Args:
            src_pathes (list[str]): 源文件路径列表
            size (tuple[int]): 目标尺寸（H, W）
            quantity (int): 生成图片数量
            dst_path (str): 目标生成路径
            resize (tuple[int] | None): resize尺寸（H, W），填写None时不做resize
            save_label (str): 将标签内容写入到指定文件中
            max_transform_range (tuple[int]): 最大变换像素距离范围
            angle_range (tuple[float]): 旋转范围（单位：度）
            num_processes (int): 同时执行的进程数
            seed (int): 随机种子，用于确保随机过程一致
            mode (str): 裁剪模式，可选["fix", "dilate"]

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 检查输入参数是否合法
        if dst_path in src_pathes:
            print("src_path and dst_path can't be the same.")
            return None

        # 创建存储路径
        dst_image = Path(dst_path, "image")
        dst_label = Path(dst_path, "label")
        shutil.rmtree(dst_image, ignore_errors=True)
        shutil.rmtree(dst_label, ignore_errors=True)
        dst_image.mkdir(parents=True, exist_ok=True)
        dst_label.mkdir(parents=True, exist_ok=True)

        # 准备多进程参数
        index = 0
        process_args = []
        for src_path in src_pathes:
            # 获取图片列表
            imgs = list(Path(src_path).iterdir())
            # 获取标签文件，并从图片列表中移除标签
            label_file = Path(src_path, self.label_file)
            label_img = Path(src_path, self.label_img)
            prompt_file = Path(src_path, self.prompt_file)
            imgs.remove(label_file)
            imgs.remove(label_img)
            imgs.remove(prompt_file)
            # 准备参数
            for img in imgs:
                process_args.append((index, img, label_file, label_img, prompt_file, dst_image, dst_label, size, resize, mode, quantity, max_transform_range, angle_range, seed+index))
                index += 1

        # 使用进程池处理图片
        with Pool(processes=num_processes) as pool:
            # 使用tqdm显示进度
            results = list(tqdm.tqdm(
                pool.imap(self._Perspective_Transform_And_Crop_Process, process_args),
                total=len(process_args),
                desc="Processing images"
            ))

        if save_label is not None:
            # 保存标签文件
            label_list:list[str] = []
            for result in results:
                label_list.extend(result)
            with open(Path(dst_path, save_label), "w") as file:
                file.write("\n".join(label_list))

        return dst_path