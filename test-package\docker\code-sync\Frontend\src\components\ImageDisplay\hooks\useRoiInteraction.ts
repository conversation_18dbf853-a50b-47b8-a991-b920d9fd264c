// Frontend/src/components/ImageDisplay/hooks/useRoiInteraction.ts
import { useState, useCallback, useEffect } from 'react';
import { Roi } from '../../../contexts/ImageWorkspaceContext'; // Assuming Roi type is exported or define it here

export interface MouseCoordinates {
  x: number;
  y: number;
}

export type GetCoordinatesRelativeToImageFn = (event: React.MouseEvent<HTMLDivElement> | MouseEvent) => MouseCoordinates | null;

interface UseRoiInteractionProps {
  isSelectingRoi: boolean;
  currentImageWidth?: number;
  currentImageHeight?: number;
  setSelectedRoiCoordinates: (roi: Roi | null) => void;
  completeRoiSelection: () => void;
  getCoordinatesRelativeToImage: GetCoordinatesRelativeToImageFn;
  // imageContainerRef: RefObject<HTMLDivElement>; // For global mouse event bounds, if needed
}

export const useRoiInteraction = ({
  isSelectingRoi,
  currentImageWidth,
  currentImageHeight,
  setSelectedRoiCoordinates,
  completeRoiSelection,
  getCoordinatesRelativeToImage,
  // imageContainerRef, // May not be directly used if events are on the container
}: UseRoiInteractionProps) => {
  const [drawingRoi, setDrawingRoi] = useState<Roi | null>(null);
  const [roiStartPoint, setRoiStartPoint] = useState<MouseCoordinates | null>(null);

  const handleMouseDownForRoi = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (!isSelectingRoi || !currentImageWidth || !currentImageHeight) return;
    // Prevent panzoom from interfering
    event.preventDefault(); 
    event.stopPropagation();

    const coords = getCoordinatesRelativeToImage(event);
    if (coords) {
      setRoiStartPoint(coords);
      setDrawingRoi({ ...coords, width: 0, height: 0 });
      // console.log('ROI Mouse Down (Image Coords):', coords);
    }
  }, [isSelectingRoi, currentImageWidth, currentImageHeight, getCoordinatesRelativeToImage]);

  const handleMouseMoveForRoi = useCallback((event: React.MouseEvent<HTMLDivElement> | MouseEvent) => {
    if (!isSelectingRoi || !roiStartPoint || !currentImageWidth || !currentImageHeight) return;
    // Prevent panzoom from interfering if called from React event
    if ('preventDefault' in event) event.preventDefault();
    if ('stopPropagation' in event) event.stopPropagation();
    

    const currentCoords = getCoordinatesRelativeToImage(event as React.MouseEvent<HTMLDivElement>); // Cast needed for shared handler
    if (currentCoords) {
      const x = Math.min(roiStartPoint.x, currentCoords.x);
      const y = Math.min(roiStartPoint.y, currentCoords.y);
      let width = Math.abs(currentCoords.x - roiStartPoint.x);
      let height = Math.abs(currentCoords.y - roiStartPoint.y);

      // Constrain ROI to image boundaries
      const constrainedX = Math.max(0, x);
      const constrainedY = Math.max(0, y);
      const constrainedWidth = Math.min(width, currentImageWidth - constrainedX);
      const constrainedHeight = Math.min(height, currentImageHeight - constrainedY);

      setDrawingRoi({ x: constrainedX, y: constrainedY, width: constrainedWidth, height: constrainedHeight });
    }
  }, [isSelectingRoi, roiStartPoint, currentImageWidth, currentImageHeight, getCoordinatesRelativeToImage]);

  const handleMouseUpForRoi = useCallback((event?: React.MouseEvent<HTMLDivElement> | MouseEvent) => {
    if (!isSelectingRoi || !drawingRoi || !roiStartPoint) return;
    if (event && 'preventDefault' in event) event.preventDefault();
    if (event && 'stopPropagation' in event) event.stopPropagation();

    if (drawingRoi.width > 5 && drawingRoi.height > 5) { // Min ROI size
        setSelectedRoiCoordinates(drawingRoi);
        // console.log('ROI Mouse Up (Image Coords), Final ROI:', drawingRoi);
    } else {
        setSelectedRoiCoordinates(null);
        // console.log('ROI Mouse Up, but ROI was too small. Cleared.');
    }
    
    completeRoiSelection();
    setRoiStartPoint(null);
    setDrawingRoi(null);
  }, [isSelectingRoi, drawingRoi, roiStartPoint, setSelectedRoiCoordinates, completeRoiSelection]);

  useEffect(() => {
    const globalMouseMove = (event: MouseEvent) => {
        // Check if mouse is within the image container bounds if necessary,
        // or rely on the fact that drawing started within it.
        // For simplicity, we assume if roiStartPoint is set, we are drawing.
        handleMouseMoveForRoi(event);
    };

    const globalMouseUp = (event: MouseEvent) => {
        handleMouseUpForRoi(event);
    };

    if (isSelectingRoi && roiStartPoint) {
      document.addEventListener('mousemove', globalMouseMove);
      document.addEventListener('mouseup', globalMouseUp);
    } else {
      document.removeEventListener('mousemove', globalMouseMove);
      document.removeEventListener('mouseup', globalMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', globalMouseMove);
      document.removeEventListener('mouseup', globalMouseUp);
    };
  }, [isSelectingRoi, roiStartPoint, handleMouseMoveForRoi, handleMouseUpForRoi]);

  return {
    drawingRoi, // The current ROI being drawn
    handleMouseDownForRoi,
    // MouseMove and MouseUp are now primarily handled by global listeners when drawing starts
    // but ImageDisplay might still need to call them if it listens on its own div
    // For now, we assume the global listeners are sufficient once drawing starts.
    // If ImageDisplay's onMouseMove/onMouseUp need to call these, they can be returned.
    // Exposing them for the main div's onMouseMove and onMouseUp might still be useful
    // if global listeners are not preferred for some reason or for initial clicks.
    // Let's return them for flexibility.
    handleMouseMoveForRoi, 
    handleMouseUpForRoi,
    isDrawing: !!roiStartPoint, // Utility to know if drawing is active
  };
};