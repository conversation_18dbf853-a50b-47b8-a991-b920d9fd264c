# 前端环境配置说明

## 📋 概述

本项目支持多种部署环境，通过环境变量实现灵活的网络配置。配置文件已经过简化，只保留必要的环境配置。

## 📁 环境配置文件

- `.env.development` - 开发环境配置（默认）
- `.env.production` - 生产环境配置
- `.env.hotreload` - Docker热重载环境配置
- `.env.example` - 完整配置示例和说明
- `.env.lan.example` - 局域网访问配置模板

## 使用方法

### 1. 本地开发 (默认)

```bash
# 使用默认配置，后端运行在 localhost:8000
npm run dev
```

### 2. 连接远程后端

```bash
# 使用环境变量覆盖
VITE_BACKEND_URL=http://*************:8000 npm run dev

# 或使用预设脚本
npm run dev:remote
```

### 3. Docker热重载部署

```bash
# 构建用于Docker热重载的前端
npm run build:hotreload
```

### 4. 局域网访问

```bash
# 复制局域网配置模板
cp .env.lan.example .env.local

# 修改IP地址后启动
npm run dev
```

## 配置说明

### 🔧 主要环境变量

#### VITE_API_BASE_URL
- **开发环境**: `/api` (通过Vite代理)
- **生产环境**: `/api` (通过nginx代理)
- **Docker环境**: `/api` (通过容器代理)

#### VITE_BACKEND_URL
- **用途**: 开发环境Vite代理目标地址
- **默认值**: `http://localhost:9000`
- **局域网**: `http://your-ip:9000`

#### 网络配置变量
- `VITE_BACKEND_HOST` - 后端主机地址
- `VITE_BACKEND_PORT` - 后端端口号
- `VITE_FRONTEND_HOST` - 前端主机地址
- `VITE_FRONTEND_PORT` - 前端端口号

## 部署架构

### 本地开发
```
浏览器 → Vite开发服务器(5173) → Django(8000)
```

### Docker热重载
```
浏览器 → nginx容器(8080) → Django容器(8000)
```

### 服务器部署
```
浏览器 → 前端静态文件 → Django服务器(8000)
```

## 故障排除

### 1. 开发环境API 404
- 检查Django服务器是否运行在8000端口
- 验证 `VITE_BACKEND_URL` 配置
- 重启Vite开发服务器

### 2. Docker部署问题
- 确认使用了正确的构建命令 `npm run build:hotreload`
- 检查nginx配置是否正确代理到后端容器
- 验证Docker网络连接

### 3. 服务器部署问题
- 确认后端服务器地址配置正确
- 检查防火墙和网络访问权限
- 验证CORS配置
