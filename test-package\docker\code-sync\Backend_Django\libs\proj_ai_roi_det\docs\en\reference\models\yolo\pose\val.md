---
description: Explore the PoseValidator class for YOLO models. Learn how to extend DetectionValidator for pose validation with example code and detailed methods.
keywords: Ultralytics, YOLO, PoseValidator, pose validation, machine learning, object detection, keypoints, python code, AI, deep learning
---

# Reference for `ultralytics/models/yolo/pose/val.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/pose/val.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.pose.val.PoseValidator

<br><br>
