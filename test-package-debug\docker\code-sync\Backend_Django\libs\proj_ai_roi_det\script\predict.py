import shutil
from pathlib import Path
from ultralytics.models.yolo.model import <PERSON><PERSON><PERSON>
from ultralytics.engine.results import Results

if __name__ == "__main__":
    save_path = Path("../runs/AI_ROI_Det/V1.1.0./weights")
    test = Path("datasets/Train/val/images")
    shutil.rmtree(save_path, ignore_errors=True)
    save_path.mkdir(exist_ok=True)

    model = YOLO("../runs/AI_ROI_Det/V1.1.0./weights/best.pt")
    for idx, img in enumerate(list(test.iterdir())):
        result:list[Results] = model(
            img,
            conf=0.4,
        )
        result[0].save(filename=Path(save_path, f"{img.stem}_{idx}.jpg").as_posix())