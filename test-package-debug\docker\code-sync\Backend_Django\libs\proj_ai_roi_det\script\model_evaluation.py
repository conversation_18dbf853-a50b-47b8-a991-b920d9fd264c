import os
import cv2
import torch
import numpy as np
from ultralytics import YOLO
import matplotlib.pyplot as plt

class YOLOEvaluator:
    def __init__(self, config):
        """
        初始化评估器
        Args:
            config: 配置字典，包含所有必要的路径和参数
        """
        self.model = YOLO(config['model_path'])
        self.test_data_path = config['test_data_path']
        self.test_labels_path = config['test_labels_path']
        self.save_dir = config['save_dir']
        self.yaml_path = config['yaml_path']
        
        # 创建保存目录结构
        self.all_results_dir = os.path.join(self.save_dir, "all_results")
        self.diff_results_dir = os.path.join(self.save_dir, "different_results")
        os.makedirs(self.all_results_dir, exist_ok=True)
        os.makedirs(self.diff_results_dir, exist_ok=True)
        
        self.evaluation_stats = {
            'total_images': 0,
            'correct_predictions': 0,
            'incorrect_predictions': 0,
            'iou_scores': [],
            'confidence_scores': [],
            'predictions_per_image': [],
            'true_positives': 0,
            'false_positives': 0,
            'false_negatives': 0,
            'yolo_metrics': None
        }

    def _load_image_and_label(self, image_path, label_path):
        """加载图像和对应的YOLO格式标签"""
        # 读取图像
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 读取标签
        boxes = []
        if os.path.exists(label_path):
            with open(label_path, 'r') as f:
                for line in f:
                    class_id, x, y, w, h = map(float, line.strip().split())
                    # 转换YOLO格式到像素坐标
                    H, W = image.shape[:2]
                    x1 = int((x - w/2) * W)
                    y1 = int((y - h/2) * H)
                    x2 = int((x + w/2) * W)
                    y2 = int((y + h/2) * H)
                    boxes.append([x1, y1, x2, y2, int(class_id)])
                    
        return image, boxes

    def _check_label_overlap(self, label1, label2, padding=5):
        """
        检查两个标签是否重叠
        Args:
            label1: (x, y, width, height) 第一个标签的位置和大小
            label2: (x, y, width, height) 第二个标签的位置和大小
            padding: 标签之间的最小间距
        """
        x1, y1, w1, h1 = label1
        x2, y2, w2, h2 = label2
        
        return not (x1 + w1 + padding < x2 or 
                   x2 + w2 + padding < x1 or 
                   y1 + h1 + padding < y2 or 
                   y2 + h2 + padding < y1)

    def _find_best_label_position(self, box, label_size, img_size, existing_labels, base_offset=30):
        """
        为标签找到最佳位置
        Args:
            box: [x1, y1, x2, y2] 检测框坐标
            label_size: (width, height) 标签大小
            img_size: (width, height) 图像大小
            existing_labels: 已存在的标签列表，每个元素为(x, y, width, height)
            base_offset: 标签到检测框的基础偏移距离
        """
        x1, y1, x2, y2 = box
        label_width, label_height = label_size
        img_width, img_height = img_size
        
        # 定义可能的位置（右上、右下、左上、左下）和对应的连接点
        possible_positions = [
            # (label_x, label_y, line_start_x, line_start_y)
            (x2 + base_offset, y1, x2, y1),  # 右上
            (x2 + base_offset, y2, x2, y2),  # 右下
            (x1 - label_width - base_offset, y1, x1, y1),  # 左上
            (x1 - label_width - base_offset, y2, x1, y2),  # 左下
        ]
        
        # 尝试不同的偏移距离
        for offset_mult in range(1, 5):
            offset = base_offset * offset_mult
            for base_x, base_y, line_x, line_y in possible_positions:
                # 计算实际标签位置
                label_x = base_x
                label_y = base_y - label_height/2
                
                # 确保标签在图像范围内
                if label_x < 0:
                    label_x = 0
                elif label_x + label_width > img_width:
                    label_x = img_width - label_width
                
                if label_y < 0:
                    label_y = 0
                elif label_y + label_height > img_height:
                    label_y = img_height - label_height
                
                # 创建当前标签区域
                current_label = (label_x, label_y, label_width, label_height)
                
                # 检查是否与现有标签重叠
                overlap = False
                for existing_label in existing_labels:
                    if self._check_label_overlap(current_label, existing_label):
                        overlap = True
                        break
                
                # 如果没有重叠，返回这个位置
                if not overlap:
                    return (label_x, label_y), (line_x, line_y)
        
        # 如果所有位置都不行，返回默认位置（右上）
        return (x2 + base_offset, y1), (x2, y1)

    def _generate_distinct_colors(self, n):
        """
        生成n个有区分度的颜色
        Args:
            n: 需要的颜色数量
        Returns:
            colors: 颜色列表，每个颜色为(B,G,R)格式
        """
        colors = []
        for i in range(n):
            # 使用HSV色彩空间来生成颜色，可以更好地控制颜色的区分度
            hue = i * (360 / n)  # 在色相环上均匀分布
            saturation = 0.8 + np.random.rand() * 0.2  # 80-100%的饱和度
            value = 0.9 + np.random.rand() * 0.1  # 90-100%的明度
            
            # 转换HSV到RGB
            h = hue / 360
            s = saturation
            v = value
            
            if s == 0.0:
                rgb = (v, v, v)
            else:
                i = int(h * 6.0)
                f = (h * 6.0) - i
                p = v * (1.0 - s)
                q = v * (1.0 - s * f)
                t = v * (1.0 - s * (1.0 - f))
                i = i % 6
                
                if i == 0:
                    rgb = (v, t, p)
                elif i == 1:
                    rgb = (q, v, p)
                elif i == 2:
                    rgb = (p, v, t)
                elif i == 3:
                    rgb = (p, q, v)
                elif i == 4:
                    rgb = (t, p, v)
                else:
                    rgb = (v, p, q)
            
            # 转换到0-255范围并转为BGR格式
            bgr = (int(rgb[2] * 255), int(rgb[1] * 255), int(rgb[0] * 255))
            colors.append(bgr)
        
        return colors

    def _draw_boxes(self, image, boxes, color=(0, 255, 0), show_conf=False):
        """在图像上绘制边界框和置信度标签"""
        img = image.copy()
        img_height, img_width = img.shape[:2]
        
        # 生成不同的颜色
        if show_conf:
            distinct_colors = self._generate_distinct_colors(len(boxes))
        
        # 文本参数
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1
        padding = 5
        
        # 保存已绘制的标签位置
        existing_labels = []
        
        for i, box in enumerate(boxes):
            # 为每个框选择颜色
            box_color = distinct_colors[i] if show_conf else color
            
            x1, y1, x2, y2 = map(int, box[:4])
            # 绘制检测框
            cv2.rectangle(img, (x1, y1), (x2, y2), box_color, 2)
            
            # 如果需要显示置信度且box中包含置信度信息
            if show_conf and len(box) > 5:
                conf = box[5]
                text = f"{conf:.2f}"
                
                # 获取文本大小
                (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)
                
                # 计算标签框的大小
                label_width = text_width + 2 * padding
                label_height = text_height + 2 * padding
                
                # 找到最佳的标签位置
                (label_x, label_y), (line_start_x, line_start_y) = self._find_best_label_position(
                    [x1, y1, x2, y2],
                    (label_width, label_height),
                    (img_width, img_height),
                    existing_labels
                )
                
                # 保存这个标签的位置
                existing_labels.append((label_x, label_y, label_width, label_height))
                
                # 绘制连接线
                line_end = (label_x + label_width/2, label_y + label_height/2)
                pts = np.array([
                    (line_start_x, line_start_y),
                    line_end
                ], np.int32)
                cv2.polylines(img, [pts.reshape((-1, 1, 2))], False, box_color, 1, cv2.LINE_AA)
                
                # 绘制标签背景
                cv2.rectangle(img,
                             (int(label_x), int(label_y)),
                             (int(label_x + label_width), int(label_y + label_height)),
                             box_color,
                             -1)
                
                # 绘制标签文本
                text_x = int(label_x + padding)
                text_y = int(label_y + label_height - padding)
                cv2.putText(img,
                           text,
                           (text_x, text_y),
                           font,
                           font_scale,
                           (255, 255, 255),  # 白色文本保持不变，确保在所有背景色上都清晰可见
                           thickness)
                
                # 绘制装饰点
                dot_radius = 2
                cv2.circle(img, (line_start_x, line_start_y), dot_radius, box_color, -1)
                cv2.circle(img, (int(line_end[0]), int(line_end[1])), dot_radius, box_color, -1)
        
        return img

    def _calculate_iou(self, box1, box2):
        """计算两个边界框的IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        intersection = max(0, x2 - x1) * max(0, y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        
        return intersection / (area1 + area2 - intersection + 1e-6)

    def _compare_predictions(self, gt_boxes, pred_boxes, iou_threshold=0.5):
        """比较预测框和真实框是否匹配"""
        if len(gt_boxes) != len(pred_boxes):
            return False
        
        # 检查每个真实框是否都有匹配的预测框
        for gt_box in gt_boxes:
            found_match = False
            for pred_box in pred_boxes:
                if self._calculate_iou(gt_box[:4], pred_box[:4]) > iou_threshold:
                    found_match = True
                    break
            if not found_match:
                return False
        return True

    def _update_stats(self, gt_boxes, pred_boxes, is_different):
        """更新评估统计信息"""
        self.evaluation_stats['total_images'] += 1
        if not is_different:
            self.evaluation_stats['correct_predictions'] += 1
        
        # 统计每张图片的预测框数量
        self.evaluation_stats['predictions_per_image'].append(len(pred_boxes))
        
        # 计算所有预测框的IoU和置信度
        for pred_box in pred_boxes:
            max_iou = 0
            for gt_box in gt_boxes:
                iou = self._calculate_iou(pred_box[:4], gt_box[:4])
                max_iou = max(max_iou, iou)
            self.evaluation_stats['iou_scores'].append(max_iou)
            
            # 如果max_iou > 0.5，认为是真阳性
            if max_iou > 0.5:
                self.evaluation_stats['true_positives'] += 1
            else:
                self.evaluation_stats['false_positives'] += 1
        
        # 统计漏检（假阴性）
        for gt_box in gt_boxes:
            max_iou = 0
            for pred_box in pred_boxes:
                iou = self._calculate_iou(gt_box[:4], pred_box[:4])
                max_iou = max(max_iou, iou)
            if max_iou < 0.5:
                self.evaluation_stats['false_negatives'] += 1

    def run_yolo_validation(self):
        """运行YOLO的验证过程获取官方指标"""
        try:
            # 在验证时指定输出目录
            results = self.model.val(
                data=self.yaml_path,
                project=self.save_dir,  # 使用已配置的保存目录
                name='yolo_validation'  # 子文件夹名称
            )
            
            # 提取指标
            metrics = {}
            try:
                metrics = {
                    'precision': float(results.results_dict.get('metrics/precision(B)', 0)),
                    'recall': float(results.results_dict.get('metrics/recall(B)', 0)),
                    'mAP50': float(results.results_dict.get('metrics/mAP50(B)', 0)),
                    'mAP50-95': float(results.results_dict.get('metrics/mAP50-95(B)', 0))
                }
            except Exception as e:
                print(f"警告：无法提取YOLO指标: {e}")
                metrics = {
                    'precision': 0,
                    'recall': 0,
                    'mAP50': 0,
                    'mAP50-95': 0
                }
            
            self.evaluation_stats['yolo_metrics'] = metrics
            return metrics
        except Exception as e:
            print(f"警告：YOLO验证过程失败: {e}")
            return None

    def _plot_evaluation_metrics(self):
        """绘制评估指标可视化图表"""
        try:
            # 设置matplotlib样式
            plt.style.use('default')
            plt.rcParams['figure.facecolor'] = 'white'
            plt.rcParams['axes.facecolor'] = 'white'
            plt.rcParams['savefig.facecolor'] = 'white'
            
            # 创建图表
            fig = plt.figure(figsize=(20, 15))
            
            # 1. 测试集推理结果正确/错误的饼状图
            plt.subplot(231)
            correct = self.evaluation_stats['correct_predictions']
            total = self.evaluation_stats['total_images']
            incorrect = total - correct
            
            colors = ['#2ecc71', '#e74c3c']
            plt.pie([correct, incorrect], 
                    labels=['Correct', 'Incorrect'],
                    colors=colors,
                    autopct='%1.1f%%',
                    startangle=90,
                    shadow=True)
            plt.title('Test Set Prediction Distribution', pad=20, fontsize=12, fontweight='bold')
            
            # 2. YOLO官方指标条形图
            plt.subplot(232)
            if self.evaluation_stats['yolo_metrics']:
                metrics = self.evaluation_stats['yolo_metrics']
                names = ['Precision', 'Recall', 'mAP50', 'mAP50-95']
                values = [metrics['precision'], metrics['recall'], 
                         metrics['mAP50'], metrics['mAP50-95']]
                
                bars = plt.bar(names, values, color=['#3498db', '#2ecc71', '#e67e22', '#9b59b6'])
                plt.title('YOLO Metrics', pad=20, fontsize=12, fontweight='bold')
                plt.ylim(0, 1)
                plt.grid(True, alpha=0.3)
                
                for bar in bars:
                    height = bar.get_height()
                    plt.text(bar.get_x() + bar.get_width()/2., height,
                            f'{height:.3f}',
                            ha='center', va='bottom')
            
            # 3. IoU分布直方图
            plt.subplot(233)
            if len(self.evaluation_stats['iou_scores']) > 0:
                plt.hist(self.evaluation_stats['iou_scores'], 
                        bins=20, 
                        range=(0, 1), 
                        color='#3498db', 
                        alpha=0.7,
                        edgecolor='black')
                plt.title('IoU Distribution', pad=20, fontsize=12, fontweight='bold')
                plt.xlabel('IoU Value', fontsize=10)
                plt.ylabel('Count', fontsize=10)
                plt.grid(True, alpha=0.3)
                
                mean_iou = np.mean(self.evaluation_stats['iou_scores'])
                plt.axvline(x=mean_iou, color='#e74c3c', linestyle='--', 
                           label=f'Mean IoU: {mean_iou:.3f}')
                plt.legend(fontsize=10)
            
            # 4. 混淆矩阵热力图
            plt.subplot(234)
            confusion_matrix = np.array([
                [self.evaluation_stats['true_positives'], self.evaluation_stats['false_positives']],
                [self.evaluation_stats['false_negatives'], 0]
            ])
            
            im = plt.imshow(confusion_matrix, cmap='YlOrRd')
            plt.colorbar(im)
            plt.title('Confusion Matrix', pad=20, fontsize=12, fontweight='bold')
            plt.xticks([0, 1], ['True', 'False'])
            plt.yticks([0, 1], ['Positive', 'Negative'])
            
            for i in range(2):
                for j in range(2):
                    plt.text(j, i, confusion_matrix[i, j],
                            ha='center', va='center',
                            color='black' if confusion_matrix[i, j] < confusion_matrix.max()/2 else 'white',
                            fontsize=10)
            
            # 5. 预测框数量分布
            plt.subplot(235)
            if len(self.evaluation_stats['predictions_per_image']) > 0:
                plt.hist(self.evaluation_stats['predictions_per_image'], 
                        bins=min(20, max(self.evaluation_stats['predictions_per_image'])),
                        color='#2ecc71',
                        alpha=0.7,
                        edgecolor='black')
                plt.title('Predictions per Image', pad=20, fontsize=12, fontweight='bold')
                plt.xlabel('Number of Predictions', fontsize=10)
                plt.ylabel('Image Count', fontsize=10)
                plt.grid(True, alpha=0.3)
                
                mean_boxes = np.mean(self.evaluation_stats['predictions_per_image'])
                plt.axvline(x=mean_boxes, color='#e74c3c', linestyle='--',
                           label=f'Mean: {mean_boxes:.1f}')
                plt.legend(fontsize=10)
            
            # 添加总标题
            plt.suptitle('Model Evaluation Metrics', fontsize=16, fontweight='bold', y=0.95)
            
            # 调整布局
            plt.tight_layout(rect=[0, 0.03, 1, 0.95])
            
            # 确保保存目录存在
            os.makedirs(self.save_dir, exist_ok=True)
            
            # 保存统计图表
            stats_save_path = os.path.join(self.save_dir, 'evaluation_metrics.png')
            plt.savefig(stats_save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            
            print(f"Evaluation metrics visualization saved to: {stats_save_path}")
            return stats_save_path
        except Exception as e:
            print(f"Warning: Failed to generate evaluation metrics visualization: {e}")
            import traceback
            traceback.print_exc()
            return None

    def evaluate_single_image(self, image_path, save_all=True):
        """评估单张图像"""
        # 获取对应的标签路径
        label_path = os.path.join(
            self.test_labels_path,
            os.path.splitext(os.path.basename(image_path))[0] + '.txt'
        )
        
        # 加载图像和标签
        image, gt_boxes = self._load_image_and_label(image_path, label_path)
        
        # 模型预测
        results = self.model(image)
        pred_boxes = []
        for r in results[0].boxes:
            box = r.xyxy[0].cpu().numpy()  # 获取预测框坐标
            conf = r.conf[0].cpu().numpy()  # 获取置信度
            cls = r.cls[0].cpu().numpy()   # 获取类别
            pred_boxes.append([*box, cls, conf])  # 添加置信度到预测框信息中
            
        # 绘制原始标注和预测结果
        gt_image = self._draw_boxes(image, gt_boxes, color=(0, 255, 0))    # 绿色表示真实标注
        pred_image = self._draw_boxes(image, pred_boxes, color=(255, 0, 0), show_conf=True) # 红色表示预测结果,显示置信度
        
        # 创建对比图
        plt.figure(figsize=(15, 5))
        plt.subplot(121)
        plt.title('Ground Truth')
        plt.imshow(gt_image)
        plt.axis('off')
        
        plt.subplot(122)
        plt.title('Prediction')
        plt.imshow(pred_image)
        plt.axis('off')
        
        # 检查预测结果是否与真实标注一致
        is_different = not self._compare_predictions(gt_boxes, pred_boxes)
        
        # 保存结果
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        
        # 保存所有结果
        if save_all:
            all_save_path = os.path.join(self.all_results_dir, f"{image_name}_evaluation.png")
            plt.savefig(all_save_path, bbox_inches='tight', dpi=300)
            print(f"评估结果已保存到: {all_save_path}")
        
        # 如果预测结果与真实标注不一致，保存到差异目录
        if is_different:
            diff_save_path = os.path.join(self.diff_results_dir, f"{image_name}_evaluation.png")
            if not save_all:
                plt.savefig(diff_save_path, bbox_inches='tight', dpi=300)
            else:
                import shutil
                shutil.copy2(all_save_path, diff_save_path)
            print(f"发现差异！结果已保存到: {diff_save_path}")
            
        plt.close()
        
        # 更新统计信息
        self._update_stats(gt_boxes, pred_boxes, is_different)
        
        return is_different

    def evaluate_dataset(self):
        """评估整个测试集"""
        image_files = [f for f in os.listdir(self.test_data_path) 
                      if f.endswith(('.jpg', '.png', '.jpeg'))]
        
        total_images = len(image_files)
        different_count = 0
        
        print(f"\n开始评估测试集中的 {total_images} 张图像")
        print(f"所有结果将保存在: {self.all_results_dir}")
        print(f"不一致的结果将保存在: {self.diff_results_dir}")
        
        for i, image_file in enumerate(image_files, 1):
            print(f"\n评估进度: [{i}/{total_images}] 处理图像: {image_file}")
            image_path = os.path.join(self.test_data_path, image_file)
            if self.evaluate_single_image(image_path):
                different_count += 1
                
        print(f"\n评估完成！")
        print(f"总计评估图像: {total_images}")
        print(f"发现差异的图像数量: {different_count}")
        print(f"准确率: {(total_images - different_count) / total_images * 100:.2f}%")
        
        # 运行YOLO验证获取官方指标
        print("\n运行YOLO验证过程...")
        yolo_metrics = self.run_yolo_validation()
        
        # 生成并保存评估指标可视化
        stats_path = self._plot_evaluation_metrics()
        print(f"\n评估指标可视化已保存到: {stats_path}")
        
        # 打印详细的评估指标
        print("\n详细评估指标:")
        if yolo_metrics:
            print("\nYOLO官方指标:")
            print(f"Precision: {yolo_metrics['precision']:.4f}")
            print(f"Recall: {yolo_metrics['recall']:.4f}")
            print(f"mAP50: {yolo_metrics['mAP50']:.4f}")
            print(f"mAP50-95: {yolo_metrics['mAP50-95']:.4f}")
        
        print("\n自定义评估指标:")
        print(f"平均IoU: {np.mean(self.evaluation_stats['iou_scores']):.4f}")
        print(f"每张图片平均预测框数量: {np.mean(self.evaluation_stats['predictions_per_image']):.2f}")

# 使用示例
if __name__ == "__main__":
    # 基础路径配置
    base_path = "/home/<USER>/my_proj_ai_roi_det/proj_ai_roi_det"
    
    # 配置字典
    config = {
        # 数据集相关路径
        'dataset_root': os.path.join(base_path, "mindeo/data_after_processed/DataSet_YOLO"),
        'test_data_path': os.path.join(base_path, "mindeo/data_after_processed/DataSet_YOLO/test/images"),
        'test_labels_path': os.path.join(base_path, "mindeo/data_after_processed/DataSet_YOLO/test/labels"),
        
        # 模型相关路径
        'model_path': os.path.join(base_path, "runs/AI_ROI_Det/V1.1.0.22/weights/best.pt"),
        
        # 评估结果保存路径
        'save_dir': os.path.join(base_path, "runs/AI_ROI_Det/V1.1.0.22/evaluation_results"),
        
        # 数据集配置文件路径
        'yaml_path': os.path.join(base_path, "mindeo/roi.yaml")
    }
    
    # 打印配置信息
    print("\n=== 评估配置信息 ===")
    for key, value in config.items():
        print(f"{key}: {value}")
        # 检查路径是否存在
        if key != 'save_dir':  # save_dir 会自动创建
            if not os.path.exists(value):
                print(f"警告: 路径不存在 - {value}")
    print("==================\n")
    
    # 创建评估器实例
    evaluator = YOLOEvaluator(config)
    
    # 评估整个测试集
    evaluator.evaluate_dataset() 