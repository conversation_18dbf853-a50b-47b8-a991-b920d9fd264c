// Frontend/src/components/ImageDisplay/hooks/useImageDropUpload.ts
import { useState, useCallback } from 'react';
import { useImageWorkspace } from '../../../contexts/ImageWorkspaceContext'; // Adjusted path

// Define allowed MIME types
const ALLOWED_IMAGE_TYPES = [
    'image/jpeg',
    'image/png',
    'image/bmp',
    'image/webp',
    'image/avif'
];

// Helper function to process a single FileSystemFileEntry
const getFileFromFileEntry = (entry: FileSystemFileEntry): Promise<File | null> => {
  return new Promise((resolve, reject) => {
    entry.file(
      (file) => {
        if (ALLOWED_IMAGE_TYPES.includes(file.type)) {
          resolve(file);
        } else {
          resolve(null); // Not a supported image type or not an image
        }
      },
      (err) => {
        console.error('Error getting file from FileSystemFileEntry:', err);
        reject(err); // Propagate error
      }
    );
  });
};

// Helper function to recursively read a directory
const readDirectoryEntries = async (directoryEntry: FileSystemDirectoryEntry): Promise<File[]> => {
  const reader = directoryEntry.createReader();
  let allEntries: FileSystemEntry[] = [];

  return new Promise<File[]>(async (resolve, reject) => {
    const readBatch = async () => {
      try {
        reader.readEntries(async (entries) => {
          if (entries.length > 0) {
            allEntries = allEntries.concat(entries);
            await readBatch(); // Read next batch
          } else {
            // All entries read for this directory level
            const files: File[] = [];
            for (const entry of allEntries) {
              if (entry.isFile) {
                const file = await getFileFromFileEntry(entry as FileSystemFileEntry);
                if (file) {
                  files.push(file);
                }
              } else if (entry.isDirectory) {
                const subDirFiles = await readDirectoryEntries(entry as FileSystemDirectoryEntry);
                files.push(...subDirFiles);
              }
            }
            resolve(files.sort((a, b) => a.name.localeCompare(b.name)));
          }
        }, (err) => {
          console.error('Error reading directory entries:', err);
          reject(err); // Propagate error
        });
      } catch (error) {
        console.error('Error in readBatch:', error);
        reject(error);
      }
    };
    await readBatch();
  });
};

export const useImageDropUpload = () => {
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const {
    loadImage,
    loadFolder,
    imageList,
    currentImageIndex,
    setCurrentImageIndex,
  } = useImageWorkspace();

  const handleDragEnter = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDraggingOver(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    // Check if the mouse is truly leaving the component, not just moving over a child
    if (!event.currentTarget.contains(event.relatedTarget as Node)) {
      setIsDraggingOver(false);
    }
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDraggingOver(true); // Keep it true while dragging over
  }, []);

  const handleDrop = useCallback(async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDraggingOver(false);

    const dtItems = event.dataTransfer.items;
    const dtFiles = event.dataTransfer.files;
    let processedViaItems = false;

    if (dtItems && dtItems.length > 0) {
      const webkitEntries = Array.from(dtItems)
        .map(item => item.webkitGetAsEntry())
        .filter(entry => entry !== null) as (FileSystemFileEntry | FileSystemDirectoryEntry)[];

      if (webkitEntries.length > 0) {
        const directoryEntries = webkitEntries.filter(entry => entry.isDirectory) as FileSystemDirectoryEntry[];
        if (directoryEntries.length > 0) {
          try {
            const imageFiles = await readDirectoryEntries(directoryEntries[0]);
            if (imageFiles.length > 0) {
              await loadFolder(imageFiles as any); // TODO: Check 'as any'
              processedViaItems = true;
            } else {
              console.warn('No supported image files found in the dropped folder.');
            }
          } catch (error) {
            console.error('Error processing dropped folder:', error);
          }
        } else if (webkitEntries.length === 1 && webkitEntries[0].isFile) {
          try {
            const file = await getFileFromFileEntry(webkitEntries[0] as FileSystemFileEntry);
            if (file) {
              await loadImage(file);
              processedViaItems = true;
            }
          } catch (error) {
            console.error('Error processing dropped file entry:', error);
          }
        }
      }
    }

    if (!processedViaItems && dtFiles && dtFiles.length > 0) {
      const imageFiles = Array.from(dtFiles).filter(file => ALLOWED_IMAGE_TYPES.includes(file.type));
      
      if (imageFiles.length === 1) {
        await loadImage(imageFiles[0]);
      } else if (imageFiles.length > 1) {
        await loadFolder(imageFiles as any); // TODO: Check 'as any'
      } else if (imageFiles.length === 0 && dtFiles.length > 0) {
        console.warn('No supported image files found in the drop (using files property).');
      }
    }
  }, [loadImage, loadFolder]);

  const handlePrevClick = useCallback(() => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    }
  }, [currentImageIndex, setCurrentImageIndex]);

  const handleNextClick = useCallback(() => {
    if (imageList.length > 0 && currentImageIndex < imageList.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
    }
  }, [currentImageIndex, imageList, setCurrentImageIndex]);
  
  const showNavigationButtons = imageList.length > 1;

  return {
    isDraggingOver,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    imageList, // Pass through for UI updates if needed directly
    currentImageIndex, // Pass through for UI updates
    handlePrevClick,
    handleNextClick,
    showNavigationButtons,
    // loadImage and loadFolder are used internally, not typically returned unless main component needs to trigger them manually outside of drop
  };
};