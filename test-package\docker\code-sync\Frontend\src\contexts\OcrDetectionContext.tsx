import React, { createContext, useState, useContext, ReactNode, useMemo } from 'react';
import { DisplayableDetection } from './ImageWorkspaceContext'; // Import DisplayableDetection
import { OcrTask } from '../services/api'; // Import OcrTask type

// --- Types ---

export type OcrDisplayMode = 'composite' | 'direct_render'; // Added

// This could be expanded if more OCR-specific params are needed globally
export interface OcrDetectionState {
  selectedTaskValue: string; // e.g., 'vehicle_license_plate_cn'
  ocrProcessingResults: DisplayableDetection[] | null; // New: For PanelInfo
  selectedDisplayMode: OcrDisplayMode; // Added
  batchProcessingInterval: number; // Added for multi-image processing
  autoInferenceEnabled: boolean; // 切换图片时是否自动执行检测
  isInferring: boolean; // 单图推理状态
  isBatchProcessing: boolean; // 批量处理状态
  // Add states for dynamic task loading to be shared via context
  availableOcrTasks: OcrTask[];
  ocrTasksLoading: boolean;
  ocrTasksError: string | null;
}

interface OcrDetectionContextType extends OcrDetectionState {
  setSelectedTaskValue: (value: string) => void;
  setOcrProcessingResults: (results: DisplayableDetection[] | null) => void; // New setter
  setSelectedDisplayMode: (mode: OcrDisplayMode) => void; // Added
  setBatchProcessingInterval: (interval: number) => void; // Added
  setAutoInferenceEnabled: (enabled: boolean) => void; // 设置切换时智能检测状态
  setIsInferring: (inferring: boolean) => void; // 设置单图推理状态
  setIsBatchProcessing: (processing: boolean) => void; // 设置批量处理状态
  // Add setters for dynamic task loading states
  setAvailableOcrTasks: (tasks: OcrTask[]) => void;
  setOcrTasksLoading: (loading: boolean) => void;
  setOcrTasksError: (error: string | null) => void;
  fetchOcrTasks: (trigger: 'initial' | 'manual') => Promise<void>; // 统一的OCR任务获取方法
}

// --- Context Creation ---
const OcrDetectionContext = createContext<OcrDetectionContextType | undefined>(undefined);

// --- Provider Component ---
interface OcrDetectionProviderProps {
  children: ReactNode;
}

// Define available OCR tasks - might be better in a shared constants file later
// For now, keeping it here to be used by the provider for default and potentially by consumers.
// export interface OcrTaskOption { // Exporting for type safety in PanelInfo - NO LONGER NEEDED HERE
//     label: string;
//     value: string;
// }

// export const OCR_TASK_OPTIONS: OcrTaskOption[] = [ // NO LONGER NEEDED
//     { label: '通用OCR检测', value: 'general_ocr_ch' },
//     { label: '国内车牌检测', value: 'vehicle_license_plate_cn' },
//     // { label: '通用文档识别', value: 'general_document_ocr' }, // Example for future
// ];


export const OcrDetectionProvider: React.FC<OcrDetectionProviderProps> = ({ children }) => {
  // selectedTaskValue will be initialized after fetching tasks in OcrDetectionPanel
  const [selectedTaskValue, setSelectedTaskValue] = useState<string>('');
  const [ocrProcessingResults, setOcrProcessingResultsState] = useState<DisplayableDetection[] | null>(null); // New state
  const [selectedDisplayMode, setSelectedDisplayModeState] = useState<OcrDisplayMode>('composite'); // 默认composite
  const [batchProcessingInterval, setBatchProcessingIntervalState] = useState<number>(1000); // Added, default 1000ms (1s)
  const [autoInferenceEnabled, setAutoInferenceEnabled] = useState<boolean>(false); // 默认不启用切换时智能检测
  const [isInferring, setIsInferring] = useState<boolean>(false); // 默认不在推理中
  const [isBatchProcessing, setIsBatchProcessing] = useState<boolean>(false); // 默认不在批量处理中

  // States for dynamic task loading - managed by context now
  const [availableOcrTasks, setAvailableOcrTasksState] = useState<OcrTask[]>([]);
  const [ocrTasksLoading, setOcrTasksLoadingState] = useState<boolean>(false); // Initialize ocrTasksLoading to false
  const [ocrTasksError, setOcrTasksErrorState] = useState<string | null>(null);

  const setOcrProcessingResults = (results: DisplayableDetection[] | null) => {
    setOcrProcessingResultsState(results);
  };

  const setSelectedDisplayMode = (mode: OcrDisplayMode) => { // Added
    setSelectedDisplayModeState(mode);
  };

  const setBatchProcessingInterval = (interval: number) => { // Added
    setBatchProcessingIntervalState(interval);
  };

  // Setters for new context states
  const setAvailableOcrTasks = (tasks: OcrTask[]) => {
      setAvailableOcrTasksState(tasks);
  };
  const setOcrTasksLoading = (loading: boolean) => {
      setOcrTasksLoadingState(loading);
  };
  const setOcrTasksError = (error: string | null) => {
      setOcrTasksErrorState(error);
  };

  // 获取OCR任务列表的统一方法
  const fetchOcrTasks = React.useCallback(async (trigger: 'initial' | 'manual') => {
    setOcrTasksLoadingState(true);
    setOcrTasksErrorState(null);

    // 根据触发类型记录日志
    console.log(`[OcrDetectionContext] 开始获取OCR任务列表 (触发方式: ${trigger})`);

    try {
      // 动态导入API函数以避免循环依赖
      const { getOcrTasks } = await import('../services/api');

      const tasks = await getOcrTasks();
      setAvailableOcrTasksState(tasks);

      // 设置默认选择的任务
      if (!selectedTaskValue && tasks.length > 0) {
        setSelectedTaskValue(tasks[0].task_name);
      }

      console.log(`[OcrDetectionContext] OCR任务获取成功 (${trigger}): 共 ${tasks.length} 个任务`);

    } catch (error) {
      console.error(`[OcrDetectionContext] 获取OCR任务失败 (${trigger}):`, error);
      setOcrTasksErrorState('获取OCR任务列表失败，请稍后重试');
      setAvailableOcrTasksState([]);
    } finally {
      setOcrTasksLoadingState(false);
    }
  }, [selectedTaskValue]);

  const contextValue = useMemo(() => ({
    selectedTaskValue,
    setSelectedTaskValue,
    ocrProcessingResults,
    setOcrProcessingResults,
    selectedDisplayMode, // Added
    setSelectedDisplayMode, // Added
    batchProcessingInterval, // Added
    setBatchProcessingInterval, // Added
    autoInferenceEnabled, // 切换时智能检测状态
    setAutoInferenceEnabled, // 设置切换时智能检测状态
    isInferring, // 单图推理状态
    setIsInferring, // 设置单图推理状态
    isBatchProcessing, // 批量处理状态
    setIsBatchProcessing, // 设置批量处理状态
    // Include new states and setters in context value
    availableOcrTasks,
    setAvailableOcrTasks,
    ocrTasksLoading,
    setOcrTasksLoading,
    ocrTasksError,
    setOcrTasksError,
    fetchOcrTasks, // 添加统一的任务获取方法
  }), [
      selectedTaskValue,
      ocrProcessingResults,
      selectedDisplayMode,
      batchProcessingInterval,
      autoInferenceEnabled,
      isInferring, // Add to dependency array
      isBatchProcessing, // Add to dependency array
      availableOcrTasks, // Add to dependency array
      ocrTasksLoading,   // Add to dependency array
      ocrTasksError,     // Add to dependency array
      fetchOcrTasks,     // Add to dependency array
    ]);

  return (
    <OcrDetectionContext.Provider value={contextValue}>
      {children}
    </OcrDetectionContext.Provider>
  );
};

// --- Hook for consuming context ---
export const useOcrDetectionParams = (): OcrDetectionContextType => {
  const context = useContext(OcrDetectionContext);
  if (context === undefined) {
    throw new Error('useOcrDetectionParams must be used within an OcrDetectionProvider');
  }
  return context;
};