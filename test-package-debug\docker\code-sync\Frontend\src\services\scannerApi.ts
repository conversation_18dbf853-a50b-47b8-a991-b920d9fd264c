import axios from 'axios';

// 根据环境配置API基础URL
// API_BASE_URL is handled by Vite proxy now
const apiClient = axios.create({
  withCredentials: true, // 允许跨域请求携带cookie
});

// --- Types ---
interface ApiResponse {
  status: 'success' | 'error';
  message: string;
}

interface StatusResponse {
  is_connected: boolean;
  is_streaming: boolean;
  device_ip: string | null;
}

// --- API Functions ---

export const scannerConnect = async (ip_address: string): Promise<ApiResponse> => {
  const response = await apiClient.post('/api/vision/scanner/connect/', { ip_address });
  return response.data;
};

export const scannerDisconnect = async (): Promise<ApiResponse> => {
  const response = await apiClient.post('/api/vision/scanner/disconnect/');
  return response.data;
};

export const scannerStartStream = async (): Promise<ApiResponse> => {
  const response = await apiClient.post('/api/vision/scanner/start/');
  return response.data;
};

export const scannerStopStream = async (): Promise<ApiResponse> => {
  const response = await apiClient.post('/api/vision/scanner/stop/');
  return response.data;
};

export const getScannerStatus = async (): Promise<StatusResponse> => {
  const response = await apiClient.get('/api/vision/scanner/status/');
  return response.data;
};