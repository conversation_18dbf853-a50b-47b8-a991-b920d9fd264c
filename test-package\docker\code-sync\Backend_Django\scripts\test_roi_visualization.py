#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ROI可视化测试脚本 - 简化版
专门测试ROI区域对特征匹配的影响
"""

import os
import sys
import cv2
import numpy as np
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'vision_app'))

from vision_app.feature_matching_onnxruntime_model_predictor import ModelBasedFeatureMatcher

def draw_roi_and_matches(img1, img2, matches, roi=None):
    """绘制ROI和匹配结果"""
    # 复制图像
    img1_vis = img1.copy()
    img2_vis = img2.copy()
    
    # 绘制ROI框
    if roi:
        x, y, w, h = roi['x'], roi['y'], roi['width'], roi['height']
        cv2.rectangle(img1_vis, (x, y), (x+w, y+h), (0, 0, 255), 3)
        cv2.putText(img1_vis, "ROI", (x, y-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    
    # 绘制匹配点
    for i, match in enumerate(matches):
        pt1 = tuple(map(int, match["point1"]))
        pt2 = tuple(map(int, match["point2"]))
        
        # 根据匹配质量选择颜色
        distance = match.get("distance", 0)
        if distance < 0.3:
            color = (0, 255, 0)  # 绿色 - 高质量
        elif distance < 0.5:
            color = (0, 255, 255)  # 黄色 - 中等质量
        else:
            color = (0, 0, 255)  # 红色 - 低质量
        
        # 绘制关键点
        cv2.circle(img1_vis, pt1, 4, color, -1)
        cv2.circle(img2_vis, pt2, 4, color, -1)
        
        # 添加序号
        cv2.putText(img1_vis, str(i+1), (pt1[0]+5, pt1[1]-5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        cv2.putText(img2_vis, str(i+1), (pt2[0]+5, pt2[1]-5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
    
    # 创建拼接图像
    h1, w1 = img1_vis.shape[:2]
    h2, w2 = img2_vis.shape[:2]
    max_h = max(h1, h2)
    
    # 调整图像尺寸
    combined = np.zeros((max_h, w1 + w2, 3), dtype=np.uint8)
    combined[:h1, :w1] = img1_vis
    combined[:h2, w1:w1+w2] = img2_vis
    
    # 绘制匹配线
    for match in matches:
        pt1 = tuple(map(int, match["point1"]))
        pt2 = tuple(map(int, [match["point2"][0] + w1, match["point2"][1]]))
        
        distance = match.get("distance", 0)
        if distance < 0.3:
            color = (0, 255, 0)
        elif distance < 0.5:
            color = (0, 255, 255)
        else:
            color = (0, 0, 255)
        
        cv2.line(combined, pt1, pt2, color, 1)
    
    return combined

def test_roi_scenarios():
    """测试不同ROI场景"""
    print("🎨 ROI可视化测试")
    print("="*50)
    
    # 检查测试图片
    image1_path = "sample4/1.bmp"
    image2_path = "sample4/3.bmp"
    model_path = "superpoint.onnx"
    
    for path in [image1_path, image2_path, model_path]:
        if not os.path.exists(path):
            print(f"❌ 文件不存在: {path}")
            return False
    
    # 读取图片
    img1 = cv2.imread(image1_path)
    img2 = cv2.imread(image2_path)
    
    print(f"图像尺寸: {img1.shape}")
    
    # 创建预测器
    predictor = ModelBasedFeatureMatcher(model_path)
    
    # 测试场景
    scenarios = [
        {"name": "全图匹配", "roi": None},
        {"name": "中心区域", "roi": {"x": 400, "y": 300, "width": 480, "height": 424}},
        {"name": "左上角", "roi": {"x": 100, "y": 100, "width": 300, "height": 200}},
        {"name": "右下角", "roi": {"x": 800, "y": 600, "width": 400, "height": 300}}
    ]
    
    results = []
    
    for i, scenario in enumerate(scenarios):
        print(f"\n📋 场景 {i+1}: {scenario['name']}")
        print("-" * 30)
        
        # 执行预测
        result = predictor.predict(
            template_image=img1,
            target_image=img2,
            template_roi=scenario['roi'],
            keypoints_count=100,
            nms_grid_size=4,
            nms_threshold=8.0/255.0,
            match_ratio_threshold=0.8,
            min_match_count=4,
            ransac_threshold=3.0
        )
        
        if result['status'] == 'success':
            matches = result['data']['matches']
            match_count = len(matches)
            print(f"✅ 成功找到 {match_count} 个匹配点")
            
            # 生成可视化
            vis_image = draw_roi_and_matches(img1, img2, matches, scenario['roi'])
            
            # 保存图像
            output_path = f"roi_test_{i+1}_{scenario['name']}.jpg"
            cv2.imwrite(output_path, vis_image)
            print(f"📁 保存图像: {output_path}")
            
            # 匹配质量分析
            if matches:
                distances = [m.get('distance', 0) for m in matches]
                avg_distance = np.mean(distances)
                high_quality = sum(1 for d in distances if d < 0.3)
                
                print(f"📊 质量统计:")
                print(f"  平均距离: {avg_distance:.4f}")
                print(f"  高质量匹配: {high_quality}/{match_count}")
                
                results.append({
                    'scenario': scenario['name'],
                    'roi': scenario['roi'],
                    'match_count': match_count,
                    'avg_distance': avg_distance,
                    'high_quality': high_quality
                })
            
        else:
            print(f"❌ 匹配失败: {result.get('message', '未知错误')}")
            results.append({
                'scenario': scenario['name'],
                'roi': scenario['roi'],
                'match_count': 0,
                'avg_distance': 0,
                'high_quality': 0
            })
    
    # 生成对比报告
    print(f"\n{'='*60}")
    print("📋 ROI影响分析报告")
    print(f"{'='*60}")
    
    print(f"{'场景':<12} {'匹配数':<8} {'平均距离':<10} {'高质量':<8} {'ROI尺寸':<15}")
    print("-" * 60)
    
    for result in results:
        roi_size = ""
        if result['roi']:
            w, h = result['roi']['width'], result['roi']['height']
            roi_size = f"{w}x{h}"
        else:
            roi_size = "全图"
        
        print(f"{result['scenario']:<12} {result['match_count']:<8} "
              f"{result['avg_distance']:<10.4f} {result['high_quality']:<8} {roi_size:<15}")
    
    # 分析结论
    if len(results) > 1:
        full_image_result = results[0] if results[0]['scenario'] == '全图匹配' else None
        roi_results = [r for r in results if r['roi'] is not None]
        
        if full_image_result and roi_results:
            print(f"\n🔍 分析结论:")
            full_matches = full_image_result['match_count']
            avg_roi_matches = np.mean([r['match_count'] for r in roi_results])
            
            print(f"  全图匹配数: {full_matches}")
            print(f"  ROI平均匹配数: {avg_roi_matches:.1f}")
            
            if full_matches > avg_roi_matches:
                print(f"  📈 全图匹配效果更好 (+{full_matches-avg_roi_matches:.1f}个匹配点)")
                print(f"  💡 建议: 优先使用全图匹配，ROI可用于后处理过滤")
            else:
                print(f"  📊 ROI匹配效果相当")
                print(f"  💡 建议: 可根据具体需求选择ROI区域")
    
    print(f"\n✅ 可视化文件已生成，请查看 roi_test_*.jpg 图像")
    return True

def main():
    """主函数"""
    os.chdir(os.path.dirname(__file__))
    success = test_roi_scenarios()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 