---
description: Explore the comprehensive reference for ultralytics.utils in the Ultralytics library. Enhance your ML workflow with these utility functions.
keywords: Ultralytics, utils, TQDM, Python, ML, Machine Learning utilities, YOLO, threading, logging, yaml, settings
---

# Reference for `ultralytics/utils/__init__.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/\_\_init\_\_.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/__init__.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/__init__.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.TQDM

<br><br><hr><br>

## ::: ultralytics.utils.SimpleClass

<br><br><hr><br>

## ::: ultralytics.utils.IterableSimpleNamespace

<br><br><hr><br>

## ::: ultralytics.utils.ThreadingLocked

<br><br><hr><br>

## ::: ultralytics.utils.TryExcept

<br><br><hr><br>

## ::: ultralytics.utils.Retry

<br><br><hr><br>

## ::: ultralytics.utils.JSONDict

<br><br><hr><br>

## ::: ultralytics.utils.SettingsManager

<br><br><hr><br>

## ::: ultralytics.utils.plt_settings

<br><br><hr><br>

## ::: ultralytics.utils.set_logging

<br><br><hr><br>

## ::: ultralytics.utils.emojis

<br><br><hr><br>

## ::: ultralytics.utils.yaml_save

<br><br><hr><br>

## ::: ultralytics.utils.yaml_load

<br><br><hr><br>

## ::: ultralytics.utils.yaml_print

<br><br><hr><br>

## ::: ultralytics.utils.read_device_model

<br><br><hr><br>

## ::: ultralytics.utils.is_ubuntu

<br><br><hr><br>

## ::: ultralytics.utils.is_colab

<br><br><hr><br>

## ::: ultralytics.utils.is_kaggle

<br><br><hr><br>

## ::: ultralytics.utils.is_jupyter

<br><br><hr><br>

## ::: ultralytics.utils.is_docker

<br><br><hr><br>

## ::: ultralytics.utils.is_raspberrypi

<br><br><hr><br>

## ::: ultralytics.utils.is_jetson

<br><br><hr><br>

## ::: ultralytics.utils.is_online

<br><br><hr><br>

## ::: ultralytics.utils.is_pip_package

<br><br><hr><br>

## ::: ultralytics.utils.is_dir_writeable

<br><br><hr><br>

## ::: ultralytics.utils.is_pytest_running

<br><br><hr><br>

## ::: ultralytics.utils.is_github_action_running

<br><br><hr><br>

## ::: ultralytics.utils.get_git_dir

<br><br><hr><br>

## ::: ultralytics.utils.is_git_dir

<br><br><hr><br>

## ::: ultralytics.utils.get_git_origin_url

<br><br><hr><br>

## ::: ultralytics.utils.get_git_branch

<br><br><hr><br>

## ::: ultralytics.utils.get_default_args

<br><br><hr><br>

## ::: ultralytics.utils.get_ubuntu_version

<br><br><hr><br>

## ::: ultralytics.utils.get_user_config_dir

<br><br><hr><br>

## ::: ultralytics.utils.colorstr

<br><br><hr><br>

## ::: ultralytics.utils.remove_colorstr

<br><br><hr><br>

## ::: ultralytics.utils.threaded

<br><br><hr><br>

## ::: ultralytics.utils.set_sentry

<br><br><hr><br>

## ::: ultralytics.utils.deprecation_warn

<br><br><hr><br>

## ::: ultralytics.utils.clean_url

<br><br><hr><br>

## ::: ultralytics.utils.url2file

<br><br><hr><br>

## ::: ultralytics.utils.vscode_msg

<br><br>
