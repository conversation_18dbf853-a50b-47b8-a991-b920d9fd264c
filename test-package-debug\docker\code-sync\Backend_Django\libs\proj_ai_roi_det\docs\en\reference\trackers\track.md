---
description: Explore the track.py script for Ultralytics object tracking. Learn how on_predict_start, on_predict_postprocess_end, and register_tracker functions work.
keywords: Ultralytics, YOLO, object tracking, track.py, on_predict_start, on_predict_postprocess_end, register_tracker
---

# Reference for `ultralytics/trackers/track.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/track.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/track.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/track.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.trackers.track.on_predict_start

<br><br><hr><br>

## ::: ultralytics.trackers.track.on_predict_postprocess_end

<br><br><hr><br>

## ::: ultralytics.trackers.track.register_tracker

<br><br>
