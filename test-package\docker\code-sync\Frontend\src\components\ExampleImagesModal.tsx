import React, { useState, useEffect, useRef } from 'react';
import { Modal, Tabs, Card, Image, Button, Spin, Alert, Checkbox, Row, Col, Typography, Space, Tag, App, FloatButton } from 'antd';
import type { TabsProps } from 'antd';
import { PictureOutlined, DownloadOutlined, EyeOutlined, VerticalAlignTopOutlined } from '@ant-design/icons';
import { getExampleImagesForDashboard, ExampleImage, ExampleImagesDashboardResponse } from '../services/api';
import { useImageWorkspace } from '../contexts/ImageWorkspaceContext';

const { Text } = Typography;

interface ExampleImagesModalProps {
  visible: boolean;
  onCancel: () => void;
  selectionMode?: 'single' | 'multiple'; // 'single' for one image, 'multiple' for batch
  onSelectImage?: (imageFile: File) => void; // Callback for single image selection
}

const ExampleImagesModal: React.FC<ExampleImagesModalProps> = ({
  visible,
  onCancel,
  selectionMode = 'multiple', // Default to multiple selection
  onSelectImage,
}) => {
  const [loading, setLoading] = useState(false);
  const [exampleImages, setExampleImages] = useState<ExampleImagesDashboardResponse | null>(null);
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const [loadingImages, setLoadingImages] = useState(false);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const { loadImage, loadFolder } = useImageWorkspace();
  const { message } = App.useApp();

  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 自定义滚动条样式
  const scrollContainerStyle: React.CSSProperties = {
    flex: 1,
    overflowY: 'auto',
    overflowX: 'hidden',
    padding: '0 4px',
    scrollBehavior: 'smooth',
    scrollbarWidth: 'thin',
    scrollbarColor: '#bfbfbf #f5f5f5'
  };

  // 获取示例图片列表
  const fetchExampleImages = async () => {
    setLoading(true);
    try {
      const data = await getExampleImagesForDashboard();
      setExampleImages(data);
      console.log('示例图片列表获取成功:', data);
    } catch (error) {
      console.error('获取示例图片列表失败:', error);
      message.error('获取示例图片列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理滚动事件
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop } = e.currentTarget;
    setShowBackToTop(scrollTop > 200); // 滚动超过200px时显示回到顶部按钮
  };

  // 回到顶部
  const scrollToTop = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  // 当模态框打开时获取数据
  useEffect(() => {
    if (visible) {
      fetchExampleImages();
      setSelectedImages(new Set()); // 重置多选
      setSelectedImageId(null); // 重置单选
      setShowBackToTop(false); // 重置回到顶部按钮状态
    }
  }, [visible]);

  // 处理图片选择
  const handleImageSelect = (imageId: string, checked: boolean) => {
    const newSelected = new Set(selectedImages);
    if (checked) {
      newSelected.add(imageId);
    } else {
      newSelected.delete(imageId);
    }
    setSelectedImages(newSelected);
  };

  // 全选/取消全选某个分类
  const handleCategorySelectAll = (category: keyof ExampleImagesDashboardResponse, checked: boolean) => {
    if (!exampleImages) return;

    const newSelected = new Set(selectedImages);
    exampleImages[category].forEach(image => {
      if (checked) {
        newSelected.add(image.id.toString());
      } else {
        newSelected.delete(image.id.toString());
      }
    });
    setSelectedImages(newSelected);
  };

  // 加载选中的图片到工作区
  const handleLoadSelectedImages = async () => {
    if (selectionMode === 'single') {
        console.warn("handleLoadSelectedImages called in single selection mode. This should not happen.");
        return;
    }

    if (selectedImages.size === 0) {
      message.warning('请先选择要加载的图片');
      return;
    }

    if (!exampleImages) return;

    setLoadingImages(true);
    try {
      // 获取所有选中的图片信息
      const allImages = [
        ...exampleImages.barcode,
        ...exampleImages.ocr,
        ...exampleImages.ai_restored
      ];

      const selectedImageInfos = allImages.filter(image => selectedImages.has(image.id.toString()));

      // 批量下载图片并转换为File对象
      const downloadPromises = selectedImageInfos.map(async (imageInfo) => {
        try {
          // 构建完整的图片URL - 注意去掉API前缀重复
          const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';
          const fullUrl = `${baseUrl.replace('/api', '')}${imageInfo.url}`;

          // 获取图片文件
          const response = await fetch(fullUrl);
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const blob = await response.blob();
          const file = new File([blob], imageInfo.name, { type: blob.type });

          console.log(`成功下载示例图片: ${imageInfo.name}`);
          return file;
        } catch (error) {
          console.error(`下载示例图片失败: ${imageInfo.name}`, error);
          message.error(`下载图片 ${imageInfo.name} 失败`);
          return null;
        }
      });

      // 等待所有图片下载完成
      const downloadedFiles = await Promise.all(downloadPromises);
      const validFiles = downloadedFiles.filter((file): file is File => file !== null);

      if (validFiles.length === 0) {
        message.error('没有成功下载任何图片');
        return;
      }

      // 如果只有一张图片，使用loadImage
      if (validFiles.length === 1) {
        await loadImage(validFiles[0]);
      } else {
        // 多张图片使用loadFolder
        // 创建一个模拟的FileList对象
        const fileList = Object.assign(validFiles, {
          item: (index: number) => validFiles[index] || null,
          length: validFiles.length
        }) as FileList;

        await loadFolder(fileList);
      }

      message.success(`成功加载 ${validFiles.length} 张示例图片到工作区`);
      onCancel(); // 关闭模态框
    } catch (error) {
      console.error('加载示例图片失败:', error);
      message.error('加载示例图片失败，请稍后重试');
    } finally {
      setLoadingImages(false);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 确认单选并加载
  const handleConfirmSingleSelection = async () => {
    if (!onSelectImage || !selectedImageId || !exampleImages) {
        message.warning('请先选择一张图片');
        return;
    }

    setLoadingImages(true);
    message.loading({ content: `正在加载图片...`, key: 'single-load' });

    const allImages = [...exampleImages.barcode, ...exampleImages.ocr, ...exampleImages.ai_restored];
    const image = allImages.find(img => img.id.toString() === selectedImageId);

    if (!image) {
        message.error({ content: '无法找到所选图片信息', key: 'single-load' });
        setLoadingImages(false);
        return;
    }
    
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';
    const fullUrl = `${baseUrl.replace('/api', '')}${image.url}`;

    try {
        const response = await fetch(fullUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const blob = await response.blob();
        const file = new File([blob], image.name, { type: blob.type });
        onSelectImage(file);
        message.success({ content: `已选择 ${image.name}`, key: 'single-load' });
        onCancel(); // Close modal on success
    } catch (error) {
        console.error(`下载示例图片失败: ${image.name}`, error);
        message.error({ content: `加载 ${image.name} 失败`, key: 'single-load' });
    } finally {
        setLoadingImages(false);
    }
  };

  // 渲染图片卡片
  const renderImageCard = (image: ExampleImage) => {
    const imageIdStr = image.id.toString();
    const isSelected = (selectionMode === 'multiple' && selectedImages.has(imageIdStr)) ||
                       (selectionMode === 'single' && selectedImageId === imageIdStr);

    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';
    const fullUrl = `${baseUrl.replace('/api', '')}${image.url}`;

    // 统一的勾选框变化处理器
    const handleCheckboxChange = (checked: boolean) => {
      if (selectionMode === 'multiple') {
        handleImageSelect(imageIdStr, checked);
      } else { // single mode
        if (checked) {
          setSelectedImageId(imageIdStr);
        } else {
          if (selectedImageId === imageIdStr) {
            setSelectedImageId(null);
          }
        }
      }
    };

    return (
      <Col xs={24} sm={12} md={8} lg={6} xl={4} key={image.id}>
        <Card
          hoverable
          size="small"
          style={{
            marginBottom: 16,
            border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
            borderRadius: 8,
            overflow: 'hidden',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            transform: isSelected ? 'translateY(-2px) scale(1.02)' : 'translateY(0) scale(1)',
            boxShadow: isSelected
              ? '0 8px 24px rgba(24, 144, 255, 0.3)'
              : '0 2px 8px rgba(0, 0, 0, 0.1)'
          }}
          // 让卡片点击也能触发勾选
          onClick={() => handleCheckboxChange(!isSelected)}
          cover={
            <div style={{
              position: 'relative',
              height: 160,
              overflow: 'hidden',
              backgroundColor: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Image
                src={fullUrl}
                alt={image.name}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  objectPosition: 'center',
                  transition: 'transform 0.3s ease'
                }}
                wrapperStyle={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                preview={{
                  mask: (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                      transition: 'all 0.3s ease',
                      opacity: 0
                    }}
                    className="image-preview-mask"
                    >
                      <EyeOutlined style={{
                        fontSize: 24,
                        color: 'white',
                        filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'
                      }} />
                    </div>
                  )
                }}
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              />
              <Checkbox
                checked={isSelected}
                onChange={(e) => {
                  // 阻止事件冒泡到Card的onClick
                  e.stopPropagation();
                  handleCheckboxChange(e.target.checked);
                }}
                style={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: 6,
                  padding: 4,
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                  transition: 'all 0.3s ease',
                  transform: isSelected ? 'scale(1.1)' : 'scale(1)',
                  zIndex: 10 // 确保在最上层
                }}
              />
              {isSelected && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(24, 144, 255, 0.1)',
                  border: '2px solid #1890ff',
                  borderRadius: 4,
                  pointerEvents: 'none',
                  animation: 'selectedPulse 0.3s ease'
                }} />
              )}
            </div>
          }
          styles={{
            body: {
              padding: '12px',
              height: '100px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between'
            }
          }}
        >
          <div>
            <Text
              ellipsis={{ tooltip: image.name }}
              style={{
                fontSize: 13,
                fontWeight: 500,
                display: 'block',
                marginBottom: 4,
                lineHeight: '1.3'
              }}
            >
              {image.name}
            </Text>
            <Text
              type="secondary"
              ellipsis={{ tooltip: image.description }}
              style={{
                fontSize: 11,
                display: 'block',
                lineHeight: '1.3',
                color: 'rgba(0, 0, 0, 0.65)'
              }}
            >
              {image.description}
            </Text>
          </div>
          <div style={{ marginTop: 8 }}>
            <Space size={4} wrap>
              <Tag
                color="green"
                style={{
                  fontSize: 10,
                  margin: 0,
                  padding: '2px 6px',
                  borderRadius: 4
                }}
              >
                {formatFileSize(image.file_size)}
              </Tag>
            </Space>
          </div>
        </Card>
      </Col>
    );
  };

  // 渲染分类标签页内容
  const renderCategoryTabContent = (category: keyof ExampleImagesDashboardResponse, title: string) => {
    if (!exampleImages) return null;

    const images = exampleImages[category];
    const selectedCount = images.filter(image => selectedImages.has(image.id.toString())).length;
    const totalCount = images.length;

    return images.length === 0 ? (
      <div style={{
        height: '450px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Alert
          message="暂无示例图片"
          description={`${title}分类下暂时没有可用的示例图片`}
          type="info"
          showIcon
        />
      </div>
    ) : (
      <div style={{
        height: '450px',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* 固定的操作栏 */}
        <div style={{
          flexShrink: 0,
          marginBottom: 16,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 4px',
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 12
        }}>
          {selectionMode === 'multiple' ? (
            <>
              <Space>
                <Checkbox
                  checked={selectedCount === totalCount && totalCount > 0}
                  indeterminate={selectedCount > 0 && selectedCount < totalCount}
                  onChange={(e) => handleCategorySelectAll(category, e.target.checked)}
                >
                  全选 ({totalCount} 张)
                </Checkbox>
              </Space>
              <Text type="secondary">
                已选择 {selectedCount} 张图片
              </Text>
            </>
          ) : (
            <Text type="secondary">请勾选下方图片进行选择</Text>
          )}
        </div>

        {/* 可滚动的图片网格容器 */}
        <div
          ref={scrollContainerRef}
          className="example-images-scroll-container"
          style={scrollContainerStyle}
          onScroll={handleScroll}
        >
          <Row gutter={[16, 16]} style={{ margin: 0 }}>
            {images.map(renderImageCard)}
          </Row>

          {/* 回到顶部浮动按钮 */}
          {showBackToTop && (
            <FloatButton
              icon={<VerticalAlignTopOutlined />}
              tooltip="回到顶部"
              onClick={scrollToTop}
              style={{
                position: 'absolute',
                right: 24,
                bottom: 24,
                zIndex: 1000,
                opacity: showBackToTop ? 1 : 0,
                transform: showBackToTop ? 'translateY(0)' : 'translateY(20px)',
                transition: 'all 0.3s ease'
              }}
            />
          )}
        </div>
      </div>
    );
  };

  // 生成Tabs的items配置
  const getTabsItems = (): TabsProps['items'] => {
    if (!exampleImages) return [];

    return [
      {
        key: 'barcode',
        label: (
          <Space>
            <PictureOutlined />
            条码检测
            {exampleImages.barcode.length > 0 && (
              <Tag color={exampleImages.barcode.filter(img => selectedImages.has(img.id.toString())).length > 0 ? 'blue' : 'default'}>
                {exampleImages.barcode.filter(img => selectedImages.has(img.id.toString())).length}/{exampleImages.barcode.length}
              </Tag>
            )}
          </Space>
        ),
        children: renderCategoryTabContent('barcode', '条码检测'),
      },
      {
        key: 'ocr',
        label: (
          <Space>
            <PictureOutlined />
            OCR识别
            {exampleImages.ocr.length > 0 && (
              <Tag color={exampleImages.ocr.filter(img => selectedImages.has(img.id.toString())).length > 0 ? 'blue' : 'default'}>
                {exampleImages.ocr.filter(img => selectedImages.has(img.id.toString())).length}/{exampleImages.ocr.length}
              </Tag>
            )}
          </Space>
        ),
        children: renderCategoryTabContent('ocr', 'OCR识别'),
      },
      {
        key: 'ai_restored',
        label: (
          <Space>
            <PictureOutlined />
            AI复原
            {exampleImages.ai_restored.length > 0 && (
              <Tag color={exampleImages.ai_restored.filter(img => selectedImages.has(img.id.toString())).length > 0 ? 'blue' : 'default'}>
                {exampleImages.ai_restored.filter(img => selectedImages.has(img.id.toString())).length}/{exampleImages.ai_restored.length}
              </Tag>
            )}
          </Space>
        ),
        children: renderCategoryTabContent('ai_restored', 'AI复原'),
      },
    ];
  };

  return (
    <>
      {/* 内联样式用于Webkit浏览器的滚动条和图片预览效果 */}
      <style>
        {`
          .example-images-scroll-container::-webkit-scrollbar {
            width: 8px;
          }
          .example-images-scroll-container::-webkit-scrollbar-track {
            background: #f5f5f5;
            border-radius: 4px;
          }
          .example-images-scroll-container::-webkit-scrollbar-thumb {
            background: #bfbfbf;
            border-radius: 4px;
            transition: background 0.3s ease;
          }
          .example-images-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #999999;
          }

          /* 图片预览遮罩悬停效果 */
          .ant-image:hover .image-preview-mask {
            opacity: 1 !important;
          }

          /* 图片卡片悬停效果 */
          .ant-card:hover .ant-image img {
            transform: scale(1.05);
          }

          /* 确保图片容器的居中对齐 */
          .ant-image {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;
            height: 100% !important;
          }

          .ant-image img {
            transition: transform 0.3s ease !important;
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            object-position: center !important;
          }

          /* 图片容器的背景和边框 */
          .ant-image .ant-image-img {
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }
        `}
      </style>
      <Modal
      title={
        <Space>
          <PictureOutlined />
          示例图片库
          {exampleImages && (
            <Tag color="blue">
              共 {Object.values(exampleImages).reduce((total, images) => total + images.length, 0)} 张图片
            </Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={1200}
      height={700}
      style={{
        top: 20,
        maxHeight: '90vh'
      }}
      styles={{
        body: {
          height: '550px',
          padding: '16px 24px',
          overflow: 'hidden'
        }
      }}
      footer={
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}>
          {selectionMode === 'multiple' && (
            <>
              <div>
                {selectedImages.size > 0 && (
                  <Space>
                    <Text type="secondary">
                      已选择 {selectedImages.size} 张图片
                    </Text>
                    <Button size="small" onClick={() => setSelectedImages(new Set())}>
                      清空选择
                    </Button>
                  </Space>
                )}
              </div>
              <Space>
                <Button key="cancel-multi" onClick={onCancel}>
                  取消
                </Button>
                <Button
                  key="load-multi"
                  type="primary"
                  icon={<DownloadOutlined />}
                  loading={loadingImages}
                  disabled={selectedImages.size === 0}
                  onClick={handleLoadSelectedImages}
                >
                  加载到工作区 ({selectedImages.size})
                </Button>
              </Space>
            </>
          )}
          {selectionMode === 'single' && (
            <>
              <div />
              <Space>
                <Button key="cancel-single" onClick={onCancel}>
                  取消
                </Button>
                <Button
                  key="load-single"
                  type="primary"
                  icon={<DownloadOutlined />}
                  loading={loadingImages}
                  disabled={!selectedImageId}
                  onClick={handleConfirmSingleSelection}
                >
                  加载图片
                </Button>
              </Space>
            </>
          )}
        </div>
      }
    >
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Spin spinning={loading} tip="正在获取示例图片列表..." style={{ height: '100%' }}>
          {exampleImages && (
            <Tabs
              defaultActiveKey="barcode"
              type="card"
              items={getTabsItems()}
              style={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column'
              }}
              tabBarStyle={{
                marginBottom: 16,
                flexShrink: 0
              }}
            />
          )}
        </Spin>
      </div>
    </Modal>
    </>
  );
};

export default ExampleImagesModal;
