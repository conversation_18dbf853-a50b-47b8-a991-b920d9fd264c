import React, { useEffect, useState } from 'react';
import { Card, Typo<PERSON>, <PERSON>, Button, Modal, Upload, Image, Checkbox, Row, Col, Spin, App, Select, Progress, List, Avatar, Input, Dropdown } from 'antd';
import { PictureOutlined, PlusOutlined, DeleteOutlined, EyeOutlined, UploadOutlined, ReloadOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined, HolderOutlined, FolderFilled, MoreOutlined } from '@ant-design/icons';
import { 
  getExampleImagesForAdmin,
  uploadExampleImage,
  deleteExampleImages,
  updateImageOrder,
  ExampleImage,
  UpdateImageOrderParams,
  updateImageDescription,
  createExampleFolder,
  renameExampleFolder,
  deleteExampleFolder,
  AdminExampleImagesResponse
} from '../services/api';
import { DndContext, PointerSensor, KeyboardSensor, useSensor, useSensors, DragEndEvent, closestCenter, DragOverlay, DragStartEvent, CollisionDetection, useDroppable } from '@dnd-kit/core';
import { sortableKeyboardCoordinates, SortableContext, useSortable, arrayMove } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const { Title, Paragraph, Text } = Typography;

const CATEGORY_LABELS: Record<string, string> = {
  barcode: '条码',
  ocr: 'OCR',
  ai_restored: 'AI复原',
};

const getFullImageUrl = (url: string) => {
  const baseUrl = (import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000').replace(/\/$/, '');
  if (/^https?:\/\//.test(url)) return url;
  if (url.startsWith('/api/') && baseUrl.endsWith('/api')) {
    return baseUrl.replace(/\/api$/, '') + url;
  }
  return baseUrl + url;
};

/**
 * A dedicated component for the drag overlay preview.
 */
const ImageOverlayCard: React.FC<{ img: ExampleImage; isSelected: boolean }> = React.memo(({ img, isSelected }) => {
  return (
    <Card
      hoverable
      className={isSelected ? 'selected-card' : ''}
      style={{ width: 230, cursor: 'grabbing', borderRadius: 8 }}
      styles={{ body: { padding: 12 } }}
      cover={
        <div className="admin-image-card-cover">
          <Image
            src={getFullImageUrl(img.url)}
            alt={img.name}
            width="100%"
            height={120}
            style={{ objectFit: 'cover', cursor: 'grabbing' }}
            preview={false}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        </div>
      }
    >
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'stretch', gap: 8 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Checkbox checked={isSelected} disabled={true}>
            选择
          </Checkbox>
          <Space size="small">
             <Button
                size="small"
                icon={<HolderOutlined />}
                style={{ cursor: 'grabbing' }}
              />
             <Button size="small" danger icon={<DeleteOutlined />} />
          </Space>
        </div>
        <Text style={{ fontSize: 13, fontWeight: 500, marginBottom: 2, wordBreak: 'break-all' }} ellipsis={{ tooltip: img.name }}>{img.name}</Text>
         <Paragraph
          style={{ fontSize: 12, color: '#888', minHeight: 16, margin: 0 }}
          ellipsis={{ rows: 2, tooltip: img.description || '点击添加描述' }}
        >
          {img.description || '点击添加描述'}
        </Paragraph>
      </div>
    </Card>
  );
});


/**
 * Sortable image component properties
 */
interface SortableImageProps {
  img: ExampleImage;
  isSelected: boolean;
  onSelect: (id: number, checked: boolean) => void;
  onSingleDelete: (id: number) => void;
  onDescriptionUpdate: (imageId: number, newDescription: string) => void;
  onMoveToRoot: (id: number) => void;
  isInFolder: boolean;
  isMultiDragActive?: boolean;
}

/**
 * Sortable Image Component
 */
const SortableImage: React.FC<SortableImageProps> = React.memo(({
  img,
  isSelected,
  onSelect,
  onSingleDelete,
  onDescriptionUpdate,
  onMoveToRoot,
  isInFolder,
  isMultiDragActive,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: img.id });
  const { message: antdMessage } = App.useApp();

  const style: React.CSSProperties = {
    transition,
    transform: CSS.Transform.toString(transform),
    opacity: (isDragging || (isMultiDragActive && isSelected)) ? 0 : 1,
  };

  if (style.opacity === 0) {
    style.visibility = 'hidden';
  }

  const handleDescriptionChange = async (newDescription: string) => {
    if (newDescription === img.description) {
      return;
    }
    try {
      await updateImageDescription(img.id, newDescription);
      antdMessage.success('描述更新成功');
      onDescriptionUpdate(img.id, newDescription);
    } catch (e: any) {
      antdMessage.error(e.message || '描述更新失败');
    }
  };

  return (
    <div ref={setNodeRef} style={style}>
       <Card
          hoverable
          className={isSelected ? 'selected-card' : ''}
          cover={
            <div className="admin-image-card-cover">
              <Image
                src={getFullImageUrl(img.url)}
                alt={img.name}
                width="100%"
                height={120}
                style={{ objectFit: 'cover' }}
                preview={{
                  src: getFullImageUrl(img.url),
                  mask: (
                    <div style={{
                      background: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100%',
                      height: '100%'
                    }}>
                      <EyeOutlined style={{ fontSize: '24px' }} />
                    </div>
                  )
                }}
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              />
            </div>
          }
          style={{ borderRadius: 8 }}
          styles={{ body: { padding: 12 } }}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'stretch', gap: 8 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Checkbox checked={isSelected} onChange={e => onSelect(img.id, e.target.checked)}>
                选择
              </Checkbox>
              <Space size="small">
                 <Button
                    size="small"
                    icon={<HolderOutlined />}
                    {...attributes}
                    {...listeners}
                    style={{ cursor: 'grab' }}
                  />
                 <Dropdown
                    menu={{
                      items: [
                        isInFolder && {
                          key: 'move-to-root',
                          label: '移回根目录',
                          onClick: () => onMoveToRoot(img.id),
                        },
                        {
                          key: 'delete',
                          label: '删除',
                          danger: true,
                          onClick: () => onSingleDelete(img.id),
                        },
                      ].filter(Boolean) as any,
                    }}
                    trigger={['click']}
                  >
                    <Button
                      size="small"
                      icon={<MoreOutlined />}
                      onClick={e => e.stopPropagation()}
                    />
                  </Dropdown>
              </Space>
            </div>
            <Text style={{ fontSize: 13, fontWeight: 500, marginBottom: 2, wordBreak: 'break-all' }} ellipsis={{ tooltip: img.name }}>{img.name}</Text>
             <Paragraph
              style={{ fontSize: 12, color: '#888', minHeight: 16, margin: 0 }}
              ellipsis={{ rows: 2, tooltip: img.description || '点击添加描述' }}
              editable={{
                icon: <>编辑</>,
                tooltip: '编辑描述',
                onChange: handleDescriptionChange,
                enterIcon: null,
              }}
            >
              {img.description || '点击添加描述'}
            </Paragraph>
          </div>
        </Card>
    </div>
  );
});

/**
 * Folder component properties
 */
interface SortableFolderProps {
  folderName: string;
  onRename: (oldName: string) => void;
  onDelete: (name: string) => void;
  onClick: (name: string) => void;
}

/**
 * Sortable and Droppable Folder Component
 */
const SortableFolder: React.FC<SortableFolderProps> = React.memo(({ folderName, onRename, onDelete, onClick }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `folder-${folderName}`,
    data: {
      isFolder: true,
      folderName,
    },
  });

  const menuItems = [
    {
      key: 'rename',
      label: '重命名',
      onClick: () => onRename(folderName),
    },
    {
      key: 'delete',
      label: '删除',
      danger: true,
      onClick: () => onDelete(folderName),
    },
  ];

  return (
    <div ref={setNodeRef}>
      <Card
        hoverable
        style={{ 
          textAlign: 'center',
          transition: 'all 0.2s ease-in-out',
          border: isOver ? '2px dashed #1677ff' : '1px solid #d9d9d9',
          transform: isOver ? 'scale(1.05)' : 'scale(1)',
        }}
        styles={{ body: { padding: '24px 12px' } }}
        onClick={() => onClick(folderName)}
      >
        <FolderFilled style={{ fontSize: 48, color: '#ffd700', marginBottom: 12 }} />
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Text style={{ flex: 1, wordBreak: 'break-all' }} ellipsis={{ tooltip: folderName }}>{folderName}</Text>
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} size="small" onClick={(e) => e.stopPropagation()} />
          </Dropdown>
        </div>
      </Card>
    </div>
  );
});


/**
 * Admin Images Page
 */
const AdminImagesPage: React.FC = () => {
  const [images, setImages] = useState<ExampleImage[]>([]);
  const [folders, setFolders] = useState<string[]>([]);
  const [currentFolder, setCurrentFolder] = useState<string | undefined>(undefined);
  const [selected, setSelected] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);
  const [category, setCategory] = useState<'barcode' | 'ocr' | 'ai_restored'>('barcode');
  const { message: antdMessage, modal } = App.useApp();
  const [activeId, setActiveId] = useState<number | string | null>(null);

  // Upload state
  const [uploadModal, setUploadModal] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadCategory, setUploadCategory] = useState<'barcode' | 'ocr' | 'ai_restored'>('barcode');
  const [uploadFileList, setUploadFileList] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: { status: 'uploading' | 'success' | 'error', progress: number, message?: string }}>({});
  const [uploadKey, setUploadKey] = useState<string>('');
  const [filePreviewUrls, setFilePreviewUrls] = useState<Record<string, string>>({});

  // Folder management state
  const [isFolderModalVisible, setIsFolderModalVisible] = useState(false);
  const [folderModalMode, setFolderModalMode] = useState<'create' | 'rename'>('create');
  const [folderName, setFolderName] = useState('');
  const [oldFolderName, setOldFolderName] = useState('');
  const [folderModalError, setFolderModalError] = useState<string | null>(null);
  const [isProcessingFolder, setIsProcessingFolder] = useState(false);


  const customCollisionDetection: CollisionDetection = closestCenter;

  const getFilePreviewUrl = (file: File): string => {
    const fileKey = `${file.name}_${file.size}`;
    return filePreviewUrls[fileKey] || '';
  };

  const cleanupFilePreviewUrls = () => {
    Object.values(filePreviewUrls).forEach(url => URL.revokeObjectURL(url));
    setFilePreviewUrls({});
  };

  useEffect(() => {
    return () => cleanupFilePreviewUrls();
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 8 } }),
    useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates })
  );

  const fetchImages = async (cat: string, fol?: string, showSuccessMessage = false) => {
    setLoading(true);
    try {
      const res: AdminExampleImagesResponse = await getExampleImagesForAdmin(cat, fol);
      setImages(res.images || []);
      setFolders(res.folders || []);
      if (showSuccessMessage) {
        antdMessage.success('刷新完成');
      }
    } catch (e) {
      antdMessage.error('获取图片列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchImagesCallback = React.useCallback(fetchImages, [antdMessage]);

  useEffect(() => {
    fetchImagesCallback(category, currentFolder);
  }, [category, currentFolder, fetchImagesCallback]);

  useEffect(() => {
    setSelected([]);
    if (!currentFolder) { 
        setCurrentFolder(undefined);
    }
  }, [category]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const activeId = active.id;
    if (typeof activeId === 'number' && !selected.includes(activeId)) {
      setSelected([activeId]);
    }
    setActiveId(activeId);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    setActiveId(null);
    const { active, over } = event;

    if (!over) return;

    const activeIdNum = active.id as number;
    const isOverFolder = over.data.current?.isFolder;

    // Case 2: Dragging an image INTO a folder
    if (isOverFolder) {
      const targetFolderName = over.data.current?.folderName;
      if (!targetFolderName || currentFolder === targetFolderName) return;

      const movedImageIds = selected.includes(activeIdNum) ? selected : [activeIdNum];
      const originalImages = [...images];

      setImages(prev => prev.filter(img => !movedImageIds.includes(img.id)));
      setSelected([]);

      try {
        const res = await updateImageOrder({
          category,
          ordered_image_ids: movedImageIds.map(String),
          folder: targetFolderName,
          source_folder: currentFolder,
        });
        if (res.success) {
          antdMessage.success(`成功移动 ${movedImageIds.length} 张图片到 "${targetFolderName}"`);
          fetchImages(category, currentFolder);
        } else {
          setImages(originalImages);
          antdMessage.error(res.message || '移动图片失败');
        }
      } catch (e: any) {
        setImages(originalImages);
        antdMessage.error(e.message || '移动图片失败');
      }
      return;
    }

    // Case 3: Reordering items within the CURRENT view (root or subfolder).
    if (active.id !== over.id && !isOverFolder) {
      const overIdNum = over.id as number;
      const overIndexCheck = images.findIndex(img => img.id === overIdNum);
      if (overIndexCheck === -1) return;

      const isMultiDrag = selected.length > 1 && selected.includes(activeIdNum);
      const originalImages = [...images];
      let newOrderedImages;

      if (isMultiDrag) {
        const selectedItems = images.filter(img => selected.includes(img.id));
        const remainingItems = images.filter(img => !selected.includes(img.id));
        let overInRemainingIndex = remainingItems.findIndex(img => img.id === overIdNum);
        
        if (overInRemainingIndex === -1) return;

        const activeIndex = images.findIndex(img => img.id === activeIdNum);
        const overIndex = images.findIndex(img => img.id === overIdNum);
        if (overIndex > activeIndex) overInRemainingIndex++;

        newOrderedImages = [...remainingItems.slice(0, overInRemainingIndex), ...selectedItems, ...remainingItems.slice(overInRemainingIndex)];
      } else {
        const oldIndex = images.findIndex(img => img.id === activeIdNum);
        const newIndex = images.findIndex(img => img.id === overIdNum);
        newOrderedImages = arrayMove(images, oldIndex, newIndex);
      }

      setImages(newOrderedImages);
      try {
        const params: UpdateImageOrderParams = {
          category,
          ordered_image_ids: newOrderedImages.map(img => img.id.toString()),
          folder: currentFolder,
        };
        const res = await updateImageOrder(params);
        if (!res.success) {
          setImages(originalImages);
          antdMessage.error(res.message || '顺序更新失败');
        } else {
          antdMessage.success(isMultiDrag ? `移动 ${selected.length} 个项目成功` : '顺序更新成功');
        }
      } catch (e: any) {
        setImages(originalImages);
        antdMessage.error(e.message || '顺序更新失败');
      }
    }
  };

  const onSelect = React.useCallback((id: number, checked: boolean) => {
    setSelected(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  }, []);

  const onSelectAll = React.useCallback((checked: boolean) => {
    setSelected(checked ? images.map(img => img.id) : []);
  }, [images]);

  const isAllSelected = images.length > 0 && selected.length === images.length;

  const handleRefresh = React.useCallback(() => fetchImages(category, currentFolder, true), [category, currentFolder]);

  const handleMoveToRoot = async (imageIds: number[]) => {
    if (!currentFolder || imageIds.length === 0) return;
    
    modal.confirm({
        title: `移回根目录`,
        content: `确定要将选中的 ${imageIds.length} 张图片移回根目录吗？`,
        okText: '确认移动',
        cancelText: '取消',
        onOk: async () => {
            try {
                const res = await updateImageOrder({
                    category,
                    ordered_image_ids: imageIds.map(String),
                    folder: '', // Empty string indicates root
                    source_folder: currentFolder,
                });
                if (res.success) {
                    antdMessage.success('图片已成功移回根目录');
                    fetchImages(category, currentFolder); // Refresh current folder
                    setSelected(prev => prev.filter(id => !imageIds.includes(id)));
                } else {
                    antdMessage.error(res.message || '移回根目录失败');
                }
            } catch (e: any) {
                antdMessage.error(e.message || '移回根目录失败');
            }
        },
    });
  };

  // --- Upload handlers ---
  const handleBatchUpload = async () => {
    if (uploadFileList.length === 0) return antdMessage.warning('请先选择要上传的图片');
    setUploading(true);
    const newProgress: typeof uploadProgress = {};
    uploadFileList.forEach(file => { newProgress[file.name] = { status: 'uploading', progress: 0 }; });
    setUploadProgress(newProgress);
    let successCount = 0, failCount = 0;
    const uploadPromises = uploadFileList.map(async (file) => {
      try {
        setUploadProgress(prev => ({ ...prev, [file.name]: { status: 'uploading', progress: 50 } }));
        await uploadExampleImage({ file, category: uploadCategory });
        setUploadProgress(prev => ({ ...prev, [file.name]: { status: 'success', progress: 100, message: '上传成功' } }));
        successCount++;
      } catch (e: any) {
        setUploadProgress(prev => ({ ...prev, [file.name]: { status: 'error', progress: 100, message: e?.response?.data?.error || '上传失败' } }));
        failCount++;
      }
    });
    await Promise.all(uploadPromises);
    if (successCount > 0 && failCount === 0) antdMessage.success(`成功上传 ${successCount} 张图片`);
    else if (successCount > 0 && failCount > 0) antdMessage.warning(`成功 ${successCount} 张, 失败 ${failCount} 张`);
    else antdMessage.error(`上传失败，共 ${failCount} 张图片`);
    setUploading(false);
    setTimeout(() => {
        if (uploadCategory !== category) { setCategory(uploadCategory); }
        else { fetchImages(uploadCategory, undefined, true); }
        setCurrentFolder(undefined);
    }, 300);
    setTimeout(() => { handleCloseUploadModal(); }, 2000);
  };

  const handleFileChange = (info: any) => {
    const incomingFiles = info.fileList.map((f: any) => f.originFileObj || f).filter(Boolean);
    if (incomingFiles.length === 0) return;
    setUploadFileList(prev => {
        const existingKeys = new Set(prev.map(f => `${f.name}_${f.size}`));
        const uniqueNewFiles = incomingFiles.filter((f: File) => !existingKeys.has(`${f.name}_${f.size}`));
        if (uniqueNewFiles.length > 0) {
            const newUrls: Record<string, string> = {};
            uniqueNewFiles.forEach((f: File) => { newUrls[`${f.name}_${f.size}`] = URL.createObjectURL(f); });
            setFilePreviewUrls(prevUrls => ({ ...prevUrls, ...newUrls }));
        }
        return [...prev, ...uniqueNewFiles];
    });
  };

  const handleRemoveFile = (file: File) => {
    setUploadFileList(prev => prev.filter(f => f.name !== file.name));
    setUploadProgress(prev => { const p = { ...prev }; delete p[file.name]; return p; });
  };
  
  const handleClearAllFiles = () => {
    setUploadFileList([]);
    setUploadProgress({});
  };

  const clearUploadState = () => {
    setUploadFileList([]);
    setUploadProgress({});
    setUploading(false);
    setUploadKey(Date.now().toString());
    cleanupFilePreviewUrls();
  };

  const handleOpenUploadModal = () => {
    setUploadCategory(category);
    clearUploadState();
    setUploadModal(true);
  };

  const handleCloseUploadModal = () => {
    clearUploadState();
    setUploadModal(false);
  };

  // --- Delete handlers ---
  const handleBatchDelete = React.useCallback(async () => {
    if (!selected.length) return;
    modal.confirm({
      title: `确定删除选中的 ${selected.length} 张图片吗?`,
      content: '此操作不可恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteExampleImages({ category, ids: selected });
          antdMessage.success('删除成功');
          setSelected([]);
          fetchImages(category, currentFolder);
        } catch (e: any) {
          antdMessage.error(e?.response?.data?.message || '删除失败');
        }
      }
    });
  }, [selected, category, currentFolder, antdMessage, modal, fetchImages]);

  const handleSingleDelete = React.useCallback(async (imageId: number) => {
    modal.confirm({
        title: `确定删除这张图片吗?`,
        content: '此操作不可恢复。',
        okText: '确认删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
            try {
              await deleteExampleImages({ category, ids: [imageId] });
              antdMessage.success('删除成功');
              setSelected(prev => prev.filter(id => id !== imageId));
              fetchImages(category, currentFolder);
            } catch (e: any) {
              antdMessage.error(e?.response?.data?.message || '删除失败');
            }
        }
    });
  }, [category, currentFolder, antdMessage, modal, fetchImages]);

  const handleDescriptionUpdate = React.useCallback((imageId: number, newDescription: string) => {
    setImages(prev => prev.map(img => img.id === imageId ? { ...img, description: newDescription } : img));
  }, []);

  // --- Folder action handlers ---
  const handleOpenFolderModal = (mode: 'create' | 'rename', name = '') => {
    setFolderModalMode(mode);
    setFolderName(name);
    setOldFolderName(name);
    setFolderModalError(null);
    setIsFolderModalVisible(true);
  };
  const handleCloseFolderModal = () => setIsFolderModalVisible(false);
  
  const handleFolderFormSubmit = async () => {
    if (!folderName.trim()) { setFolderModalError('文件夹名称不能为空'); return; }
    if (folderName.includes('/') || folderName.includes('\\')) { setFolderModalError('文件夹名称不能包含 / 或 \\'); return; }
    setIsProcessingFolder(true);
    setFolderModalError(null);
    try {
        if (folderModalMode === 'create') {
            await createExampleFolder({ category, folder_name: folderName });
            antdMessage.success('文件夹创建成功');
        } else {
            await renameExampleFolder({ category, old_name: oldFolderName, new_name: folderName });
            antdMessage.success('文件夹重命名成功');
        }
        handleCloseFolderModal();
        if (folderModalMode === 'create') {
          fetchImages(category, undefined, true);
        } else {
          setCurrentFolder(undefined);
        }
    } catch (e: any) {
        setFolderModalError(e.message || '操作失败，请重试');
    } finally {
        setIsProcessingFolder(false);
    }
  };

  const handleDeleteFolder = (name: string) => {
    modal.confirm({
      title: `确定要删除文件夹 "${name}"吗？`,
      content: '只有当文件夹为空时才能被删除。此操作不可恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteExampleFolder({ category, folder_name: name });
          antdMessage.success('文件夹删除成功');
          if(currentFolder === name) {
            setCurrentFolder(undefined);
          } else {
            fetchImages(category, currentFolder, true);
          }
        } catch (e: any) {
          antdMessage.error(e.message || '删除文件夹失败');
        }
      },
    });
  };

  const handleFolderClick = (name: string) => {
      setCurrentFolder(name);
  };

  const activeImage = typeof activeId === 'number' ? images.find(img => img.id === activeId) : null;
  
  return (
    <div style={{ width: '100%', maxWidth: 1400, margin: '0 auto' }}>
      <style>{`
         .admin-image-card-cover { position: relative; background: #f5f5f5; height: 120px; display: flex; align-items: center; justify-content: center; overflow: hidden; border-radius: 8px; }
         .admin-image-card-cover .ant-image { width: 100%; height: 100%; border-radius: 8px; }
         .admin-image-card-cover .ant-image-img { transition: transform 0.3s cubic-bezier(0.4,0,0.2,1); border-radius: 8px; width: 100% !important; height: 100% !important; }
         .admin-image-card-cover:hover .ant-image-img { transform: scale(1.05); }
         .admin-image-card-cover .ant-image-mask { border-radius: 8px !important; inset: 0 !important; }
         .admin-image-card-cover .ant-image-mask > div { border-radius: 8px !important; }
         .selected-card { border: 2px solid #1677ff !important; box-shadow: 0 0 0 3px rgba(5, 145, 255, 0.2) !important; }
      `}</style>
      <div style={{ marginBottom: 28, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>
          <Title level={2} style={{ margin: 0, color: '#52c41a' }}><PictureOutlined style={{ marginRight: 12 }} />示例图片管理</Title>
          <Paragraph style={{ marginTop: 8, fontSize: 16, color: '#666', marginBottom: 0 }}>管理示例图片库，为用户提供测试和演示用的图像资源</Paragraph>
        </div>
        <Space>
           <Button icon={<UploadOutlined />} type="primary" onClick={handleOpenUploadModal}>上传图片</Button>
           <Button icon={<PlusOutlined />} onClick={() => handleOpenFolderModal('create')}>创建文件夹</Button>
           {currentFolder && (
             <Button
               onClick={() => handleMoveToRoot(selected)}
               disabled={selected.length === 0}
             >
               批量移回根目录
             </Button>
           )}
           <Button icon={<DeleteOutlined />} danger disabled={selected.length === 0} onClick={handleBatchDelete}>批量删除</Button>
           <Button icon={<ReloadOutlined />} onClick={handleRefresh}>刷新</Button>
         </Space>
      </div>
      
      <DndContext sensors={sensors} collisionDetection={customCollisionDetection} onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <Space style={{ marginBottom: 16, flexWrap: 'wrap', alignItems: 'center' }}>
          {(['barcode', 'ocr', 'ai_restored'] as const).map(cat => (
            <Button key={cat} type={cat === category ? 'primary' : 'default'} onClick={() => setCategory(cat)}>
              {CATEGORY_LABELS[cat]}
            </Button>
          ))}
          <Checkbox checked={isAllSelected} indeterminate={selected.length > 0 && !isAllSelected} onChange={e => onSelectAll(e.target.checked)}>全选</Checkbox>
          {currentFolder ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Text type="secondary">当前位置:</Text>
              <Button type="link" onClick={() => setCurrentFolder(undefined)}>根目录</Button>
              <Text type="secondary">/</Text>
              <Text style={{ marginLeft: 8 }}>{currentFolder}</Text>
            </div>
          ) : (
            <Text type="secondary" style={{ fontSize: 12 }}>提示：可拖动卡片进行排序，或拖入文件夹</Text>
          )}
        </Space>

        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}>
            <Spin size="large" />
          </div>
        ) : (
          <div style={{ padding: '16px', borderRadius: '8px', minHeight: 200, position: 'relative' }}>
            {/* Folders are droppable but not sortable, so they are outside the SortableContext */}
            {!currentFolder && folders.length > 0 && (
              <>
                  <Title level={4} style={{ marginTop: 0, marginBottom: 16 }}>文件夹</Title>
                  <Row gutter={[16, 16]}>
                      {folders.map(folder => (
                          <Col xs={12} sm={8} md={6} lg={4} key={`folder-${folder}`}>
                              <SortableFolder
                                  folderName={folder}
                                  onRename={() => handleOpenFolderModal('rename', folder)}
                                  onDelete={handleDeleteFolder}
                                  onClick={handleFolderClick}
                              />
                          </Col>
                      ))}
                  </Row>
              </>
            )}
            
            <Title level={4} style={{ marginTop: !currentFolder && folders.length > 0 ? 24 : 0, marginBottom: 16 }}>
              {currentFolder ? `“${currentFolder}”中的图片` : '根目录图片'}
            </Title>
            
            {/* SortableContext should ONLY wrap the sortable items */}
            <SortableContext items={images.map(img => img.id)}>
              {images.length > 0 ? (
                <Row gutter={[16, 16]}>
                  {images.map(img => (
                    <Col xs={12} sm={8} md={6} lg={4} key={img.id}>
                      <SortableImage
                        img={img}
                        isSelected={selected.includes(img.id)}
                        onSelect={onSelect}
                        onSingleDelete={handleSingleDelete}
                        onDescriptionUpdate={handleDescriptionUpdate}
                        onMoveToRoot={(id) => handleMoveToRoot([id])}
                        isInFolder={!!currentFolder}
                        isMultiDragActive={activeId !== null && selected.length > 1 && selected.includes(activeId as number)}
                      />
                    </Col>
                  ))}
                </Row>
              ) : (
                <div style={{ textAlign: 'center', padding: '48px 0', color: '#888' }}>
                  <PictureOutlined style={{ fontSize: 48, marginBottom: 16 }}/>
                  <p>{currentFolder ? '这个文件夹是空的' : '根目录中没有图片'}</p>
                </div>
              )}
            </SortableContext>
          </div>
        )}
        

        <DragOverlay dropAnimation={null}>
          {activeId && activeImage && (
            <ImageOverlayCard img={activeImage} isSelected={selected.includes(activeImage.id)} />
          )}
        </DragOverlay>
      </DndContext>

      <Modal
        title={folderModalMode === 'create' ? '创建新文件夹' : '重命名文件夹'}
        open={isFolderModalVisible}
        onOk={handleFolderFormSubmit}
        onCancel={handleCloseFolderModal}
        confirmLoading={isProcessingFolder}
        okText={folderModalMode === 'create' ? '创建' : '重命名'}
        cancelText="取消"
      >
        <Input value={folderName} onChange={(e) => { setFolderName(e.target.value); if (folderModalError) setFolderModalError(null); }} placeholder="请输入文件夹名称" onPressEnter={handleFolderFormSubmit} />
        {folderModalError && <Text type="danger" style={{ marginTop: 8, display: 'block' }}>{folderModalError}</Text>}
      </Modal>

      <Modal open={uploadModal} title="批量上传示例图片" onCancel={handleCloseUploadModal} footer={[
          <Button key="cancel" onClick={handleCloseUploadModal} disabled={uploading}>取消</Button>,
          <Button key="upload" type="primary" onClick={handleBatchUpload} loading={uploading} disabled={uploadFileList.length === 0}>开始上传 ({uploadFileList.length} 张)</Button>
        ]} width={600}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <div>
            <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>选择目标分类：</label>
            <Select value={uploadCategory} onChange={setUploadCategory} style={{ width: '100%' }} size="large">
              <Select.Option value="barcode">条码检测</Select.Option>
              <Select.Option value="ocr">OCR文字识别</Select.Option>
              <Select.Option value="ai_restored">AI图像修复</Select.Option>
            </Select>
          </div>
          <div>
            <Upload.Dragger key={uploadKey} name="file" multiple={true} onChange={handleFileChange} beforeUpload={() => false} showUploadList={false} accept=".jpg,.jpeg,.png,.bmp,.webp,.avif" disabled={uploading} fileList={[]}>
              <p className="ant-upload-drag-icon"><PlusOutlined /></p>
              <p className="ant-upload-text">点击或拖拽选择图片文件</p>
              <p className="ant-upload-hint">支持批量选择和多次添加。{uploadFileList.length > 0 && `已选择 ${uploadFileList.length} 张图片`}</p>
            </Upload.Dragger>
          </div>
          {uploadFileList.length > 0 && (
            <div>
              <div style={{ marginBottom: 12, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ fontWeight: 500 }}>已选择文件 ({uploadFileList.length} 张)：</span>
                {!uploading && <Button type="text" size="small" danger onClick={handleClearAllFiles} style={{ fontSize: 12 }}>清空全部</Button>}
              </div>
              <List size="small" dataSource={uploadFileList} renderItem={(file) => {
                  const progress = uploadProgress[file.name];
                  return (
                    <List.Item actions={[!uploading && <Button type="text" size="small" danger onClick={() => handleRemoveFile(file)}>移除</Button>].filter(Boolean)}>
                      <List.Item.Meta
                        avatar={
                          progress?.status === 'uploading' ? <LoadingOutlined /> :
                          progress?.status === 'success' ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                          progress?.status === 'error' ? <CloseCircleOutlined style={{ color: '#ff4d4f' }} /> :
                          <Avatar size={40} src={<Image src={getFilePreviewUrl(file)} style={{ width: 40, height: 40, objectFit: 'cover' }} preview={{src: getFilePreviewUrl(file)}} fallback="data:image/png;base64,..."/>} icon={<PictureOutlined />}/>
                        }
                        title={file.name}
                        description={progress ? ( <div> <Progress percent={progress.progress} size="small" status={progress.status === 'error' ? 'exception' : undefined} showInfo={false}/> {progress.message && (<div style={{ fontSize: 12, marginTop: 4, color: progress.status === 'error' ? '#ff4d4f' : '#52c41a' }}>{progress.message}</div>)} </div>) : (`${(file.size / 1024 / 1024).toFixed(2)} MB`)}
                      />
                    </List.Item>
                  );
                }}
              />
            </div>
          )}
        </Space>
      </Modal>
    </div>
  );
};

export default AdminImagesPage;
