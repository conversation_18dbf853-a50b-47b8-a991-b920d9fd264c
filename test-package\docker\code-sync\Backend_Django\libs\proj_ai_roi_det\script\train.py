# 添加父目录到系统路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# from ultralytics.models.yolo.model import YOLO
from ultralytics import YOLO
from pathlib import Path
from ultralytics import settings

# 修改设置
root = Path(__file__).parent                                                # 获取当前脚本所在目录的路径
settings.update({'datasets_dir': f"{Path(root).as_posix()}"})               # 更新数据集目录
settings.update({'weights_dir': f"{Path(root, 'weights').as_posix()}"})     # 更新权重目录
settings.update({'runs_dir': f"{Path(root, 'runs').as_posix()}"})           # 更新运行结果目录
print(settings)

# 主函数
if __name__ == "__main__":
    # 初始化YOLO模型
    model = YOLO("../mindeo/configs/yolo11-SCDown-v4-3.yaml")  # 不使用预训练权重
    
    
    # 开始训练模型，设置各种训练参数
    results = model.train(
        # 训练策略和控制参数
        epochs=500,                     # 训练的总轮数（更多的轮数通常会给模型更多的时间去学习，但也可能导致过拟合）
        device=[0, 1],                  # 使用的GPU设备，支持多GPU
        batch=512,                      # 每个训练批次的大小（较大的批次大小可以提高模型的稳定性，但需要更多的显存）
        imgsz=320,                      # 输入图像的尺寸（输入图像的尺寸。图像尺寸影响模型的计算量和精度，更大的图像尺寸通常会带来更好的性能但计算成本更高）    

        # 数据相关参数
        data="../mindeo/roi.yaml",      # 数据集配置文件的路径
        single_cls=True,                # 强行转换为单类别训练
        rect=False,                     # 矩形训练（保持图像的长宽比，可以提高检测精度）(多GPU训练好像不支持矩形训练，会报警告“WARNING ⚠️ 'rect=True' is incompatible with Multi-GPU training, setting 'rect=False'”。)
                                        # 参考官方issue：https://github.com/ultralytics/ultralytics/issues/3171

        # 模型和项目相关参数
        project="../runs/AI_ROI_Det",   # 训练结果保存的项目路径
        name="V1.1.0.",                # 训练结果的名称，使用完整的四段式版本号

        # 中途保存模型
        save_period=50,                 # 每隔50个epoch保存一次模型
        resume=True,                    # 允许从上次中断的地方继续训练

        # 早停策略
        patience=50,                    # 如果验证性能在50个epoch内没有提升，则提前停止训练

        # 学习率和优化器设置
        lr0=0.012,                      # 初始学习率（学习率决定了每次更新参数的幅度，过高或过低的学习率都会影响模型收敛）
        lrf=0.1,                        # 最终学习率（学习率衰减到最终学习率，帮助模型更好地收敛）
        optimizer='AdamW',              # 选择的优化器（如AdamW）

        # 数据增强相关参数
        hsv_v=0.4,                      # 色调变化范围
        scale=0.05,                     # 缩放变化范围
        shear=0.0,                      # 剪切变化范围
        perspective=0.001,              # 透视变化范围
        degrees=0.0,                    # 旋转角度范围
        mosaic=0.0,                     # 使用拼接增强
        flipud=0.5,                     # 垂直翻转概率

        # 正则化与损失函数相关参数
        cls=1.0,                        # 分类损失权重
        label_smoothing=0.02,           # 标签平滑参数（通过标签平滑技术减少模型对训练数据的过拟合）
    )
