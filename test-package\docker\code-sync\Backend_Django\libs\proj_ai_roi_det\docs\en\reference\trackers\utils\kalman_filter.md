---
description: Explore Kalman filter implementations like KalmanFilterXYAH and KalmanFilterXYWH for tracking bounding boxes in image space using Ultralytics.
keywords: Kalman Filter, Object Tracking, Python, Ultralytics, YOLO, Bounding Boxes, Image Processing
---

# Reference for `ultralytics/trackers/utils/kalman_filter.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/kalman_filter.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/kalman_filter.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/utils/kalman_filter.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.trackers.utils.kalman_filter.KalmanFilterXYAH

<br><br><hr><br>

## ::: ultralytics.trackers.utils.kalman_filter.KalmanFilterXYWH

<br><br>
