import cv2
import json
import shutil
import numpy as np
from pathlib import Path
from ultralytics import settings
from ultralytics.utils.plotting import Annotator, colors
from ultralytics.models.yolo.model import YOLO
from ultralytics.engine.results import Results

# 修改设置
root = Path(__file__).parent
settings.update({'datasets_dir': f"{Path(root).as_posix()}"})
settings.update({'weights_dir': f"{Path(root, 'weights').as_posix()}"})
settings.update({'runs_dir': f"{Path(root, 'runs').as_posix()}"})
print(settings)

# 可视化
def Visualize_BBox(image_file, predn:list, save_file, groundtruth=None):
    # 获取可视化工具
    annotator = Annotator(cv2.imread(image_file))
    bias = 18

    # 按置信度从第到高逐个绘制边框
    predn.reverse()
    predn = np.array(predn)
    for pred in predn:
        box = pred[:4]
        conf = pred[4]
        cls = int(pred[5])
        annotator.box_label(box, f"{cls} {conf:.2f}", colors(cls+bias))

    if groundtruth is not None:
        # 可视化ground truth
        gt_annotator = Annotator(cv2.imread(image_file))
        for gt in groundtruth:
            box = gt[:4]
            cls = int(gt[4])
            gt_annotator.box_label(box, f"{cls}", color=colors(cls+bias))
        # 拼接图像
        height = gt_annotator.im.shape[0]
        blank_width = 10  # 空白区域的宽度
        blank_image = np.ones((height, blank_width, 3), dtype=np.uint8) * 255
        result = cv2.hconcat([gt_annotator.im, blank_image])
        result = cv2.hconcat([result, annotator.im])
        cv2.imwrite(save_file, result)
    else:
        # 保存结果
        annotator.save(save_file)

def is_all_target_predict_right(predict, conf_thre, iou_thre) -> bool:
    conf = np.array(predict["conf"])
    iou = np. array(predict["iou"])
    same = np.array(predict["same"])

    # iou和conf满足要求
    iou_satisfied = iou > iou_thre
    conf_satisfied = conf.reshape(1, -1) > conf_thre
    thre_ok = conf_satisfied & iou_satisfied

    # cls一致
    thre_ok = thre_ok & same

    # # debug
    # if Path(predict["image"]).stem == "520":
    #     print(iou)
    #     print(same)

    # 每个目标都正确预测
    result = thre_ok.any(axis = 1).all()

    return result

# 分析完整预测情况
def Analyze_Full_Predict(val_path, save_path, model):
    # 定义存储路径
    source_path = Path(save_path, "source") # 没有预测全的结果的保存路径
    predict_path = Path(save_path, "predict") # 可视化结果保存路径
    trace_path = Path(save_path, "trace") # 跟踪路径

    # 创建存储路径
    shutil.rmtree(save_path, ignore_errors=True)
    Path(source_path).mkdir(exist_ok=True, parents=True)
    Path(predict_path).mkdir(exist_ok=True, parents=True)
    Path(trace_path).mkdir(exist_ok=True, parents=True)

    # 读取结果文件
    with open(Path(val_path, "predictions.json"), "r") as f:
        predictions = json.loads(f.read())
    for predict in predictions:
        all_right = is_all_target_predict_right(predict, conf_thre, iou_thre)
        if not all_right:
            image_file = Path(predict["image"])
            shutil.copy(image_file, source_path) # 拷贝图片
            shutil.copy(Path(image_file.parents[1], "traces", image_file.stem + ".txt"), trace_path) # 拷贝追踪数据
            Visualize_BBox(image_file, predict["predn"], Path(predict_path, f"{image_file.stem}.jpg").as_posix(), groundtruth=predict["gt"])

def is_false_positive_inside(predict, conf_thre, iou_thre):
    conf = np.array(predict["conf"])
    iou = np. array(predict["iou"])
    same = np.array(predict["same"])

    # iou和conf满足要求
    conf_satisfied = conf > conf_thre
    iou = iou[:, conf_satisfied]
    same = same[:, conf_satisfied]
    iou_satisfied = iou > iou_thre

    # # debug
    # if Path(predict["image"]).stem == "559":
    #     print(iou)
    #     print(conf)

    # cls一致
    thre_ok = iou_satisfied & same

    # 有误检的情况
    result = ~thre_ok.any(axis = 0).all()

    return result

# 分析误检
def Analyze_False_Positive(val_path, save_path, model):
    # 定义存储路径
    source_path = Path(save_path, "source") # 没有预测全的结果的保存路径
    predict_path = Path(save_path, "predict") # 可视化结果保存路径
    trace_path = Path(save_path, "trace") # 跟踪路径

    # 创建存储路径
    shutil.rmtree(save_path, ignore_errors=True)
    Path(source_path).mkdir(exist_ok=True, parents=True)
    Path(predict_path).mkdir(exist_ok=True, parents=True)
    Path(trace_path).mkdir(exist_ok=True, parents=True)

    # 读取结果文件
    with open(Path(val_path, "predictions.json"), "r") as f:
        predictions = json.loads(f.read())
    for predict in predictions:
        false_positive_inside = is_false_positive_inside(predict, conf_thre, iou_thre)
        if false_positive_inside:
            image_file = Path(predict["image"])
            shutil.copy(image_file, source_path) # 拷贝图片
            shutil.copy(Path(image_file.parents[1], "traces", image_file.stem + ".txt"), trace_path) # 拷贝追踪数据
            Visualize_BBox(image_file, predict["predn"], Path(predict_path, f"{image_file.stem}.jpg").as_posix())

if __name__ == "__main__":
    # 阈值设置
    conf_thre = 0.35
    iou_thre = 0.7

    # 路径设置
    project = "../runs/AI_ROI_Det/V1.1.0.22"
    project_name = "Analyze"
    val_path = Path(project, project_name)

    # 加载模型
    model = YOLO("../runs/AI_ROI_Det/V1.1.0.22/weights/best.pt")
    # 执行验证
    shutil.rmtree(val_path, ignore_errors=True)
    model.val(data="../mindeo/roi.yaml", split="test", imgsz=320, project=project, name=project_name, save_json=True, conf=conf_thre)


    # 完整预测情况
    save_path = Path(project, "FullPredict")  # 更新保存路径
    Analyze_Full_Predict(val_path, save_path, model)

    # 误检情况
    save_path = Path(project, "FalsePositive")  # 更新保存路径
    Analyze_False_Positive(val_path, save_path, model)