/* src/App.css */

/* 基础样式重置 */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 应用布局 */
.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 内容区域样式 */
.ant-layout-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex; /* 确保 Content 是 Flex 容器，帮助 Allotment 正确计算高度 */
}

/* Allotment 容器样式 */
.allotment {
  width: 100%;
  height: 100%;
}

/* Allotment 分隔条样式 */
/* 使用 allotment 默认或自定义的类名，这里用 allotment-sash 作为示例 */
.allotment-sash {
  background-color: #f0f0f0; /* 浅灰色分隔条 */
  z-index: 1;
  position: relative;
}

.allotment-sash:hover {
  background-color: #1677ff; /* Antd 主题蓝 */
}

.allotment-sash.split-vertical {
  width: 5px !important; /* 调整分隔条宽度 */
  cursor: col-resize;
  border-left: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
}

.allotment-sash.split-horizontal {
  height: 5px !important; /* 调整分隔条高度 */
  cursor: row-resize;
  border-top: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
}

/* 面板通用样式 */
.allotment-pane {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 确保所有组件容器都能正确填充其父容器 */
.allotment-pane > div {
  height: 100%;
  width: 100%;
  overflow: auto; /* 保留内部滚动 */
}

/* 状态栏样式 */
.ant-layout-footer {
  padding: 0 16px;
  height: 24px;
  line-height: 24px;
  background: #f0f2f5;
  border-top: 1px solid #d9d9d9;
  flex-shrink: 0; /* 确保状态栏不被压缩 */
}

/* 如果需要自定义 Pane 内的滚动条样式，可以在这里添加 */
/* 例如： */
/* .allotment-pane > div::-webkit-scrollbar {
  width: 6px;
}
.allotment-pane > div::-webkit-scrollbar-thumb {
  background-color: #cccccc;
  border-radius: 3px;
} */

/* FunctionTree antd override - Selected Item Color */
.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: #096dd9 !important; /* 更深的蓝色背景 for selected item */
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected .ant-tree-title {
  color: #ffffff !important; /* 确保文字在深色背景下可见 */
}

/* 覆盖选中项的展开/折叠图标颜色 */
.ant-tree .ant-tree-treenode-selected .ant-tree-switcher .ant-tree-switcher-icon svg {
  color: #ffffff !important;
}

/* _覆盖选中项的自定义图标（如果是 antd icon）颜色_ */
.ant-tree .ant-tree-treenode-selected .ant-tree-iconEle .anticon {
  color: #ffffff !important;
}

/* 如果自定义图标是 img, 尝试用 filter (可能效果不完美或需要调整) */
.ant-tree .ant-tree-treenode-selected .ant-tree-iconEle img {
  filter: brightness(0) invert(1);
}

/* 增强Radio.Button选中状态的背景色 */
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background-color: #096dd9 !important; /* 更深的蓝色作为选中状态 */
  color: white !important;
  border-color: #096dd9 !important;
}

/* 增强Radio.Button选中并且鼠标悬停时的样式 */
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  background-color: #0050b3 !important; /* 悬停时更深的颜色 */
  color: white !important;
  border-color: #0050b3 !important;
}

/* 增强Button主按钮选中状态的背景色 */
.ant-btn-primary {
  background-color: #1890ff !important; /* 主色调 */
  color: white !important; /* 确保文字为白色 */
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #40a9ff !important; /* 鼠标悬停时的颜色 */
  border-color: #40a9ff !important;
  color: white !important; /* 确保悬停时文字仍为白色 */
}

.ant-btn-primary:active {
  background-color: #096dd9 !important; /* 点击时的颜色，更深 */
  border-color: #096dd9 !important;
  color: white !important; /* 确保点击时文字仍为白色 */
}

/* 确保所有其他蓝色背景按钮的文字为白色 */
.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary[disabled]:active {
  color: rgba(255, 255, 255, 0.65) !important; /* 禁用状态下的白色文字，带透明度 */
}

/* 其他类型的按钮，当它们有蓝色背景时也应该有白色文字 */
.ant-btn-default.ant-btn-primary,
.ant-btn-dashed.ant-btn-primary,
.ant-btn-text.ant-btn-primary,
.ant-btn-link.ant-btn-primary {
  color: white !important;
}

/* 增强菜单栏按钮悬停效果 */
.menu-bar .ant-btn-text:hover,
.menu-bar .menu-btn:hover {
  background-color: rgba(0, 0, 0, 0.15) !important; /* 半透明黑色背景，增强悬停效果 */
  color: white !important;
}

/* 增强树状菜单悬停效果 */
.ant-tree .ant-tree-node-content-wrapper:hover,
.function-tree .ant-tree-node-content-wrapper:hover {
  background-color: #e8e8e8 !important; /* 较深的灰色，增强悬停效果 */
}

/* 额外增强功能树特定的悬停效果 */
.function-tree-container .ant-tree-treenode:hover > .ant-tree-node-content-wrapper {
  background-color: #e8e8e8 !important; /* 较深的灰色，增强悬停效果 */
}

/* 增强菜单项的悬停效果 */
.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover {
  background-color: #e6f4ff !important; /* 较深的蓝灰色，增强悬停效果 */
}

/* 图像导航按钮样式 */
.image-nav-button {
  transition: all 0.3s ease !important;
}

.image-nav-button:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  transform: scale(1.05) !important;
}

.image-nav-button:focus {
  outline: none !important;
}

/* 图像导航列样式 */
.image-nav-column {
  transition: background 0.3s ease !important;
}

.image-nav-column:hover {
  background: linear-gradient(to right, rgba(24, 144, 255, 0.2), transparent) !important;
}

.image-nav-column.right:hover {
  background: linear-gradient(to left, rgba(24, 144, 255, 0.2), transparent) !important;
}

/* 禁用导航按钮的样式覆盖 */
.image-nav-button.ant-btn[disabled] {
  background: rgba(245, 245, 245, 0.8) !important;
  border-color: #e8e8e8 !important;
  color: #bdbdbd !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
  box-shadow: none !important;
}
