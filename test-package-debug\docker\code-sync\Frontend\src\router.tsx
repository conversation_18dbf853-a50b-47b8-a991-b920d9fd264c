import { RouteObject } from 'react-router-dom';
// 只导入当前实际使用的页面组件
import DashboardLayout from './layouts/DashboardLayout'; // 导入布局
import DashboardPage from './pages/DashboardPage'; // 导入页面
// 管理页面相关组件
import AdminLayout from './layouts/AdminLayout'; // 管理页面布局
import AdminProtectedRoute from './components/AdminProtectedRoute'; // 权限保护组件
import AdminDashboardPage from './pages/AdminDashboardPage'; // 管理主页
import AdminModelsPage from './pages/AdminModelsPage'; // 模型管理页面
import AdminImagesPage from './pages/AdminImagesPage'; // 示例图片管理页面

export const routes: RouteObject[] = [
  {
    path: '/', // 根路径直接渲染 DashboardLayout
    element: <DashboardLayout />,
    children: [
      {
        index: true, // 默认渲染 DashboardPage
        element: <DashboardPage />,
      },
      // 未来可以添加其他相同布局的页面
      // { path: 'settings', element: <SettingsPage /> },
    ],
  },
  // 管理页面路由 - 需要管理员权限
  {
    path: '/admin',
    element: (
      <AdminProtectedRoute>
        <AdminLayout />
      </AdminProtectedRoute>
    ),
    children: [
      {
        index: true, // /admin 默认渲染管理主页
        element: <AdminDashboardPage />,
      },
      {
        path: 'models', // /admin/models 模型管理页面
        element: <AdminModelsPage />,
      },
      {
        path: 'images', // /admin/images 示例图片管理页面
        element: <AdminImagesPage />,
      },
    ],
  },
  // 可以添加 404 Not Found 页面
  // { path: '*', element: <NotFoundPage /> },
];