---
description: Learn to integrate DVCLive with Ultralytics for enhanced logging during training. Step-by-step methods for setting up and optimizing DVC callbacks.
keywords: Ultralytics, DVC, DVCLive, machine learning, logging, training, callbacks, integration
---

# Reference for `ultralytics/utils/callbacks/dvc.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/dvc.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/dvc.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/dvc.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.dvc._log_images

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc._log_plots

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc._log_confusion_matrix

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc.on_pretrain_routine_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc.on_train_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc.on_train_epoch_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.dvc.on_train_end

<br><br>
