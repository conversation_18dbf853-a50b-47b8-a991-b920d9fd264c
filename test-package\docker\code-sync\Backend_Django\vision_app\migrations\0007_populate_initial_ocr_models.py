# Generated by Django 5.2.1 on 2025-05-15 07:26

from django.db import migrations
from django.utils import timezone # Import timezone

def populate_models(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    # 车牌检测模型
    AIModel.objects.using(db_alias).create(
        name='vehicle_license_plate_cn',
        description='Chinese vehicle license plate detection model (PaddleOCR)',
        model_file='ocr/car_liencese/AI_OCR_Det_CHNLP_NCHW_1x3x320x320',
        version='1.0', # You can adjust the version
        model_type='ocr',
        is_system_model=True,
        ocr_role='detection',
        uploaded_at=timezone.now() # Explicitly set for clarity, though default would also work
    )

    # 车牌识别模型
    AIModel.objects.using(db_alias).create(
        name='vehicle_license_plate_cn',
        description='Chinese vehicle license plate recognition model (PaddleOCR)',
        model_file='ocr/car_liencese/AI_OCR_Rec_CHNLP_NCHW_1x3x64x320',
        version='1.0', # You can adjust the version
        model_type='ocr',
        is_system_model=True,
        ocr_role='recognition',
        uploaded_at=timezone.now() # Explicitly set
    )

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0006_alter_aimodel_unique_together_aimodel_ocr_role_and_more'),
    ]

    operations = [
        migrations.RunPython(populate_models),
    ]
