import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Typography, Space, Breadcrumb, Avatar } from 'antd';
import {
  SettingOutlined,
  DatabaseOutlined,
  PictureOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  UserOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAdmin } from '../contexts/AdminContext';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

/**
 * 管理页面布局组件
 * 提供侧边栏导航、顶部栏和内容区域
 */
const AdminLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { logout: adminLogout } = useAdmin();
  const navigate = useNavigate();
  const location = useLocation();

  // 添加滚动条样式
  useEffect(() => {
    // 创建样式元素
    const styleId = 'admin-scrollbar-styles';
    let existingStyle = document.getElementById(styleId);

    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .admin-content-scroll {
          scrollbar-width: thin;
          scrollbar-color: #bfbfbf #f5f5f5;
        }
        .admin-content-scroll::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        .admin-content-scroll::-webkit-scrollbar-track {
          background: #f5f5f5;
          border-radius: 4px;
        }
        .admin-content-scroll::-webkit-scrollbar-thumb {
          background: #bfbfbf;
          border-radius: 4px;
          border: 1px solid #f5f5f5;
        }
        .admin-content-scroll::-webkit-scrollbar-thumb:hover {
          background: #999999;
        }
        .admin-content-scroll::-webkit-scrollbar-corner {
          background: #f5f5f5;
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      const styleElement = document.getElementById(styleId);
      if (styleElement && document.head.contains(styleElement)) {
        document.head.removeChild(styleElement);
      }
    };
  }, []);

  // 侧边栏菜单项
  const menuItems = [
    {
      key: '/admin',
      icon: <SettingOutlined />,
      label: '管理概览',
    },
    {
      key: '/admin/models',
      icon: <DatabaseOutlined />,
      label: '模型管理',
    },
    {
      key: '/admin/images',
      icon: <PictureOutlined />,
      label: '示例图片',
    },
  ];

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 处理返回首页
  const handleBackToHome = () => {
    navigate('/');
  };

  // 处理管理员登出
  const handleLogout = async () => {
    try {
      await adminLogout();
      navigate('/');
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  // 获取当前页面标题
  const getCurrentPageTitle = () => {
    const path = location.pathname;
    switch (path) {
      case '/admin':
        return '管理概览';
      case '/admin/models':
        return '模型管理';
      case '/admin/images':
        return '示例图片管理';
      default:
        return '管理后台';
    }
  };

  // 获取面包屑导航
  const getBreadcrumbItems = () => {
    const path = location.pathname;
    const items = [
      {
        title: (
          <span>
            <HomeOutlined />
            <span style={{ marginLeft: '4px' }}>首页</span>
          </span>
        ),
      },
      {
        title: (
          <span>
            <SettingOutlined />
            <span style={{ marginLeft: '4px' }}>管理后台</span>
          </span>
        ),
      },
    ];

    if (path !== '/admin') {
      items.push({
        title: (
          <span>{getCurrentPageTitle()}</span>
        ),
      });
    }

    return items;
  };

  return (
    <Layout style={{ height: '100vh', overflow: 'hidden' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          background: '#001529',
          boxShadow: '2px 0 8px rgba(0, 0, 0, 0.15)'
        }}
      >
        {/* Logo区域 */}
        <div style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(255, 255, 255, 0.1)',
          margin: '16px',
          borderRadius: '8px'
        }}>
          <SettingOutlined style={{
            fontSize: collapsed ? '24px' : '20px',
            color: '#1890ff'
          }} />
          {!collapsed && (
            <Title
              level={4}
              style={{
                color: '#ffffff',
                margin: '0 0 0 8px',
                fontSize: '16px',
                fontWeight: 600
              }}
            >
              管理后台
            </Title>
          )}
        </div>

        {/* 导航菜单 */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            borderRight: 'none',
            background: 'transparent'
          }}
        />
      </Sider>

      <Layout style={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
        {/* 顶部栏 */}
        <Header style={{
          padding: '0 24px',
          background: '#ffffff',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          flexShrink: 0,
          height: '64px'
        }}>
          {/* 左侧：折叠按钮和面包屑 */}
          <Space size="large">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            />
            <Breadcrumb items={getBreadcrumbItems()} />
          </Space>

          {/* 右侧：用户信息和操作按钮 */}
          <Space size="middle">
            <Button
              type="text"
              icon={<HomeOutlined />}
              onClick={handleBackToHome}
              style={{
                borderRadius: '6px',
                fontWeight: 500
              }}
            >
              返回首页
            </Button>

            <Space size="small">
              <Avatar
                icon={<UserOutlined />}
                style={{
                  backgroundColor: '#1890ff',
                  border: '2px solid #e8f4fd'
                }}
              />
              <Text strong style={{ color: '#333' }}>管理员</Text>
            </Space>

            <Button
              type="text"
              icon={<LogoutOutlined />}
              onClick={handleLogout}
              style={{
                color: '#ff4d4f',
                borderRadius: '6px',
                fontWeight: 500
              }}
            >
              登出
            </Button>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content
          className="admin-content-scroll"
          style={{
            flex: 1,
            margin: '12px',
            padding: '20px',
            background: '#ffffff',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            overflow: 'auto',
            overflowX: 'hidden',
            minHeight: 0, // 重要：允许flex子项收缩
            width: '100%' // 确保充分利用宽度
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;
