import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0', // 允许通过 IP 地址访问
    // 可以使用 netsh interface ipv4 show excludedportrange protocol=tcp 进行端口检查
    port: 8080,
    proxy: {
      '/api': {
        // 使用环境变量，支持不同部署环境
        // 开发环境: localhost:8000
        // Docker环境: backend:8000 (容器名)
        // 自定义环境: 通过 VITE_BACKEND_URL 环境变量指定
        target: process.env.VITE_BACKEND_URL || 'http://localhost:9000',    // 使用环境变量配置后端地址
        changeOrigin: true,
        secure: false, // 允许自签名证书
        // rewrite: (path) => path.replace(/^\/api/, '/api'), // 通常不用加
      }
    }
  }
})
