---
description: Discover the BaseTrack classes and methods for object tracking in YOLO by Ultralytics. Learn about TrackState, BaseTrack attributes, and methods.
keywords: Ultralytics, YOLO, object tracking, BaseTrack, TrackState, tracking methods, TrackState enumeration, object detection
---

# Reference for `ultralytics/trackers/basetrack.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/basetrack.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/basetrack.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/basetrack.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.trackers.basetrack.TrackState

<br><br><hr><br>

## ::: ultralytics.trackers.basetrack.BaseTrack

<br><br>
