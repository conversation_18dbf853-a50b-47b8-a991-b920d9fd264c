# 🚀 前端配置快速指南

## 📁 配置文件概览

```
Frontend/
├── .env.development      # 开发环境（默认）
├── .env.production       # 生产环境
├── .env.hotreload        # Docker热重载
├── .env.example          # 完整配置示例
└── .env.lan.example      # 局域网配置模板
```

## 🎯 快速开始

### 1️⃣ 本地开发（默认）
```bash
# 直接启动，使用 .env.development
npm run dev

# 访问地址
# 前端: http://localhost:5173
# 后端: http://localhost:9000
```

### 2️⃣ 局域网访问
```bash
# 1. 复制局域网配置模板
cp .env.lan.example .env.local

# 2. 修改 .env.local 中的IP地址
# VITE_BACKEND_HOST=你的IP地址
# VITE_FRONTEND_HOST=你的IP地址

# 3. 启动开发服务器
npm run dev

# 4. 局域网访问地址
# 前端: http://你的IP:5173
# 后端: http://你的IP:9000
```

### 3️⃣ 生产构建
```bash
# 生产构建
npm run build

# 预览构建结果
npm run preview
```

### 4️⃣ Docker热重载
```bash
# 构建Docker热重载版本
npm run build:hotreload

# 在Docker容器中运行
# 使用 .env.hotreload 配置
```

## ⚙️ 环境变量说明

### 🔧 核心配置
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_API_BASE_URL` | API基础URL | `/api` |
| `VITE_BACKEND_URL` | 后端服务地址 | `http://localhost:9000` |
| `VITE_ENVIRONMENT` | 环境标识 | `development` |

### 🌐 网络配置
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_BACKEND_HOST` | 后端主机 | `localhost` |
| `VITE_BACKEND_PORT` | 后端端口 | `9000` |
| `VITE_FRONTEND_HOST` | 前端主机 | `localhost` |
| `VITE_FRONTEND_PORT` | 前端端口 | `5173` |

## 🔍 配置检查

```bash
# 检查配置是否正确
npm run check-config

# 查看配置摘要
node -e "import('./src/config/index.js').then(c => console.log(c.getConfigSummary()))"
```

## 🛠️ 故障排除

### ❌ API请求失败
1. 检查后端是否运行在配置的端口
2. 验证 `VITE_BACKEND_URL` 配置
3. 检查网络连接和防火墙

### ❌ 局域网无法访问
1. 确认IP地址配置正确
2. 检查防火墙设置
3. 验证端口未被占用

### ❌ Docker环境问题
1. 确认使用了正确的构建模式
2. 检查容器网络配置
3. 验证nginx代理设置

## 📝 配置优先级

```
1. 命令行环境变量（最高）
2. .env.local 文件
3. .env.[mode] 文件（如 .env.development）
4. .env 文件
5. 默认值（最低）
```

## 🔗 相关文档

- [详细环境配置说明](./README_ENV.md)
- [网络配置管理](./src/config/README.md)
- [Vite环境变量文档](https://vitejs.dev/guide/env-and-mode.html)
