---
description: Learn how to use the Ultralytics Heatmap module for real-time video analysis with object tracking and heatmap generation.
keywords: Ultralytics, Heatmap, Python, Real-time Video, Object Tracking, cv2, Shapely, Computer Vision, AI
---

# Reference for `ultralytics/solutions/heatmap.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/heatmap.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/heatmap.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/heatmap.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.heatmap.Heatmap

<br><br>
