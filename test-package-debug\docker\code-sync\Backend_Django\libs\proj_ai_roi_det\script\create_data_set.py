from user_utils.proj_offline_data_augmentation.YOLO import ObjectDetectionYOLO
from tqdm import tqdm, trange
import os

def get_dir_files_count(path):
    """获取目录下文件数量"""
    if not os.path.exists(path):
        return 0
    return len([name for name in os.listdir(path) if os.path.isfile(os.path.join(path, name))])

if __name__ == "__main__":
    yolo = ObjectDetectionYOLO(["1D", "2D_QR", "2D_DM", "2D_others"])

    # 分配数据集
    print("正在分配数据集...")
    yolo.Distribute_Data_Set({
        "../mindeo/data/DS_1D2D_ROI/1D_Goods" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/1D_Industry" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_DM_Industry" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_DM_Life" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_DM_Small" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Industry1" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Industry2" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Industry3" : 1.0,     # 新增数据集
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Life1" : 1.0,         # 新增数据集
        "../mindeo/data/DS_1D2D_ROI/2D_QR_Industry" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_QR_Large" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_QR_Life" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/MutilCodes" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/MutilCodes1" : 1.0,             # 新增数据集
        }, 0.8, 0.1, "../mindeo/data_processed/DataSet", only_label=True, seed=20241004)
    print("数据集分配完成!")
    print(f"分配后的数据集已保存到：../mindeo/data_processed/DataSet")

    # 缩放图像
    print("\n开始缩放图像...")
    for data_type in tqdm(["train", "val", "test"], desc="缩放进度"):
        input_dir = f"../mindeo/data_processed/DataSet/{data_type}"
        total_files = get_dir_files_count(input_dir)
        print(f"\n处理 {data_type} 集合中的 {total_files} 个文件...")
        yolo.Resize_Image([
            input_dir,
            ], [320, 320], f"../mindeo/data_processed/Resize/{data_type}", keep_ratio=False, mode="float_factor")
    print(f"缩放后的图像已保存到：../mindeo/data_processed/Resize/{data_type}")

    # 对图片做反色处理
    print("\n开始反色处理...")
    for data_type in tqdm(["train", "val", "test"], desc="反色处理进度"):
        input_dir = f"../mindeo/data_processed/Resize/{data_type}"
        total_files = get_dir_files_count(input_dir)
        print(f"\n处理 {data_type} 集合中的 {total_files} 个文件...")
        yolo.Invert_Image([
            input_dir,
            ], f"../mindeo/data_processed/Inverted/{data_type}")
        print(f"反色处理后的图像已保存到：../mindeo/data_processed/Inverted/{data_type}")

    # 转换YOLO标签
    print("\n开始转换YOLO标签...")
    for data_type in tqdm(["train", "val", "test"], desc="标签转换进度"):
        input_dir = f"../mindeo/data_processed/Resize/{data_type}"
        total_files = get_dir_files_count(input_dir)
        print(f"\n处理 {data_type} 集合中的 {total_files} 个文件...")
        yolo.Labelme_To_YOLO([
            f"../mindeo/data_processed/Resize/{data_type}",
            f"../mindeo/data_processed/Inverted/{data_type}",
            ], f"../datasets/Train/{data_type}")
        print(f"转换后的YOLO标签已保存到：../datasets/Train/{data_type}")

    print("\n所有处理完成!")
