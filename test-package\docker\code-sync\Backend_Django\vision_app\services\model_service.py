"""
AI模型管理服务模块

专门处理AI模型相关的操作，包括：
- 模型加载和生命周期管理
- 模型路径解析和验证
- 模型缓存和性能优化
- 模型文件管理和存储
- 模型元数据管理

完整的ModelService架构：

class ModelService(BaseService):
    # ==================== 模型查询和获取 ====================
    def get_model_by_name()                # 根据名称获取模型
    def get_models_by_type()               # 根据类型获取模型列表
    def get_ocr_model_pair()               # 获取OCR模型对
    def validate_model_exists()            # 验证模型是否存在
    
    # ==================== 模型路径管理 ====================
    def get_model_path()                   # 获取模型文件路径
    def build_system_model_path()          # 构建系统模型路径
    def build_custom_model_path()          # 构建自定义模型路径
    def resolve_model_path()               # 智能路径解析
    
    # ==================== 模型加载和缓存 ====================
    def load_model()                       # 加载模型实例
    def get_cached_model()                 # 获取缓存的模型
    def cache_model()                      # 缓存模型实例
    def clear_model_cache()                # 清理模型缓存
    
    # ==================== 模型验证和检查 ====================
    def validate_model_file()              # 验证模型文件
    def check_model_integrity()            # 检查模型完整性
    def verify_model_format()              # 验证模型格式
    def validate_model_compatibility()     # 验证模型兼容性
    
    # ==================== 模型生命周期管理 ====================
    def register_model()                   # 注册新模型
    def update_model_metadata()            # 更新模型元数据
    def delete_model()                     # 删除模型
    def backup_model()                     # 备份模型文件

"""

import os
import hashlib
import time
from typing import Dict, Any, Optional, List, Tuple, Union
from pathlib import Path
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile

from .base import BaseService
from .file_service import FileService
from ..models import AIModel


class ModelService(BaseService):
    """
    AI模型管理服务类
    
    专门处理AI模型的加载、路径管理、验证、缓存等操作。
    """
    
    # 支持的模型格式
    SUPPORTED_MODEL_FORMATS = {
        'barcode': ['.pt'],
        'ocr': ['.tar', '.tar.gz', '.zip'],
        'ai_restored': ['.onnx'],
        'feature_matching': ['.onnx']
    }
    
    # 模型缓存
    _model_cache = {}
    _cache_timestamps = {}
    
    # 缓存配置
    CACHE_TIMEOUT = 3600  # 1小时
    MAX_CACHE_SIZE = 10   # 最大缓存模型数量
    
    def __init__(self):
        """初始化模型服务"""
        super().__init__()
        self.file_service = FileService()
    
    # ==================== 模型查询和获取 ====================
    
    def get_model_by_name(self, name: str, model_type: str, 
                         is_system_model: Optional[bool] = None) -> AIModel:
        """
        根据名称获取模型
        
        Args:
            name: 模型名称
            model_type: 模型类型
            is_system_model: 是否为系统模型（可选）
            
        Returns:
            AIModel实例
            
        Raises:
            ValueError: 模型不存在或存在多个
        """
        try:
            queryset = AIModel.objects.filter(name=name, model_type=model_type)
            
            if is_system_model is not None:
                queryset = queryset.filter(is_system_model=is_system_model)
            
            if queryset.count() == 0:
                raise ValueError(f'名为 {name} 的 {model_type} 模型未找到')
            elif queryset.count() > 1:
                raise ValueError(f'发现多个名为 {name} 的 {model_type} 模型记录')
            
            model = queryset.first()
            self.log_info(f"Found model: {model.name} (ID: {model.id}, type: {model.model_type})")
            return model
            
        except Exception as e:
            self.log_error(f"Error getting model by name: {str(e)}")
            raise
    
    def get_models_by_type(self, model_type: str, 
                          is_system_model: Optional[bool] = None) -> List[AIModel]:
        """
        根据类型获取模型列表
        
        Args:
            model_type: 模型类型
            is_system_model: 是否为系统模型（可选）
            
        Returns:
            AIModel实例列表
        """
        try:
            queryset = AIModel.objects.filter(model_type=model_type)
            
            if is_system_model is not None:
                queryset = queryset.filter(is_system_model=is_system_model)
            
            models = list(queryset.order_by('name'))
            self.log_info(f"Found {len(models)} models of type '{model_type}'")
            return models
            
        except Exception as e:
            self.log_error(f"Error getting models by type: {str(e)}")
            raise
    
    def get_ocr_model_pair(self, ocr_task_name: str) -> Tuple[AIModel, AIModel]:
        """
        获取OCR模型对（检测+识别）

        Args:
            ocr_task_name: OCR任务名称

        Returns:
            (检测模型, 识别模型)

        Raises:
            ValueError: 模型对不完整
        """
        try:
            # 首先尝试使用ocr_collection_name查找
            det_models = AIModel.objects.filter(
                model_type='ocr',
                ocr_role='detection',
                ocr_collection_name=ocr_task_name,
                is_system_model=True
            )

            rec_models = AIModel.objects.filter(
                model_type='ocr',
                ocr_role='recognition',
                ocr_collection_name=ocr_task_name,
                is_system_model=True
            )

            # 如果使用ocr_collection_name没找到，尝试使用name字段
            if not det_models.exists():
                det_models = AIModel.objects.filter(
                    model_type='ocr',
                    ocr_role='detection',
                    name=ocr_task_name,
                    is_system_model=True
                )

            if not rec_models.exists():
                rec_models = AIModel.objects.filter(
                    model_type='ocr',
                    ocr_role='recognition',
                    name=ocr_task_name,
                    is_system_model=True
                )

            if not det_models.exists():
                raise ValueError(f'未找到OCR任务 "{ocr_task_name}" 的检测模型')
            if not rec_models.exists():
                raise ValueError(f'未找到OCR任务 "{ocr_task_name}" 的识别模型')

            det_model = det_models.first()
            rec_model = rec_models.first()

            self.log_info(f"Found OCR model pair for '{ocr_task_name}': "
                         f"det={det_model.name}, rec={rec_model.name}")

            return det_model, rec_model

        except Exception as e:
            self.log_error(f"Error getting OCR model pair: {str(e)}")
            raise
    
    def validate_model_exists(self, model_id: int) -> bool:
        """
        验证模型是否存在
        
        Args:
            model_id: 模型ID
            
        Returns:
            是否存在
        """
        try:
            return AIModel.objects.filter(id=model_id).exists()
        except Exception as e:
            self.log_error(f"Error validating model exists: {str(e)}")
            return False

    # ==================== 模型路径管理 ====================

    def get_model_path(self, model: AIModel) -> str:
        """
        获取模型文件路径

        Args:
            model: AIModel实例

        Returns:
            模型文件的完整路径

        Raises:
            FileNotFoundError: 模型文件不存在
            ValueError: 路径配置错误
        """
        try:
            if model.is_system_model:
                model_path = self.build_system_model_path(model)
            else:
                model_path = self.build_custom_model_path(model)

            # 验证文件存在
            if not os.path.exists(model_path):
                # 尝试智能路径解析
                resolved_path = self.resolve_model_path(model)
                if resolved_path and os.path.exists(resolved_path):
                    model_path = resolved_path
                else:
                    raise FileNotFoundError(f'模型文件不存在: {model_path}')

            self.log_info(f"Model path resolved: {model_path}")
            return model_path

        except Exception as e:
            self.log_error(f"Error getting model path: {str(e)}")
            raise

    def build_system_model_path(self, model: AIModel) -> str:
        """
        构建系统模型路径

        Args:
            model: AIModel实例

        Returns:
            系统模型的完整路径
        """
        system_models_root = self.get_setting('SYSTEM_MODELS_ROOT')
        if not system_models_root:
            raise ValueError('SYSTEM_MODELS_ROOT未配置')

        if not model.model_file or not model.model_file.name:
            raise ValueError(f'系统模型 {model.name} 记录缺少模型文件名')

        # 处理不同的路径格式
        model_filename = os.path.basename(model.model_file.name)

        # 根据模型类型构建路径
        if model.model_type == 'ocr':
            # OCR模型通常在 inference 子目录中
            model_path = os.path.join(system_models_root, model.model_file.name, 'inference')
        else:
            # 其他模型类型
            if model.model_type in model.model_file.name:
                model_path = os.path.join(system_models_root, model.model_file.name)
            else:
                model_path = os.path.join(system_models_root, model.model_type, model_filename)

        return model_path

    def build_custom_model_path(self, model: AIModel) -> str:
        """
        构建自定义模型路径

        Args:
            model: AIModel实例

        Returns:
            自定义模型的完整路径
        """
        if not model.model_file:
            raise ValueError(f'自定义模型 {model.name} 记录缺少模型文件引用')

        # 自定义模型使用 MEDIA_ROOT
        media_root = self.get_setting('MEDIA_ROOT')
        if not media_root:
            # 如果没有配置MEDIA_ROOT，使用model_file.path
            return model.model_file.path

        model_path = os.path.join(media_root, model.model_file.name)
        return model_path

    def resolve_model_path(self, model: AIModel) -> Optional[str]:
        """
        智能路径解析，尝试多种可能的路径

        Args:
            model: AIModel实例

        Returns:
            解析成功的路径，如果都不存在则返回None
        """
        alternative_paths = []

        if model.is_system_model:
            base_dir = self.get_setting('BASE_DIR')
            model_name = model.name
            model_type = model.model_type

            # 生成多种可能的路径
            if model_type == 'ai_restored':
                alternative_paths.extend([
                    os.path.join(base_dir, 'models', 'system_models', 'ai_restored', f"{model_name}.onnx"),
                    os.path.join(base_dir, 'models', 'system_models', 'ai_restored', model_name),
                    os.path.join(base_dir, 'models', 'ai_restored', f"{model_name}.onnx"),
                ])
            elif model_type == 'barcode':
                alternative_paths.extend([
                    os.path.join(base_dir, 'models', 'system_models', 'barcode', f"{model_name}.pt"),
                    os.path.join(base_dir, 'models', 'barcode', f"{model_name}.pt"),
                ])
            elif model_type == 'feature_matching':
                alternative_paths.extend([
                    os.path.join(base_dir, 'models', 'system_models', 'feature_matching', f"{model_name}.onnx"),
                    os.path.join(base_dir, 'models', 'feature_matching', f"{model_name}.onnx"),
                ])

        # 检查每个可能的路径
        for path in alternative_paths:
            if os.path.exists(path):
                self.log_info(f"Resolved model path: {path}")
                return path

        return None

    # ==================== 模型加载和缓存 ====================

    def load_model(self, model: AIModel, **kwargs) -> Any:
        """
        加载模型实例

        Args:
            model: AIModel实例
            **kwargs: 模型加载的额外参数

        Returns:
            加载的模型实例

        Raises:
            Exception: 模型加载失败
        """
        try:
            # 检查缓存
            cache_key = self._get_cache_key(model)
            cached_model = self.get_cached_model(cache_key)
            if cached_model:
                self.log_info(f"Using cached model: {model.name}")
                return cached_model

            # 获取模型路径
            model_path = self.get_model_path(model)

            # 根据模型类型加载
            loaded_model = self._load_model_by_type(model, model_path, **kwargs)

            # 缓存模型
            self.cache_model(cache_key, loaded_model)

            self.log_info(f"Successfully loaded model: {model.name}")
            return loaded_model

        except Exception as e:
            self.log_error(f"Error loading model {model.name}: {str(e)}")
            raise

    def get_cached_model(self, cache_key: str) -> Optional[Any]:
        """
        获取缓存的模型

        Args:
            cache_key: 缓存键

        Returns:
            缓存的模型实例，如果不存在或过期则返回None
        """
        if cache_key not in self._model_cache:
            return None

        # 检查缓存是否过期
        if cache_key in self._cache_timestamps:
            cache_time = self._cache_timestamps[cache_key]
            if time.time() - cache_time > self.CACHE_TIMEOUT:
                self._remove_from_cache(cache_key)
                return None

        return self._model_cache[cache_key]

    def cache_model(self, cache_key: str, model_instance: Any) -> None:
        """
        缓存模型实例

        Args:
            cache_key: 缓存键
            model_instance: 模型实例
        """
        # 检查缓存大小限制
        if len(self._model_cache) >= self.MAX_CACHE_SIZE:
            self._evict_oldest_cache()

        self._model_cache[cache_key] = model_instance
        self._cache_timestamps[cache_key] = time.time()

        self.log_debug(f"Cached model with key: {cache_key}")

    def clear_model_cache(self, model_type: Optional[str] = None) -> None:
        """
        清理模型缓存

        Args:
            model_type: 要清理的模型类型，如果为None则清理所有缓存
        """
        if model_type is None:
            # 清理所有缓存
            self._model_cache.clear()
            self._cache_timestamps.clear()
            self.log_info("Cleared all model cache")
        else:
            # 清理特定类型的缓存
            keys_to_remove = [key for key in self._model_cache.keys() if model_type in key]
            for key in keys_to_remove:
                self._remove_from_cache(key)
            self.log_info(f"Cleared cache for model type: {model_type}")

    def _get_cache_key(self, model: AIModel) -> str:
        """生成缓存键"""
        return f"{model.model_type}_{model.name}_{model.id}"

    def _remove_from_cache(self, cache_key: str) -> None:
        """从缓存中移除指定项"""
        if cache_key in self._model_cache:
            del self._model_cache[cache_key]
        if cache_key in self._cache_timestamps:
            del self._cache_timestamps[cache_key]

    def _evict_oldest_cache(self) -> None:
        """移除最旧的缓存项"""
        if not self._cache_timestamps:
            return

        oldest_key = min(self._cache_timestamps.keys(),
                        key=lambda k: self._cache_timestamps[k])
        self._remove_from_cache(oldest_key)
        self.log_debug(f"Evicted oldest cache: {oldest_key}")

    def _load_model_by_type(self, model: AIModel, model_path: str, **kwargs) -> Any:
        """
        根据模型类型加载模型

        Args:
            model: AIModel实例
            model_path: 模型文件路径
            **kwargs: 额外参数

        Returns:
            加载的模型实例
        """
        if model.model_type == 'barcode':
            return self._load_yolo_model(model_path, **kwargs)
        elif model.model_type == 'ai_restored':
            return self._load_onnx_model(model_path, **kwargs)
        elif model.model_type == 'feature_matching':
            return self._load_onnx_model(model_path, **kwargs)
        elif model.model_type == 'ocr':
            # OCR模型需要特殊处理，通常不直接加载单个文件
            raise NotImplementedError("OCR模型加载需要通过专门的OCR预测器处理")
        else:
            raise ValueError(f"不支持的模型类型: {model.model_type}")

    def _load_yolo_model(self, model_path: str, **kwargs):
        """加载YOLO模型"""
        try:
            from ultralytics import YOLO
            model = YOLO(model_path)
            return model
        except ImportError:
            raise ImportError("需要安装ultralytics库来加载YOLO模型")
        except Exception as e:
            raise Exception(f"YOLO模型加载失败: {str(e)}")

    def _load_onnx_model(self, model_path: str, **kwargs):
        """加载ONNX模型"""
        try:
            import onnxruntime as ort
            session = ort.InferenceSession(model_path)
            return session
        except ImportError:
            raise ImportError("需要安装onnxruntime库来加载ONNX模型")
        except Exception as e:
            raise Exception(f"ONNX模型加载失败: {str(e)}")

    # ==================== 模型验证和检查 ====================

    def validate_model_file(self, file: UploadedFile, model_type: str) -> Tuple[bool, Optional[str]]:
        """
        验证模型文件（委托给FileService）

        Args:
            file: 上传的文件对象
            model_type: 模型类型

        Returns:
            (是否有效, 错误消息)
        """
        return self.file_service.validate_file(file, 'model', model_type=model_type)

    def check_model_integrity(self, model_path: str, model_type: str) -> bool:
        """
        检查模型文件完整性

        Args:
            model_path: 模型文件路径
            model_type: 模型类型

        Returns:
            是否完整
        """
        try:
            if not os.path.exists(model_path):
                return False

            # 检查文件大小
            file_size = os.path.getsize(model_path)
            if file_size == 0:
                return False

            # 根据模型类型进行特定检查
            if model_type == 'barcode':
                return self._check_yolo_integrity(model_path)
            elif model_type in ['ai_restored', 'feature_matching']:
                return self._check_onnx_integrity(model_path)
            elif model_type == 'ocr':
                return self._check_ocr_integrity(model_path)

            return True

        except Exception as e:
            self.log_error(f"Error checking model integrity: {str(e)}")
            return False

    def verify_model_format(self, model_path: str, expected_format: str) -> bool:
        """
        验证模型格式

        Args:
            model_path: 模型文件路径
            expected_format: 期望的格式

        Returns:
            格式是否正确
        """
        try:
            file_ext = os.path.splitext(model_path)[1].lower()
            return file_ext == expected_format.lower()
        except Exception:
            return False

    def validate_model_compatibility(self, model: AIModel) -> Tuple[bool, Optional[str]]:
        """
        验证模型兼容性

        Args:
            model: AIModel实例

        Returns:
            (是否兼容, 错误消息)
        """
        try:
            # 检查模型文件是否存在
            model_path = self.get_model_path(model)

            # 检查文件完整性
            if not self.check_model_integrity(model_path, model.model_type):
                return False, "模型文件损坏或不完整"

            # 检查模型类型特定的兼容性
            if model.model_type == 'ocr':
                return self._validate_ocr_compatibility(model)
            elif model.model_type == 'barcode':
                return self._validate_barcode_compatibility(model)
            elif model.model_type == 'ai_restored':
                return self._validate_ai_restored_compatibility(model)
            elif model.model_type == 'feature_matching':
                return self._validate_feature_matching_compatibility(model)

            return True, None

        except Exception as e:
            return False, f"兼容性检查失败: {str(e)}"

    def _check_yolo_integrity(self, model_path: str) -> bool:
        """检查YOLO模型完整性"""
        try:
            # 简单的文件头检查
            with open(model_path, 'rb') as f:
                header = f.read(8)
                # PyTorch模型通常以特定的魔数开始
                return len(header) == 8
        except Exception:
            return False

    def _check_onnx_integrity(self, model_path: str) -> bool:
        """检查ONNX模型完整性"""
        try:
            # 尝试读取ONNX文件头
            with open(model_path, 'rb') as f:
                header = f.read(16)
                # ONNX文件通常以特定的标识开始
                return b'onnx' in header.lower() or len(header) == 16
        except Exception:
            return False

    def _check_ocr_integrity(self, model_path: str) -> bool:
        """检查OCR模型完整性"""
        try:
            # OCR模型通常是目录结构，检查是否为目录
            if os.path.isdir(model_path):
                # 检查是否包含必要的文件
                required_files = ['inference.pdmodel', 'inference.pdiparams']
                return any(os.path.exists(os.path.join(model_path, f)) for f in required_files)
            return False
        except Exception:
            return False

    def _validate_ocr_compatibility(self, model: AIModel) -> Tuple[bool, Optional[str]]:
        """验证OCR模型兼容性"""
        if not model.ocr_role:
            return False, "OCR模型必须指定角色（detection或recognition）"

        if model.ocr_role not in ['detection', 'recognition']:
            return False, "无效的OCR角色"

        return True, None

    def _validate_barcode_compatibility(self, model: AIModel) -> Tuple[bool, Optional[str]]:
        """验证条码检测模型兼容性"""
        # 条码模型通常没有特殊要求
        return True, None

    def _validate_ai_restored_compatibility(self, model: AIModel) -> Tuple[bool, Optional[str]]:
        """验证AI图像修复模型兼容性"""
        # AI修复模型通常没有特殊要求
        return True, None

    def _validate_feature_matching_compatibility(self, model: AIModel) -> Tuple[bool, Optional[str]]:
        """验证特征匹配模型兼容性"""
        # 特征匹配模型通常没有特殊要求
        return True, None

    # ==================== 模型生命周期管理 ====================

    def register_model(self, model_data: Dict[str, Any]) -> AIModel:
        """
        注册新模型

        Args:
            model_data: 模型数据字典

        Returns:
            创建的AIModel实例

        Raises:
            ValueError: 数据验证失败
        """
        try:
            # 验证必需字段
            required_fields = ['name', 'model_type']
            for field in required_fields:
                if field not in model_data:
                    raise ValueError(f"缺少必需字段: {field}")

            # 检查模型名称唯一性
            existing_model = AIModel.objects.filter(
                name=model_data['name'],
                model_type=model_data['model_type']
            ).first()

            if existing_model:
                raise ValueError(f"模型名称 '{model_data['name']}' 在类型 '{model_data['model_type']}' 中已存在")

            # 创建模型实例
            model = AIModel.objects.create(**model_data)

            self.log_info(f"Successfully registered model: {model.name} (ID: {model.id})")
            return model

        except Exception as e:
            self.log_error(f"Error registering model: {str(e)}")
            raise

    def update_model_metadata(self, model_id: int, update_data: Dict[str, Any]) -> AIModel:
        """
        更新模型元数据

        Args:
            model_id: 模型ID
            update_data: 更新数据

        Returns:
            更新后的AIModel实例

        Raises:
            ValueError: 模型不存在或数据无效
        """
        try:
            model = AIModel.objects.get(id=model_id)

            # 更新允许的字段
            allowed_fields = ['name', 'description', 'version', 'ocr_collection_name']

            for field, value in update_data.items():
                if field in allowed_fields:
                    setattr(model, field, value)

            model.save()

            # 清理相关缓存
            cache_key = self._get_cache_key(model)
            self._remove_from_cache(cache_key)

            self.log_info(f"Successfully updated model metadata: {model.name}")
            return model

        except AIModel.DoesNotExist:
            raise ValueError(f"模型ID {model_id} 不存在")
        except Exception as e:
            self.log_error(f"Error updating model metadata: {str(e)}")
            raise

    def batch_delete_models(self, model_ids: List[int]) -> Dict[str, Any]:
        """
        批量删除AI模型

        Args:
            model_ids: 要删除的模型ID列表

        Returns:
            删除结果字典，包含成功和失败的详情

        Raises:
            ValueError: 当参数无效时
        """
        if not isinstance(model_ids, list) or not model_ids:
            raise ValueError('请提供要删除的模型ID列表')

        self.log_info(f"Batch deleting AI models: {model_ids}")

        deleted_models = []
        failed_models = []

        for model_id in model_ids:
            try:
                # 获取模型实例
                model = AIModel.objects.get(id=model_id)

                # 记录模型信息用于日志和响应
                model_info = f"{model.name} (ID: {model.id}, Type: {model.model_type})"
                model_data = {
                    'id': model_id,
                    'name': model.name,
                    'model_type': model.model_type
                }

                # 清理相关缓存
                cache_key = self._get_cache_key(model)
                self._remove_from_cache(cache_key)

                # 删除模型（包括文件和数据库记录）
                model.delete()

                deleted_models.append(model_data)
                self.log_info(f"Successfully deleted model: {model_info}")

            except AIModel.DoesNotExist:
                failed_models.append({
                    'id': model_id,
                    'error': '模型不存在'
                })
                self.log_warning(f"Model with ID {model_id} does not exist")
            except Exception as e:
                failed_models.append({
                    'id': model_id,
                    'error': str(e)
                })
                self.log_error(f"Failed to delete model with ID {model_id}: {str(e)}")

        # 构建响应
        result = {
            'success': len(deleted_models) > 0,
            'deleted': deleted_models,
            'failed': failed_models,
            'message': f'成功删除 {len(deleted_models)} 个模型，失败 {len(failed_models)} 个'
        }

        self.log_info(f"Batch delete completed: {len(deleted_models)} success, {len(failed_models)} failed")
        return result

    def update_model_info(self, model_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新AI模型信息

        Args:
            model_id: 模型ID
            update_data: 更新数据字典

        Returns:
            更新结果字典

        Raises:
            ValueError: 当模型不存在或数据验证失败时
        """
        self.log_info(f"Updating model {model_id} with data: {update_data}")

        # 获取模型实例
        try:
            model = AIModel.objects.get(id=model_id)
        except AIModel.DoesNotExist:
            raise ValueError(f'模型不存在 (ID: {model_id})')

        # 验证请求数据
        from ..serializers import AIModelUpdateSerializer
        serializer = AIModelUpdateSerializer(data=update_data)
        if not serializer.is_valid():
            raise ValueError(f'模型信息验证失败: {serializer.errors}')

        validated_data = serializer.validated_data

        # 验证模型名称唯一性（排除当前模型）
        model_name = validated_data['name']
        model_type = validated_data['model_type']

        existing_model = AIModel.objects.filter(
            name=model_name,
            model_type=model_type
        ).exclude(id=model_id).first()

        if existing_model:
            raise ValueError(f'模型名称 "{model_name}" 在类型 "{model_type}" 中已存在')

        # 处理OCR角色验证
        ocr_role = validated_data.get('ocr_role')
        if model_type == 'ocr' and not ocr_role:
            raise ValueError('OCR模型必须指定角色 (detection 或 recognition)')
        elif model_type != 'ocr':
            ocr_role = None  # 非OCR模型清空角色字段

        # 处理系统模型标识
        is_system_model = validated_data.get('is_system_model', False)

        # 清理相关缓存
        cache_key = self._get_cache_key(model)
        self._remove_from_cache(cache_key)

        # 更新模型信息
        model.name = model_name
        model.model_type = model_type
        model.version = validated_data['version']
        model.description = validated_data.get('description')
        model.ocr_role = ocr_role
        model.is_system_model = is_system_model
        model.ocr_collection_name = validated_data.get('ocr_collection_name')

        model.save()

        self.log_info(f"Successfully updated model: {model.name} (ID: {model.id})")

        # 返回更新后的模型信息
        from ..serializers import AIModelSerializer
        response_serializer = AIModelSerializer(model)
        return {
            'success': True,
            'message': '模型信息更新成功',
            'model': response_serializer.data
        }

    def upload_model(self, model_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        上传AI模型

        Args:
            model_data: 模型数据字典，包含所有必需字段

        Returns:
            上传结果字典

        Raises:
            ValueError: 当参数验证失败或模型已存在时
        """
        from ..serializers import AIModelUploadSerializer, AIModelSerializer

        self.log_info(f"Uploading AI model: {model_data.get('name')}")

        # 验证数据
        upload_serializer = AIModelUploadSerializer(data=model_data)
        if not upload_serializer.is_valid():
            raise ValueError(f'模型上传参数验证失败: {upload_serializer.errors}')

        validated_data = upload_serializer.validated_data

        # 检查重复模型
        model_name = validated_data['name']
        model_type = validated_data['model_type']
        ocr_role = validated_data.get('ocr_role')
        is_system_model = validated_data.get('is_system_model', False)

        # 构建查询条件
        filter_kwargs = {
            'name': model_name,
            'model_type': model_type,
            'is_system_model': is_system_model
        }

        if model_type == 'ocr':
            filter_kwargs['ocr_role'] = ocr_role

        existing_model = AIModel.objects.filter(**filter_kwargs).first()

        if existing_model:
            # 构建详细的错误信息
            if model_type == 'ocr':
                error_msg = f"已存在名为 '{model_name}' 的{model_type}模型（角色: {ocr_role}）"
            else:
                error_msg = f"已存在名为 '{model_name}' 的{model_type}模型"

            if validated_data.get('version'):
                error_msg += "。建议使用不同的版本号或模型名称"
            else:
                error_msg += "。建议添加版本号或使用不同的模型名称"

            self.log_warning(f"Duplicate model upload attempt: {error_msg}")
            raise ValueError(error_msg)

        try:
            # 创建AI模型实例
            ai_model_instance = AIModel(
                name=model_name,
                model_type=model_type,
                version=validated_data['version'],
                description=validated_data.get('description'),
                ocr_role=ocr_role,
                model_file=validated_data['model_file'],
                is_system_model=is_system_model,
                ocr_collection_name=validated_data.get('ocr_collection_name')
            )
            ai_model_instance.save()  # 触发文件保存和路径生成

            # 构建响应数据
            response_serializer = AIModelSerializer(ai_model_instance)
            result = {
                'success': True,
                'message': '模型上传成功',
                'model': response_serializer.data
            }

            self.log_info(f"Successfully uploaded AI model: {model_name} (ID: {ai_model_instance.id})")
            return result

        except Exception as e:
            self.log_error(f"Failed to upload AI model: {str(e)}")
            raise Exception(f'处理模型上传失败: {str(e)}')

    def delete_model(self, model_id: int, remove_files: bool = True) -> bool:
        """
        删除模型

        Args:
            model_id: 模型ID
            remove_files: 是否删除模型文件

        Returns:
            是否删除成功
        """
        try:
            model = AIModel.objects.get(id=model_id)
            model_name = model.name

            # 备份模型信息
            model_info = {
                'name': model.name,
                'model_type': model.model_type,
                'version': model.version,
                'is_system_model': model.is_system_model
            }

            # 删除模型文件（如果需要且不是系统模型）
            if remove_files and not model.is_system_model:
                try:
                    model_path = self.get_model_path(model)
                    if os.path.exists(model_path):
                        os.remove(model_path)
                        self.log_info(f"Removed model file: {model_path}")
                except Exception as e:
                    self.log_warning(f"Failed to remove model file: {str(e)}")

            # 清理缓存
            cache_key = self._get_cache_key(model)
            self._remove_from_cache(cache_key)

            # 删除数据库记录
            model.delete()

            self.log_info(f"Successfully deleted model: {model_name} (ID: {model_id})")
            return True

        except AIModel.DoesNotExist:
            self.log_warning(f"Model ID {model_id} does not exist")
            return False
        except Exception as e:
            self.log_error(f"Error deleting model: {str(e)}")
            return False

    def backup_model(self, model_id: int, backup_dir: Optional[str] = None) -> str:
        """
        备份模型文件

        Args:
            model_id: 模型ID
            backup_dir: 备份目录（可选）

        Returns:
            备份文件路径

        Raises:
            ValueError: 模型不存在
            Exception: 备份失败
        """
        try:
            model = AIModel.objects.get(id=model_id)
            model_path = self.get_model_path(model)

            # 确定备份目录
            if backup_dir is None:
                backup_dir = os.path.join(
                    self.get_setting('BASE_DIR', '/tmp'),
                    'model_backups'
                )

            os.makedirs(backup_dir, exist_ok=True)

            # 生成备份文件名
            timestamp = int(time.time())
            backup_filename = f"{model.name}_{model.model_type}_{timestamp}{os.path.splitext(model_path)[1]}"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 复制文件
            import shutil
            if os.path.isdir(model_path):
                shutil.copytree(model_path, backup_path)
            else:
                shutil.copy2(model_path, backup_path)

            self.log_info(f"Successfully backed up model to: {backup_path}")
            return backup_path

        except AIModel.DoesNotExist:
            raise ValueError(f"模型ID {model_id} 不存在")
        except Exception as e:
            self.log_error(f"Error backing up model: {str(e)}")
            raise

    # ==================== 辅助方法 ====================

    def get_supported_formats(self) -> Dict[str, List[str]]:
        """
        获取支持的模型格式

        Returns:
            支持的格式字典
        """
        return self.SUPPORTED_MODEL_FORMATS.copy()

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            缓存统计字典
        """
        return {
            'cached_models': len(self._model_cache),
            'max_cache_size': self.MAX_CACHE_SIZE,
            'cache_timeout': self.CACHE_TIMEOUT,
            'cache_keys': list(self._model_cache.keys())
        }

    def generate_model_hash(self, model_path: str) -> str:
        """
        生成模型文件哈希值

        Args:
            model_path: 模型文件路径

        Returns:
            MD5哈希值
        """
        try:
            hash_md5 = hashlib.md5()
            with open(model_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.log_error(f"Error generating model hash: {str(e)}")
            return ""
