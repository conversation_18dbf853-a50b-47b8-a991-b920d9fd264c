"""
管理员服务模块

提供管理员相关功能的业务逻辑，包括：
- 示例图片管理（查询、删除）
- AI模型管理（删除、更新）
- 数据验证和错误处理

完整的的 AdminService 架构：

class AdminService(BaseService):
    # ==================== 示例图片管理 ====================
    def get_example_images_list()           # 获取图片列表
    def get_dashboard_images_data()         # 获取仪表盘数据
    def get_example_images_with_folders()   # 获取带文件夹的图片列表
    def delete_example_images()             # 批量删除图片
    
    # ==================== 示例图片上传和编辑 ====================
    def upload_example_image()              # 上传示例图片
    def update_image_order()                # 更新图片顺序和位置
    def update_example_image_description()  # 更新图片描述
    def _move_image_file()                  # 物理文件移动（私有方法）
    
    # ==================== 文件夹管理 ====================
    def create_example_folder()             # 创建文件夹
    def rename_example_folder()             # 重命名文件夹
    def delete_example_folder()             # 删除空文件夹
    
    # ==================== AI模型管理 ====================
    def delete_models()                     # 批量删除模型
    def update_model()                      # 更新模型信息
    def upload_ai_model()                   # 上传AI模型
    
    # ==================== 管理员认证 ====================
    def admin_login()                       # 管理员登录验证
    def admin_logout()                      # 管理员登出
    def admin_check_status()                # 检查登录状态

"""

import os
import logging
from typing import List, Dict, Any, Tuple, Optional
from django.db.models import QuerySet, Max
from django.db import transaction
from django.conf import settings

from .base import BaseService
from .file_service import FileService
from .model_service import ModelService
from ..models import AIModel, ExampleImage


class AdminService(BaseService):
    """
    管理员服务类
    
    提供管理员相关功能的业务逻辑处理，包括示例图片和AI模型的管理。
    """
    
    def __init__(self):
        """初始化管理员服务"""
        super().__init__()
        self.file_service = FileService()
        self.model_service = ModelService()
    
    # ==================== 示例图片管理 ====================
    
    def get_example_images_list(self, category: str, folder: str = '') -> Dict[str, Any]:
        """
        获取示例图片列表
        
        Args:
            category: 图片分类 ('barcode', 'ocr', 'ai_restored')
            folder: 文件夹路径（可选）
            
        Returns:
            包含图片列表和文件夹列表的字典
            
        Raises:
            ValueError: 当分类无效时
        """
        # 验证分类
        allowed_categories = {'barcode', 'ocr', 'ai_restored'}
        if category not in allowed_categories:
            raise ValueError(f'无效的分类: {category}。允许的分类: {", ".join(allowed_categories)}')
        
        self.log_info(f"Getting example images list: category='{category}', folder='{folder}'")
        
        # 清理文件夹路径
        folder = folder.strip('/')
        
        # 基础查询集
        queryset = ExampleImage.objects.filter(category=category)
        
        # 过滤图片
        if not folder:
            # 根目录下的图片（路径中不含'/'）
            image_queryset = queryset.filter(path__regex=r'^[^/]+$')
        else:
            # 指定文件夹下的图片
            prefix = f"{folder}/"
            image_queryset = queryset.filter(
                path__startswith=prefix, 
                path__regex=rf'^{prefix}[^/]+$'
            )
        
        # 获取图片数据
        images = image_queryset.order_by('display_order', 'name')
        images_data = []
        
        for image in images:
            images_data.append({
                'id': image.id,
                'name': image.name,
                'url': image.url,
                'description': image.description or '',
                'file_size': image.file_size,
                'dimensions': [image.width, image.height],
                'display_order': image.display_order
            })
        
        # 获取子文件夹列表
        subfolders = self._get_subfolders(queryset, folder)
        
        result = {
            'images': images_data,
            'folders': subfolders
        }
        
        self.log_info(f"Found {len(images_data)} images and {len(subfolders)} folders")
        return result
    
    def _get_subfolders(self, queryset: QuerySet, current_folder: str) -> List[str]:
        """
        获取当前文件夹下的子文件夹列表
        
        Args:
            queryset: 图片查询集
            current_folder: 当前文件夹路径
            
        Returns:
            子文件夹名称列表
        """
        if not current_folder:
            # 根目录：查找包含'/'的路径的第一级目录
            paths_with_folders = queryset.filter(path__contains='/').values_list('path', flat=True)
            subfolders = set()
            for path in paths_with_folders:
                first_folder = path.split('/')[0]
                subfolders.add(first_folder)
        else:
            # 子目录：查找以"current_folder/"开头且包含更多'/'的路径
            prefix = f"{current_folder}/"
            deeper_paths = queryset.filter(
                path__startswith=prefix,
                path__regex=rf'^{prefix}[^/]+/.+'
            ).values_list('path', flat=True)
            
            subfolders = set()
            for path in deeper_paths:
                # 移除前缀，获取下一级文件夹名
                relative_path = path[len(prefix):]
                next_folder = relative_path.split('/')[0]
                subfolders.add(next_folder)
        
        return sorted(list(subfolders))
    
    def delete_example_images(self, image_ids: List[int]) -> Dict[str, Any]:
        """
        批量删除示例图片
        
        Args:
            image_ids: 要删除的图片ID列表
            
        Returns:
            删除结果字典，包含成功和失败的详情
        """
        if not isinstance(image_ids, list) or not image_ids:
            raise ValueError('参数错误，需要提供一个包含图片ID的数组')
        
        self.log_info(f"Deleting example images: {image_ids}")
        
        # 查找所有待删除的图片
        images_to_delete = ExampleImage.objects.filter(id__in=image_ids)
        valid_ids_found = {img.id for img in images_to_delete}
        
        deleted_count = 0
        failed_ids = []
        
        # 遍历并删除图片
        for image in images_to_delete:
            image_id = image.id
            try:
                # 调用模型的delete方法，这将触发物理文件删除
                image.delete()
                deleted_count += 1
                self.log_info(f"Successfully deleted ExampleImage with ID: {image_id}")
            except Exception as e:
                self.log_error(f"Failed to delete ExampleImage with ID {image_id}: {str(e)}")
                failed_ids.append(image_id)
        
        # 检查无效的ID
        invalid_ids = [img_id for img_id in image_ids if img_id not in valid_ids_found]
        
        # 构建响应
        success_count = deleted_count
        failed_count = len(failed_ids) + len(invalid_ids)
        
        if success_count > 0 and failed_count == 0:
            message = f'成功删除 {success_count} 张图片'
        elif success_count > 0 and failed_count > 0:
            message = f'成功删除 {success_count} 张图片，失败 {failed_count} 张'
        else:
            message = f'删除失败，共 {failed_count} 张图片删除失败'
        
        result = {
            'success': True,
            'deleted_count': deleted_count,
            'failed_ids': failed_ids,
            'invalid_ids': invalid_ids,
            'message': message
        }
        
        self.log_info(f"Delete operation completed: {message}")
        return result

    # ==================== AI模型管理 ====================

    def delete_models(self, model_ids: List[int]) -> Dict[str, Any]:
        """
        批量删除AI模型（委托给ModelService）

        Args:
            model_ids: 要删除的模型ID列表

        Returns:
            删除结果字典，包含成功和失败的详情
        """
        return self.model_service.batch_delete_models(model_ids)

    def update_model(self, model_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新AI模型信息（委托给ModelService）

        Args:
            model_id: 模型ID
            update_data: 更新数据字典

        Returns:
            更新结果字典

        Raises:
            ValueError: 当模型不存在或数据验证失败时
        """
        return self.model_service.update_model_info(model_id, update_data)

    # ==================== 示例图片查询 ====================

    def get_dashboard_images_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取仪表盘用的所有示例图片数据

        Returns:
            按分类组织的图片数据字典
        """
        self.log_info("Getting all example images for dashboard statistics")

        all_images = ExampleImage.objects.all().order_by('display_order')

        response_data = {
            'barcode': [],
            'ocr': [],
            'ai_restored': []
        }

        for img in all_images:
            image_data = {
                'id': img.id,
                'name': img.name,
                'path': img.path,
                'category': img.category,
                'display_order': img.display_order,
                'description': img.description,
                'file_size': img.file_size,
                'width': img.width,
                'height': img.height,
                'url': img.url
            }
            if img.category in response_data:
                response_data[img.category].append(image_data)

        self.log_info(f"Dashboard data prepared: {len(response_data['barcode'])} barcode, "
                     f"{len(response_data['ocr'])} ocr, {len(response_data['ai_restored'])} ai_restored images")

        return response_data

    def get_example_images_with_folders(self, category: str, folder: str = '') -> Dict[str, Any]:
        """
        获取示例图片列表（包含文件夹信息）

        Args:
            category: 图片分类
            folder: 文件夹路径

        Returns:
            包含图片和文件夹的数据字典
        """
        # 验证分类
        allowed_categories = {'barcode', 'ocr', 'ai_restored'}
        if category not in allowed_categories:
            raise ValueError(f'无效的分类: {category}。允许的分类: {", ".join(allowed_categories)}')

        self.log_info(f"Getting example images with folders: category='{category}', folder='{folder}'")

        # 清理文件夹路径
        folder = folder.strip('/')

        # 基础查询集
        queryset = ExampleImage.objects.filter(category=category)

        # 过滤图片
        if not folder:
            # 根目录下的图片（路径中不含'/'）
            image_queryset = queryset.filter(path__regex=r'^[^/]+$')
        else:
            # 指定文件夹下的图片
            prefix = f"{folder}/"
            image_queryset = queryset.filter(
                path__startswith=prefix,
                path__regex=rf'^{prefix}[^/]+$'
            )

        # 序列化图片数据
        images_data = []
        for img in image_queryset:
            images_data.append({
                'id': img.id,
                'name': img.name,
                'path': img.path,
                'category': img.category,
                'display_order': img.display_order,
                'description': img.description,
                'file_size': img.file_size,
                'width': img.width,
                'height': img.height,
                'url': img.url
            })

        # 获取子文件夹（数据库和文件系统结合）
        subfolders = self._get_subfolders_with_filesystem(queryset, category, folder)

        result = {
            'images': images_data,
            'folders': subfolders
        }

        self.log_info(f"Found {len(images_data)} images and {len(subfolders)} folders")
        return result

    # ==================== 示例图片上传和编辑 ====================

    def upload_example_image(self, image_file, category: str, name: Optional[str] = None,
                           description: Optional[str] = None) -> Dict[str, Any]:
        """
        上传示例图片

        Args:
            image_file: 上传的图片文件
            category: 图片分类
            name: 自定义文件名（可选）
            description: 图片描述（可选）

        Returns:
            上传结果字典，包含图片信息

        Raises:
            ValueError: 当参数无效时
        """
        from .file_service import FileService
        from django.db.models import Max

        # 参数验证
        if not image_file or not category:
            raise ValueError('必须提供图片文件和分类')

        file_service = FileService()
        if category not in file_service.ALLOWED_EXAMPLE_CATEGORIES:
            raise ValueError('无效的图片分类')

        self.log_info(f"Uploading example image: category='{category}', name='{name}'")

        # 验证图像文件
        is_valid, error_msg = file_service.validate_image_file(image_file)
        if not is_valid:
            raise ValueError(error_msg)

        # 获取目标目录
        example_images_root = self.get_setting('EXAMPLE_IMAGES_ROOT')
        if not example_images_root:
            base_dir = self.get_setting('BASE_DIR')
            example_images_root = os.path.join(base_dir, 'models', 'example_images')

        category_dir = os.path.join(example_images_root, category)

        # 生成文件名
        target_filename = name if name else os.path.splitext(image_file.name)[0]
        target_filename += os.path.splitext(image_file.name)[1]  # 保持原始扩展名

        try:
            # 保存文件
            file_path = file_service.save_permanent_file(image_file, category_dir, target_filename)
            filename = os.path.basename(file_path)

            # 获取文件信息
            file_info = file_service.get_file_info(file_path)
            file_size = file_info.get('size', 0)
            width = file_info.get('width', 0)
            height = file_info.get('height', 0)

            # 计算display_order（新图片排在最后）
            max_order_result = ExampleImage.objects.filter(
                category=category,
                path__regex=r'^[^/]+$'  # 根目录文件
            ).aggregate(max_order=Max('display_order'))

            max_order = max_order_result.get('max_order')
            new_order = max_order + 1 if max_order is not None else 0

            # 设置默认描述
            default_descriptions = {
                'barcode': '条码检测示例图片',
                'ocr': 'OCR检测示例图片',
                'ai_restored': 'AI修复示例图片'
            }
            final_description = description or default_descriptions.get(category, f"{category} 示例图片")

            # 创建数据库记录
            new_image = ExampleImage.objects.create(
                name=filename,
                path=filename,  # 根目录文件，path与name相同
                category=category,
                description=final_description,
                display_order=new_order,
                file_size=file_size,
                width=width,
                height=height
            )

            # 构建响应数据
            result = {
                'id': new_image.id,
                'name': new_image.name,
                'description': new_image.description,
                'url': new_image.url,
                'category': new_image.category,
                'file_size': new_image.file_size,
                'dimensions': [new_image.width, new_image.height]
            }

            self.log_info(f"Successfully uploaded example image: {category}/{filename} with ID {new_image.id}")
            return result

        except Exception as e:
            self.log_error(f"Failed to upload example image: {str(e)}")
            raise Exception(f'上传图片失败: {str(e)}')

    def update_image_order(self, category: str, ordered_image_ids: List[int],
                          target_folder: str = '') -> Dict[str, Any]:
        """
        更新图片的显示顺序和所属文件夹

        Args:
            category: 图片分类
            ordered_image_ids: 按新顺序排列的图片ID列表
            target_folder: 目标文件夹路径

        Returns:
            更新结果字典

        Raises:
            ValueError: 当参数无效时
        """
        if not category or not isinstance(ordered_image_ids, list):
            raise ValueError('参数错误: 必须提供 category 和 ordered_image_ids 列表')

        target_folder = target_folder.strip('/')
        self.log_info(f"Updating image order: category='{category}', target_folder='{target_folder}', count={len(ordered_image_ids)}")

        try:
            with transaction.atomic():
                images_to_update = ExampleImage.objects.filter(id__in=ordered_image_ids).select_for_update()
                id_to_instance_map = {str(image.id): image for image in images_to_update}

                # 检查ID匹配情况
                if len(ordered_image_ids) != len(images_to_update):
                    self.log_warning(f"ID mismatch: requested {len(ordered_image_ids)}, found {len(images_to_update)}")

                updated_count = 0
                for index, image_id_str in enumerate(ordered_image_ids):
                    image_id = int(image_id_str)
                    image = id_to_instance_map.get(str(image_id))

                    if not image:
                        self.log_warning(f"Image with ID {image_id} not found, skipping")
                        continue

                    # 更新显示顺序
                    image.display_order = index

                    # 更新路径（移动到新文件夹）
                    new_path = f"{target_folder}/{image.name}" if target_folder else image.name
                    if image.path != new_path:
                        # 检查目标路径冲突
                        if ExampleImage.objects.filter(category=image.category, path=new_path).exclude(id=image.id).exists():
                            raise ValueError(f"目标路径 '{new_path}' 已被另一张图片占用")

                        # 物理移动文件
                        self._move_image_file(image, new_path)
                        image.path = new_path

                    image.save()
                    updated_count += 1

                result = {
                    'success': True,
                    'message': '图片顺序和位置更新成功',
                    'updated_count': updated_count
                }

                self.log_info(f"Successfully updated {updated_count} images")
                return result

        except Exception as e:
            self.log_error(f"Failed to update image order: {str(e)}")
            raise Exception(f'更新失败: {str(e)}')

    def update_example_image_description(self, image_id: int, description: str) -> Dict[str, Any]:
        """
        更新示例图片的描述信息

        Args:
            image_id: 图片ID
            description: 新的描述信息

        Returns:
            更新结果字典

        Raises:
            ValueError: 当图片不存在时
        """
        self.log_info(f"Updating image description: image_id={image_id}")

        try:
            image_instance = ExampleImage.objects.get(id=image_id)
        except ExampleImage.DoesNotExist:
            raise ValueError(f'ID为 {image_id} 的图片未找到')

        try:
            image_instance.description = description
            image_instance.save(update_fields=['description'])

            result = {
                'success': True,
                'message': '图片描述更新成功',
                'image': {
                    'id': image_instance.id,
                    'description': image_instance.description
                }
            }

            self.log_info(f"Successfully updated description for image ID {image_id}")
            return result

        except Exception as e:
            self.log_error(f"Failed to update image description: {str(e)}")
            raise Exception(f'更新图片描述失败: {str(e)}')

    def _move_image_file(self, image: ExampleImage, new_path: str) -> None:
        """
        物理移动图片文件

        Args:
            image: 图片实例
            new_path: 新的路径
        """
        example_images_root = self.get_setting('EXAMPLE_IMAGES_ROOT')
        if not example_images_root:
            base_dir = self.get_setting('BASE_DIR')
            example_images_root = os.path.join(base_dir, 'models', 'example_images')

        old_file_path = os.path.join(example_images_root, image.category, image.path)
        new_file_path = os.path.join(example_images_root, image.category, new_path)

        if os.path.exists(old_file_path):
            # 确保目标目录存在
            os.makedirs(os.path.dirname(new_file_path), exist_ok=True)
            os.rename(old_file_path, new_file_path)
            self.log_info(f"Moved file from {old_file_path} to {new_file_path}")
        else:
            self.log_warning(f"Source file not found for move: {old_file_path}")

    # ==================== 文件夹管理 ====================

    def create_example_folder(self, category: str, folder_name: str) -> Dict[str, Any]:
        """
        创建示例图片文件夹

        Args:
            category: 图片分类
            folder_name: 文件夹名称

        Returns:
            创建结果字典

        Raises:
            ValueError: 当参数无效或文件夹已存在时
        """
        if not category or not folder_name:
            raise ValueError('必须提供 category 和 folder_name')

        folder_name = folder_name.strip()

        # 验证分类
        allowed_categories = {'barcode', 'ocr', 'ai_restored'}
        if category not in allowed_categories:
            raise ValueError(f'无效的分类: {category}')

        # 安全性检查：防止路径遍历
        if '/' in folder_name or '\\' in folder_name or '..' in folder_name:
            raise ValueError('文件夹名称不能包含斜杠或点')

        self.log_info(f"Creating folder: category='{category}', folder='{folder_name}'")

        # 获取目录路径
        example_images_root = self.get_setting('EXAMPLE_IMAGES_ROOT')
        if not example_images_root:
            base_dir = self.get_setting('BASE_DIR')
            example_images_root = os.path.join(base_dir, 'models', 'example_images')

        category_dir = os.path.join(example_images_root, category)
        folder_path = os.path.join(category_dir, folder_name)

        if os.path.exists(folder_path):
            raise ValueError(f"文件夹 '{folder_name}' 已存在于分类 '{category}' 中")

        try:
            os.makedirs(folder_path, exist_ok=True)

            result = {
                'success': True,
                'message': f"文件夹 '{folder_name}' 创建成功",
                'folder': folder_name
            }

            self.log_info(f"Successfully created folder: {folder_path}")
            return result

        except Exception as e:
            self.log_error(f"Failed to create folder: {str(e)}")
            raise Exception(f'创建文件夹失败: {str(e)}')

    def rename_example_folder(self, category: str, old_name: str, new_name: str) -> Dict[str, Any]:
        """
        重命名示例图片文件夹

        Args:
            category: 图片分类
            old_name: 旧文件夹名称
            new_name: 新文件夹名称

        Returns:
            重命名结果字典

        Raises:
            ValueError: 当参数无效时
        """
        if not all([category, old_name, new_name]):
            raise ValueError('必须提供 category, old_name, 和 new_name')

        old_name = old_name.strip()
        new_name = new_name.strip()

        if old_name == new_name:
            raise ValueError('新旧文件夹名称不能相同')

        # 安全性检查
        for name in [old_name, new_name]:
            if '/' in name or '\\' in name or '..' in name:
                raise ValueError('文件夹名称不能包含无效字符')

        self.log_info(f"Renaming folder: category='{category}', '{old_name}' -> '{new_name}'")

        # 获取路径
        example_images_root = self.get_setting('EXAMPLE_IMAGES_ROOT')
        if not example_images_root:
            base_dir = self.get_setting('BASE_DIR')
            example_images_root = os.path.join(base_dir, 'models', 'example_images')

        category_dir = os.path.join(example_images_root, category)
        old_folder_path = os.path.join(category_dir, old_name)
        new_folder_path = os.path.join(category_dir, new_name)

        if not os.path.isdir(old_folder_path):
            raise ValueError(f"文件夹 '{old_name}' 不存在")

        if os.path.exists(new_folder_path):
            raise ValueError(f"目标文件夹名称 '{new_name}' 已存在")

        try:
            with transaction.atomic():
                # 找到所有属于该文件夹的图片
                images_to_update = ExampleImage.objects.filter(
                    category=category,
                    path__startswith=f"{old_name}/"
                )

                # 重命名物理目录
                os.rename(old_folder_path, new_folder_path)
                self.log_info(f"Renamed folder from {old_folder_path} to {new_folder_path}")

                # 批量更新数据库中的路径
                update_count = 0
                for image in images_to_update:
                    original_path = image.path
                    image.path = original_path.replace(f"{old_name}/", f"{new_name}/", 1)
                    image.save(update_fields=['path'])
                    update_count += 1

                result = {
                    'success': True,
                    'message': f"文件夹 '{old_name}' 已成功重命名为 '{new_name}'",
                    'updated_image_count': update_count
                }

                self.log_info(f"Updated {update_count} image paths in database")
                return result

        except Exception as e:
            # 尝试回滚物理文件系统操作
            if os.path.exists(new_folder_path) and not os.path.exists(old_folder_path):
                try:
                    os.rename(new_folder_path, old_folder_path)
                    self.log_warning(f"Rolled back folder rename due to error")
                except:
                    pass

            self.log_error(f"Failed to rename folder: {str(e)}")
            raise Exception(f'重命名文件夹失败: {str(e)}')

    def delete_example_folder(self, category: str, folder_name: str) -> Dict[str, Any]:
        """
        删除空的示例图片文件夹

        Args:
            category: 图片分类
            folder_name: 文件夹名称

        Returns:
            删除结果字典

        Raises:
            ValueError: 当参数无效、文件夹不存在或不为空时
        """
        if not category or not folder_name:
            raise ValueError('必须提供 category 和 folder_name')

        folder_name = folder_name.strip()

        self.log_info(f"Deleting folder: category='{category}', folder='{folder_name}'")

        # 检查文件夹内是否还有图片
        if ExampleImage.objects.filter(category=category, path__startswith=f"{folder_name}/").exists():
            raise ValueError(f"文件夹 '{folder_name}' 不为空，无法删除。请先移动或删除其中的所有图片")

        # 获取路径
        example_images_root = self.get_setting('EXAMPLE_IMAGES_ROOT')
        if not example_images_root:
            base_dir = self.get_setting('BASE_DIR')
            example_images_root = os.path.join(base_dir, 'models', 'example_images')

        folder_path = os.path.join(example_images_root, category, folder_name)

        if not os.path.isdir(folder_path):
            raise ValueError(f"文件夹 '{folder_name}' 不存在")

        try:
            # 删除空目录
            os.rmdir(folder_path)

            result = {
                'success': True,
                'message': f"文件夹 '{folder_name}' 已成功删除"
            }

            self.log_info(f"Successfully deleted empty folder: {folder_path}")
            return result

        except OSError as e:
            # 捕捉到试图删除非空目录的异常
            if "not empty" in str(e).lower():
                raise ValueError(f"文件夹 '{folder_name}' 不为空，无法删除")
            else:
                self.log_error(f"OSError deleting folder: {str(e)}")
                raise Exception(f'删除文件夹时发生操作系统错误: {str(e)}')
        except Exception as e:
            self.log_error(f"Failed to delete folder: {str(e)}")
            raise Exception(f'删除文件夹失败: {str(e)}')

    # ==================== AI模型上传 ====================

    def upload_ai_model(self, model_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        上传AI模型（委托给ModelService）

        Args:
            model_data: 模型数据字典，包含所有必需字段

        Returns:
            上传结果字典

        Raises:
            ValueError: 当参数验证失败或模型已存在时
        """
        return self.model_service.upload_model(model_data)

    # ==================== 管理员认证 ====================

    def admin_login(self, password: str, session) -> Dict[str, Any]:
        """
        管理员登录验证

        Args:
            password: 登录密码
            session: Django会话对象

        Returns:
            登录结果字典

        Raises:
            ValueError: 当密码为空或错误时
        """
        if not password:
            raise ValueError('请输入密码')

        self.log_info("Admin login attempt")

        # 简单的密码验证 - 固定密码 "mindeo"
        if password == 'mindeo':
            # 在会话中设置管理员状态
            from django.utils import timezone
            session['is_admin'] = True
            session['admin_login_time'] = timezone.now().isoformat()

            result = {
                'success': True,
                'message': '管理员登录成功'
            }

            self.log_info("Admin login successful")
            return result
        else:
            self.log_warning("Admin login failed with incorrect password")
            raise ValueError('密码错误')

    def admin_logout(self, session) -> Dict[str, Any]:
        """
        管理员登出

        Args:
            session: Django会话对象

        Returns:
            登出结果字典
        """
        self.log_info("Admin logout")

        # 清除会话中的管理员状态
        session.pop('is_admin', None)
        session.pop('admin_login_time', None)

        result = {
            'success': True,
            'message': '管理员登出成功'
        }

        self.log_info("Admin logout successful")
        return result

    def admin_check_status(self, session) -> Dict[str, Any]:
        """
        检查管理员登录状态

        Args:
            session: Django会话对象

        Returns:
            状态检查结果字典
        """
        is_admin = session.get('is_admin', False)
        login_time = session.get('admin_login_time')

        result = {
            'success': True,
            'is_admin': is_admin,
            'login_time': login_time
        }

        self.log_info(f"Admin status check: is_admin={is_admin}")
        return result

    def _get_subfolders_with_filesystem(self, queryset: QuerySet, category: str, current_folder: str) -> List[str]:
        """
        获取子文件夹列表（结合数据库和文件系统）

        Args:
            queryset: 图片查询集
            category: 图片分类
            current_folder: 当前文件夹路径

        Returns:
            子文件夹名称列表
        """
        # 1. 从数据库中获取包含图片的文件夹
        db_subfolders = set()

        if not current_folder:
            # 根目录：查找包含'/'的路径的第一级目录
            paths_with_subdirs = queryset.filter(path__contains='/')
            db_subfolders.update(p.path.split('/')[0] for p in paths_with_subdirs)
        else:
            # 子目录：查找指定前缀下的文件夹
            prefix = f"{current_folder}/"
            paths_in_folder = queryset.filter(path__startswith=prefix)
            for p in paths_in_folder:
                path_without_prefix = p.path[len(prefix):]
                if '/' in path_without_prefix:
                    db_subfolders.add(path_without_prefix.split('/')[0])

        # 2. 从文件系统扫描所有物理文件夹（包括空文件夹）
        fs_subfolders = set()
        try:
            example_images_root = self.get_setting('EXAMPLE_IMAGES_ROOT')
            if not example_images_root:
                example_images_root = os.path.join(self.get_setting('BASE_DIR'), 'models', 'example_images')

            current_scan_dir = os.path.join(example_images_root, category, current_folder) if current_folder else os.path.join(example_images_root, category)

            if os.path.isdir(current_scan_dir):
                for item in os.listdir(current_scan_dir):
                    item_path = os.path.join(current_scan_dir, item)
                    if os.path.isdir(item_path):
                        fs_subfolders.add(item)
        except Exception as e:
            self.log_warning(f"Could not scan filesystem for folders in '{current_scan_dir}': {e}")

        # 3. 合并并排序
        return sorted(list(db_subfolders.union(fs_subfolders)))
