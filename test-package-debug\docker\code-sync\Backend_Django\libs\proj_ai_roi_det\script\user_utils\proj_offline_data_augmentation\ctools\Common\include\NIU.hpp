#ifndef _NIU_H
#define _NIU_H

//-----------------------------------------------------------------------------
//  Includes

#include <string>
#include "AIEngineCommon.h"
#include "Preprocess.hpp"
#include "Postprocess.hpp"
#include "ModelLoader.hpp"

//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

class BaseNIU {
public:
    virtual ~BaseNIU() {}
    BaseNIU() {}

    virtual BasePreprocess*Preprocess(void) = 0; // 前处理模块
    virtual BasePostprocess *Postprocess(void) = 0; // 后处理模块
    virtual int Inference(void) = 0; // 推理接口

    virtual const std::vector<void *> *Get_Input_Tensor_Index_List(void) = 0; // 获取输入张量索引列表
    virtual const std::vector<void *> *Get_Output_Tensor_Index_List(void) = 0; // 获取输出张量索引列表

    /**
    * @brief    
    *           执行目标检测任务
    *           
    * @param    model_tag:         模型标签
    * @param    image_info:        图片数据信息
    * @param    det_results:       目标检测返回结果保存地址
    *           
    * @retval   错误码
    *           
    * @date     2024-10-30 Created by HuangJP
    */
    int Task_Detection(struct ImageInfo *image_info, std::vector<DetectionBBoxInfo> *det_results)
    {
        // 判断传入参数是否合法
        if ((image_info == nullptr)
            || (det_results == nullptr))
        {
            LOGE("A nullptr found in user input");
            return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
        }

        // 获取输入输出张量列表
        const PreprocessedData *prep_data = nullptr;

        // 执行前处理
        int rev = AIENGINE_NO_ERROR;
        rev = Fill_Input_Tensor_With_Image(image_info, 0, prep_data);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the preprocess.");
            return rev;
        }

        // 执行推理
        rev = Inference();
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the inference.");
            return rev;
        }

        // 执行后处理
        rev = Postprocess()->template Output_Detection<float>(prep_data, *Get_Output_Tensor_Index_List(), det_results);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 后处理失败
            LOGE("An error was encountered during the postprocess.");
            return rev;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           执行语义分割任务
     *           
     * @param    model_tag:         模型标签
     * @param    image_info:        图片数据信息
     * @param    seg_mask:          指向掩码图像存储地址的指针
     * @param    contours:          轮廓坐标
     *           
     * @retval   错误码
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    int Task_Semantic_Segmentation(struct ImageInfo *image_info, struct ImageInfo *seg_mask, std::vector<std::vector<Point>> *contours, std::vector<int> *classes)
    {
        // 判断传入参数是否合法
        if (image_info == nullptr)
        {
            LOGE("A nullptr found in user input");
            return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
        }

        // 获取输入输出张量列表
        const PreprocessedData *prep_data = nullptr;

        // 执行前处理
        int rev = AIENGINE_NO_ERROR;
        rev = Fill_Input_Tensor_With_Image(image_info, 0, prep_data);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the preprocess.");
            return rev;
        }

        // 执行推理
        rev = Inference();
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the inference.");
            return rev;
        }

        // 执行后处理
        rev = Postprocess()->template Output_Semantic_Segmentation<float>(prep_data, *Get_Output_Tensor_Index_List(), seg_mask, contours, classes);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 后处理失败
            LOGE("An error was encountered during the postprocess.");
            return rev;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           执行字符检测任务
     *           
     * @param    model_tag:         模型标签
     * @param    image_info:        图片数据信息
     * @param    ocr_det_results:   字符检测返回结果保存地址
     *           
     * @retval   错误码
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    int Task_OCR_Detection(struct ImageInfo *image_info, std::vector<QuadPoints> *ocr_det_results)
    {
        // 判断传入参数是否合法
        if ((image_info == nullptr)
            || (ocr_det_results == nullptr))
        {
            LOGE("A nullptr found in user input");
            return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
        }

        // 获取输入输出张量列表
        const PreprocessedData *prep_data = nullptr;

        // 执行前处理
        int rev = AIENGINE_NO_ERROR;
        rev = Fill_Input_Tensor_With_Image(image_info, 0, prep_data);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the preprocess.");
            return rev;
        }

        // 执行推理
        rev = Inference();
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the inference.");
            return rev;
        }

        // 执行后处理
        rev = Postprocess()->template Output_OCR_Detection<float>(prep_data, *Get_Output_Tensor_Index_List(), ocr_det_results);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 后处理失败
            LOGE("An error was encountered during the postprocess.");
            return rev;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           执行字符识别任务
     *           
     * @param    model_tag:         模型标签
     * @param    image_info:        图片数据信息
     * @param    ocr_rec_results:   字符识别返回结果保存地址
     *           
     * @retval   错误码
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    int Task_OCR_Recognize(struct ImageInfo *image_info, std::vector<OCRCharacter> *ocr_rec_results)
    {
        // 判断传入参数是否合法
        if ((image_info == nullptr)
            || (ocr_rec_results == nullptr))
        {
            LOGE("A nullptr found in user input");
            return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
        }

        // 获取输入输出张量列表
        const PreprocessedData *prep_data = nullptr;

        // 执行前处理
        int rev = AIENGINE_NO_ERROR;
        rev = Fill_Input_Tensor_With_Image(image_info, 0, prep_data);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the preprocess.");
            return rev;
        }

        // 执行推理
        rev = Inference();
        if (rev != AIENGINE_NO_ERROR)
        {
            // 推理失败
            LOGE("An error was encountered during the inference.");
            return rev;
        }

        // 执行后处理
        rev = Postprocess()->template Output_OCR_Recognize<float>(prep_data, *Get_Output_Tensor_Index_List(), ocr_rec_results);
        if (rev != AIENGINE_NO_ERROR)
        {
            // 后处理失败
            LOGE("An error was encountered during the postprocess.");
            return rev;
        }

        return AIENGINE_NO_ERROR;
    }

protected:
    const std::string _model_tag; // 模型标签

    /**
     * @brief    
     *           输入图片
     *           
     * @param    image_info:    图片信息
     * @param    index:         填充张量索引
     * @param    prep_data:     指向前处理数据存储地址的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    inline int Fill_Input_Tensor_With_Image(struct ImageInfo *image_info, int index, const PreprocessedData *&prep_data)
    {
        // 获取输入输出张量列表
        const std::vector<void *> *input = Get_Input_Tensor_Index_List();

        // 判断输入张量数量是否符合预期
        if (input->size() < (size_t)(index + 1))
        {
            LOGE("The count of input/output tensor should be greater than 1.");
            return AIENGINE_INTERFACE_ERROR;
        }

        // 执行前处理
        int rev = AIENGINE_NO_ERROR;
        rev = Preprocess()->Input_Image((*input)[index], image_info);

        // 获取前处理数据
        prep_data = Preprocess()->Request_Preprocessed_Data((*input)[index]);

        return rev;
    }

    /**
     * @brief    
     *           BaseNIU构造函数
     *           
     * @param    model_tag:     模型标签
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    BaseNIU(std::string model_tag):
        _model_tag(model_tag)
    {

    }
};

#endif
//-----------------------------------------------------------------------------
//  End of file