# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLO11 object detection model with P4-P5 outputs (P3 head removed).
# For usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 1   # number of classes
ch: 1   # input channels
scales: # model compound scaling constants: [depth, width, max_channels]
  n: [0.33, 0.25, 512]

# YOLO11n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]        # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]]       # 1-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]
  - [-1, 1, Conv, [256, 3, 2]]       # 3-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]
  - [-1, 1, Conv, [512, 3, 2]]       # 5-P4/16
  - [-1, 2, C3k2, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]]      # 7-P5/32
  - [-1, 2, C3k2, [1024, True]]
  - [-1, 1, SP<PERSON>F, [1024, 5]]         # 9
  - [-1, 2, C2<PERSON><PERSON>, [1024]]           # 10

# YOLO11n head
head:
  # --- P4 detect branch ---
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]  # upsample from P5 to P4 scale
  - [[-1, 6], 1, Concat, [1]]                   # concat with backbone P4 (output of index 6)
  - [-1, 2, C3k2, [512, False]]                 # 13 -> P4/16 (medium)

  # --- P5 detect branch ---
  - [-1, 1, Conv, [512, 3, 2]]                  # downsample back to P5
  - [[-1, 10], 1, Concat, [1]]                  # concat with backbone P5 (output of index 10)
  - [-1, 2, C3k2, [1024, True]]                 # 16 -> P5/32 (large)

  # Detect layers (only P4, P5)
  - [[13, 16], 1, Detect, [nc]]   # Detect head with 2 outputs: P4, P5