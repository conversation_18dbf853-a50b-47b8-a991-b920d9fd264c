const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electron', {
  // 获取应用版本
  getVersion: () => ipcRenderer.invoke('get-version'),
  
  // 获取应用路径
  getAppPath: () => ipcRenderer.invoke('get-app-path'),
  
  // 获取操作系统平台
  getPlatform: () => process.platform,
  
  // 是否是开发环境
  isDev: process.env.NODE_ENV === 'development',
  
  // 打开外部链接
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  
  // 最小化窗口
  minimize: () => ipcRenderer.send('window-minimize'),
  
  // 最大化/还原窗口
  maximize: () => ipcRenderer.send('window-maximize'),
  
  // 关闭窗口
  close: () => ipcRenderer.send('window-close')
}); 