#ifndef _AIENGINE_CORE_H
#define _AIENGINE_CORE_H

//-----------------------------------------------------------------------------
//  Includes

#include "AIEngineCommon.h"

#include <vector>
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations
const char *ai_engine_get_version(void);
const char *ai_engine_get_build_flags(void);
int ai_engine_model_init(const char *model_tag, int num_sessions);
int ai_engine_destroy_sessions(const char *model_tag);
int ai_engine_task_detection(const char *model_tag, struct ImageInfo *image_info, std::vector<DetectionBBoxInfo> *det_results);
int ai_engine_task_semantic_segmentation(const char *model_tag, struct ImageInfo *image_info, struct ImageInfo *seg_mask, std::vector<std::vector<Point>> *contours, std::vector<int> *classes);
int ai_engine_task_ocr_detection(const char *model_tag, struct ImageInfo *image_info, std::vector<QuadPoints> *ocr_det_results);
int ai_engine_task_ocr_recognize(const char *model_tag, struct ImageInfo *image_info, std::vector<OCRCharacter> *ocr_rec_results);

#endif
//-----------------------------------------------------------------------------
//  End of file