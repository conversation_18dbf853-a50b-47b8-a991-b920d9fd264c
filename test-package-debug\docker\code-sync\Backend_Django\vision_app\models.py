import os
import shutil
from django.db import models
from django.utils import timezone
from django.conf import settings

# Create your models here.

def get_model_upload_path(instance, filename):
    """
    Determines the upload path for a model file based on model category.

    For system models: files will be uploaded to SYSTEM_MODELS_ROOT/<model_type>/<filename>
    For user models: files will be uploaded to MEDIA_ROOT/<model_type>/<filename>

    Note: This function is now only used as a placeholder since we handle file storage manually.
    """
    # 这个函数现在只是一个占位符，实际的文件存储由 _handle_file_storage 方法处理
    # 返回简单的文件名，避免路径重复
    return filename

class AIModel(models.Model):
    name = models.CharField(max_length=255) # 移除了 unique=True，因为系统模型和自定义模型可能同名
    description = models.TextField(blank=True, null=True)
    # 对于自定义模型，model_file 存储上传的文件。
    # 对于系统模型，此字段可以为空，或者存储一个指示性的名称（但实际路径由 views.py 结合 SYSTEM_MODELS_ROOT 构建）。
    # 或者，我们可以规定系统模型也在此字段中存储其文件名（不含路径），然后视图据此构建完整路径。
    # 为了统一，我们让 model_file 存储文件名（带后缀）。
    model_file = models.FileField(upload_to=get_model_upload_path, max_length=255, blank=True, null=True, help_text="Path for custom models relative to MEDIA_ROOT/<model_type>/, or just filename for system models.")
    version = models.CharField(max_length=50, blank=True, null=True)
    model_type = models.CharField(max_length=50, help_text="e.g., barcode, ocr, ai_restored")
    is_system_model = models.BooleanField(default=False, help_text="Is this a system-provided model?")
    uploaded_at = models.DateTimeField(default=timezone.now, help_text="Timestamp when the model record was created or last updated.") # 改为 default=timezone.now

    # New field for OCR model role
    OCR_ROLE_CHOICES = [
        ('detection', 'Detection Model'),
        ('recognition', 'Recognition Model'),
        # ('standalone', 'Standalone OCR Model'), # Future consideration
    ]
    ocr_role = models.CharField(
        max_length=20,
        choices=OCR_ROLE_CHOICES,
        blank=True,
        null=True,
        help_text="Role of the OCR model (e.g., detection, recognition). Null if not an OCR model or role not applicable."
    )

    # 新增：OCR模型集合名称，用于将detection和recognition模型配对管理
    ocr_collection_name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Collection name for OCR model pairs. Same collection_name groups detection and recognition models together."
    )

    # 新增：特征匹配模型角色
    FEATURE_MATCHING_ROLE_CHOICES = [
        ('extractor', 'Extractor Model'), # 特征提取器 (e.g., SuperPoint)
        ('matcher', 'Matcher Model'),   # 匹配器 (e.g., SuperGlue)
    ]
    feature_matching_role = models.CharField(
        max_length=20,
        choices=FEATURE_MATCHING_ROLE_CHOICES,
        blank=True,
        null=True,
        help_text="Role of the Feature Matching model (e.g., extractor, matcher). Null if not a Feature Matching model."
    )

    class Meta:
        # Ensure同名、同类型、同系统状态、同角色的模型是唯一的
        # If version should also be part of uniqueness, add 'version'
        unique_together = [['name', 'model_type', 'is_system_model', 'ocr_role', 'feature_matching_role']]


    def __str__(self):
        return f"{self.name} ({self.model_type}) - v{self.version if self.version else 'N/A'} {'(System)' if self.is_system_model else '(Custom)'}"

    def save(self, *args, **kwargs):
        # Import timezone here if not already imported at the top
        from django.utils import timezone

        # 处理文件存储路径
        # 只有当model_file是新上传的文件时才处理文件存储
        is_new_file = False
        try:
            is_new_file = self.model_file and hasattr(self.model_file, 'file') and hasattr(self.model_file.file, 'read')
        except (ValueError, OSError):
            # 如果文件不存在或无法访问，说明这不是新上传的文件
            is_new_file = False

        if is_new_file:
            # 这是一个新上传的文件，需要手动处理存储
            self._handle_file_storage()
            # 阻止Django的FileField自动保存文件
            # 通过设置一个临时的文件名来避免重复保存
            original_file = self.model_file
            self.model_file = None

        # Update uploaded_at timestamp for custom models only
        if not self.is_system_model:
            try:
                # Get the instance from DB only if it exists (i.e., we are updating)
                old_instance = AIModel.objects.get(pk=self.pk) if self.pk else None
            except AIModel.DoesNotExist:
                old_instance = None

            if self.pk is None: # Creating a new custom model
                 self.uploaded_at = timezone.now()
                 # No old_instance to compare with, so just set timestamp
            elif old_instance: # Updating an existing custom model
                # Check if the model_file field itself has changed.
                old_file_name = old_instance.model_file.name if old_instance.model_file else None
                new_file_name = self.model_file.name if self.model_file else None
                if old_file_name != new_file_name:
                    self.uploaded_at = timezone.now()
                # If only other fields changed, keep the original uploaded_at
            # else: self.pk exists but instance not found? Should not happen.

        # else: # System model
            # For system models, we let the default=timezone.now handle the creation timestamp.
            # If updating a system model, its existing uploaded_at timestamp will be preserved
            # unless explicitly changed before calling save().
            # No special logic needed here for system models regarding uploaded_at.
            pass

        super().save(*args, **kwargs)

        # 如果处理了新文件，恢复model_file字段的值（仅用于数据库存储路径）
        if is_new_file:
            # 直接更新数据库中的model_file字段，不触发save
            AIModel.objects.filter(pk=self.pk).update(model_file=self._stored_file_path)

    def _handle_file_storage(self):
        """
        处理文件存储路径，根据 is_system_model 选择不同的存储目录
        """
        if not self.model_file:
            return

        # 获取上传的文件
        uploaded_file = self.model_file.file
        original_name = self.model_file.name

        # 根据模型类别确定目标目录
        if self.is_system_model:
            base_dir = settings.SYSTEM_MODELS_ROOT
        else:
            base_dir = settings.MEDIA_ROOT

        # 构建目标路径
        target_dir = os.path.join(base_dir, self.model_type)
        os.makedirs(target_dir, exist_ok=True)

        # 构建目标文件路径
        filename = os.path.basename(original_name)
        target_path = os.path.join(target_dir, filename)

        # 如果文件已存在，生成新的文件名
        counter = 1
        base_name, ext = os.path.splitext(filename)
        while os.path.exists(target_path):
            new_filename = f"{base_name}_{counter}{ext}"
            target_path = os.path.join(target_dir, new_filename)
            filename = new_filename
            counter += 1

        # 保存文件到目标路径
        with open(target_path, 'wb') as destination:
            for chunk in uploaded_file.chunks():
                destination.write(chunk)

        # 存储相对路径，用于后续数据库更新
        relative_path = os.path.join(self.model_type, filename)
        self._stored_file_path = relative_path

    def delete(self, *args, **kwargs):
        """
        删除模型时同时删除对应的文件
        """
        if self.model_file and self.model_file.name:
            # 根据模型类别确定文件路径
            if self.is_system_model:
                base_dir = settings.SYSTEM_MODELS_ROOT
            else:
                base_dir = settings.MEDIA_ROOT

            file_path = os.path.join(base_dir, self.model_file.name)

            # 删除文件
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"已删除文件: {file_path}")
                except Exception as e:
                    print(f"删除文件失败: {file_path}, 错误: {e}")

        # 调用父类的删除方法
        super().delete(*args, **kwargs)

class ExampleImage(models.Model):
    """
    Represents an example image with its metadata, including display order.
    """
    # 核心字段
    name = models.CharField(max_length=255, help_text="文件名，例如 'image.jpg'")
    path = models.CharField(max_length=1024, help_text="文件在分类目录下的相对路径，例如 'folder1/image.jpg'")
    category = models.CharField(max_length=50, choices=[('barcode', '条码'), ('ocr', 'OCR'), ('ai_restored', 'AI修复')])
    
    # 排序和元数据
    display_order = models.IntegerField(default=0, help_text="用于排序的字段，数值越小越靠前")
    description = models.TextField(blank=True, null=True, help_text="图片的描述信息")
    file_size = models.PositiveIntegerField(default=0, help_text="文件大小（字节）")
    width = models.PositiveIntegerField(default=0)
    height = models.PositiveIntegerField(default=0)

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        # 保证在同一个目录下文件名是唯一的
        unique_together = [['category', 'path']]
        # 默认按显示顺序和名称排序
        ordering = ['display_order', 'name']

    def __str__(self):
        return f"[{self.category}] {self.path}"

    @property
    def url(self):
        """动态生成图片的访问URL"""
        from django.urls import reverse
        from urllib.parse import quote
        # 假设 URL 结构是 /api/vision/example-images/<category>/<path>
        # 注意：这里的 path 已经包含了文件夹和文件名
        return reverse('vision_app:serve_example_image', kwargs={
            'category': self.category,
            'filename': quote(self.path)
        })

    def get_physical_path(self):
        """获取图片的物理文件路径"""
        example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', os.path.join(settings.BASE_DIR, 'models', 'example_images'))
        return os.path.join(example_images_root, self.category, self.path)

    def delete(self, *args, **kwargs):
        """删除数据库记录时，同时删除物理文件"""
        physical_path = self.get_physical_path()
        
        super().delete(*args, **kwargs) # 先删除数据库记录

        if os.path.isfile(physical_path):
            try:
                os.remove(physical_path)
                print(f"Successfully deleted physical file: {physical_path}")
            except OSError as e:
                print(f"Error deleting physical file {physical_path}: {e}")
