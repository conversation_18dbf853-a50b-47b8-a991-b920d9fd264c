import React, { createContext, useState, useContext, useCallback, ReactNode, useEffect, useRef } from 'react';
import { cropImage } from '../utils/imageUtils'; // Added for cropping

// Define allowed MIME types
const ALLOWED_IMAGE_TYPES = [
    'image/jpeg',
    'image/png',
    'image/bmp',
    'image/webp',
    'image/avif'
];

// --- Types ---
export interface RecentImageItem {
 id: string; // Unique ID, e.g., timestamp + name, or just path for URLs
 name: string; // Filename or display name
 path: string; // Original file path (for local files, this might just be the name) or URL. For File objects, this will be file.name. For URLs, this will be the URL.
 type: 'local' | 'url'; // Type of the image source
 timestamp: number; // Timestamp of when it was last opened
}

export interface ImageInfo { // Exporting for potential use elsewhere
 name: string;
  width: number;
  height: number;
  type: string;
  url: string; // Object URL
}

/**
 * Represents a single displayable detection result (e.g., a bounding box with a label).
 */
export interface DisplayableDetection {
  box: [number, number, number, number]; // [x1, y1, x2, y2] - relative to original image dimensions
  label: string;
  type: 'barcode' | 'ocr_char' | 'ocr_line' | 'general_object'; // Add more types as needed
  confidence?: number; // Optional: confidence score
  id?: string | number; // Optional: unique ID for the detection
  class_id?: number; // Optional: class ID from the model
  class_name?: string; // Optional: class name from the model
  polygon?: [number, number][]; // Optional: Original polygon for OCR or complex shapes
  displayIndex?: number; // Optional: 1-based index for display in lists or overlays
}

export interface Roi {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FeatureMatch {
  point1: [number, number];
  point2: [number, number];
  distance?: number;
}

export interface ImageWorkspaceContextType { // Added export
  imageList: File[];
  currentImageIndex: number;
  currentImageInfo: ImageInfo | null;
  loadImage: (file: File) => Promise<void>;
  loadFolder: (files: FileList) => Promise<void>;
  setCurrentImageIndex: (index: number) => void;
  clearWorkspace: () => void;
  detectionResults: DisplayableDetection[] | null; // New: store detection results
  setDetectionResults: (results: DisplayableDetection[] | null) => Promise<void>; // New: function to set results
  isSelectingRoi: boolean;
  selectedRoi: Roi | null;
  startRoiSelection: () => void;
  setSelectedRoiCoordinates: (roi: Roi | null) => void;
  completeRoiSelection: () => void; // Add this to signal completion of ROI selection
  croppedImageDataUrl: string | null; // New: store cropped image data URL
  setCroppedImageDataUrl: (url: string | null) => void; // New: function to set cropped image data URL
  performCrop: () => Promise<void>; // New: function to perform cropping
  mouseImageCoordinates: { x: number, y: number } | null; // New: store mouse coordinates relative to image
  setMouseImageCoordinates: (coordinates: { x: number, y: number } | null) => void; // New: function to set mouse coordinates
  recentlyOpenedImages: RecentImageItem[];
  loadRecentImage: (item: RecentImageItem) => Promise<void>;
  registerDisplayedViewGetter?: (getter: (() => Promise<string | null>) | null) => void;
  getDisplayedViewDataURL?: () => Promise<string | null>;

  // New properties for composite OCR view
  originalImageInfo: ImageInfo | null; // To store the pristine image info when a composite is displayed
  setCompositeImageDisplay: (
    compositeImageUrl: string,
    compositeImageWidth: number,
    compositeImageHeight: number, // Changed from originalImageHeight
    compositeImageName: string // Changed from originalImageName
    // originalImageType: string // Not strictly needed if composite is always png
  ) => void;
  restoreOriginalImageDisplay: () => Promise<void>;
  restoreOriginalForAiRestore: () => Promise<void>; // 新增：专门用于AI复原的状态恢复

  // 强制刷新图像函数
  forceRefreshImage: (index: number) => Promise<boolean>;

  // 新增：用于控制图像切换按钮在批量处理期间的禁用状态
  isBatchProcessingActive: boolean;
  setIsBatchProcessingActive: (isActive: boolean) => void;
  featureMatches: FeatureMatch[] | null; // 新增：用于存储特征匹配线
  setFeatureMatches: (matches: FeatureMatch[] | null) => void; // 新增：用于设置特征匹配线
}

const RECENTLY_OPENED_IMAGES_KEY = 'recentlyOpenedImages';
const MAX_RECENT_IMAGES = 5;

// --- Context Creation ---
// ImageWorkspaceContext itself is not exported directly, use the hook 'useImageWorkspace'
const ImageWorkspaceContext = createContext<ImageWorkspaceContextType | undefined>(undefined);

// --- Provider Component ---
interface ImageWorkspaceProviderProps {
  children: ReactNode;
}

export const ImageWorkspaceProvider: React.FC<ImageWorkspaceProviderProps> = ({ children }) => {
  const [imageList, setImageList] = useState<File[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(-1);
  const [currentImageInfo, setCurrentImageInfoState] = useState<ImageInfo | null>(null); // Renamed to allow custom setter logic
  const [detectionResults, setDetectionResultsState] = useState<DisplayableDetection[] | null>(null);
  const [isSelectingRoi, setIsSelectingRoi] = useState<boolean>(false);
  const [selectedRoi, setSelectedRoi] = useState<Roi | null>(null);
  const [croppedImageDataUrl, setCroppedImageDataUrlState] = useState<string | null>(null);
  const [mouseImageCoordinates, setMouseImageCoordinatesState] = useState<{ x: number, y: number } | null>(null);
  const [recentlyOpenedImages, setRecentlyOpenedImages] = useState<RecentImageItem[]>([]);
  const getDisplayedViewDataURLRef = useRef<(() => Promise<string | null>) | null>(null);

  // New state for original image info when composite is shown
  const [originalImageInfo, setOriginalImageInfoState] = useState<ImageInfo | null>(null);
  // 新增：批量处理状态
  const [isBatchProcessingActive, setIsBatchProcessingActiveState] = useState<boolean>(false);
  const [featureMatches, setFeatureMatchesState] = useState<FeatureMatch[] | null>(null);

  const registerDisplayedViewGetter = useCallback((getter: (() => Promise<string | null>) | null) => {
    getDisplayedViewDataURLRef.current = getter;
  }, []);

  const getDisplayedViewDataURLInternal = useCallback(async (): Promise<string | null> => {
    if (getDisplayedViewDataURLRef.current) {
      return await getDisplayedViewDataURLRef.current();
    }
    console.warn('getDisplayedViewDataURL was called, but no getter is registered.');
    return null;
  }, []);

  useEffect(() => {
    const loadedImages = getRecentlyOpenedImagesFromStorage();
    setRecentlyOpenedImages(loadedImages);
  }, []);

  const getRecentlyOpenedImagesFromStorage = useCallback((): RecentImageItem[] => {
    try {
      const storedImages = localStorage.getItem(RECENTLY_OPENED_IMAGES_KEY);
      if (storedImages) {
        const parsedImages = JSON.parse(storedImages) as RecentImageItem[];
        if (Array.isArray(parsedImages) && parsedImages.every(img =>
            typeof img.id === 'string' &&
            typeof img.name === 'string' &&
            typeof img.path === 'string' &&
            (img.type === 'local' || img.type === 'url') &&
            typeof img.timestamp === 'number'
        )) {
          return parsedImages;
        } else {
          console.warn('Invalid data structure in localStorage for recently opened images.');
          localStorage.removeItem(RECENTLY_OPENED_IMAGES_KEY);
        }
      }
    } catch (error) {
      console.error('Error reading/parsing recently opened images from localStorage:', error);
    }
    return [];
  }, []);

  const saveRecentlyOpenedImagesToStorage = useCallback((images: RecentImageItem[]) => {
    try {
      localStorage.setItem(RECENTLY_OPENED_IMAGES_KEY, JSON.stringify(images));
    } catch (error) {
      console.error('Error saving recently opened images to localStorage:', error);
    }
  }, []);

  const addImageToRecentList = useCallback((imageInfoToAdd: { name: string, path: string, type: 'local' | 'url' }) => {
    setRecentlyOpenedImages(prevImages => {
      const now = Date.now();
      const newItem: RecentImageItem = {
        id: `${imageInfoToAdd.type}-${imageInfoToAdd.path}`,
        name: imageInfoToAdd.name,
        path: imageInfoToAdd.path,
        type: imageInfoToAdd.type,
        timestamp: now,
      };
      const filteredImages = prevImages.filter(img => img.id !== newItem.id);
      const updatedImages = [newItem, ...filteredImages].slice(0, MAX_RECENT_IMAGES);
      saveRecentlyOpenedImagesToStorage(updatedImages);
      return updatedImages;
    });
  }, [saveRecentlyOpenedImagesToStorage]);

  const getImageInfoFromFile = (file: File): Promise<ImageInfo> => {
    return new Promise((resolve, reject) => {
      const url = URL.createObjectURL(file);
      const img = new Image();
      img.onload = () => {
        resolve({
          name: file.name,
          width: img.naturalWidth,
          height: img.naturalHeight,
          type: file.type,
          url: url
        });
      };
      img.onerror = (err) => {
        URL.revokeObjectURL(url);
        reject(err);
      };
      img.src = url;
    });
  };

  // Centralized function to set current image and ensure original is cleared if it's a new pristine image
  const setCurrentPristineImage = useCallback((newImageInfo: ImageInfo | null) => {
    if (currentImageInfo?.url && currentImageInfo.url !== newImageInfo?.url) {
        // Revoke previous object URL if it exists and is different,
        // unless it's the original image URL we are restoring (handled in restoreOriginal)
        // Or if it's a data URL from composite (no revoke needed)
        if (currentImageInfo.url.startsWith('blob:') && (!originalImageInfo || originalImageInfo.url !== currentImageInfo.url)) {
             URL.revokeObjectURL(currentImageInfo.url);
        }
    }
    setCurrentImageInfoState(newImageInfo);
    setOriginalImageInfoState(null); // Clear original info when a new pristine image is set
    setDetectionResultsState(null);  // Clear detections
    setCroppedImageDataUrlState(null); // Clear any crop
    setSelectedRoi(null); // Clear ROI selection when setting new pristine image
    setFeatureMatchesState(null); // 清除特征匹配线
  }, [currentImageInfo, originalImageInfo]);


  const restoreOriginalImageDisplay = useCallback(async () => {
    if (originalImageInfo) {
      // If currentImageInfo.url is a data URL (from composite), no need to revoke
      // If it was a blob URL for some other temporary state, it should have been revoked when originalImageInfo was set.
      setCurrentImageInfoState(originalImageInfo);
      setOriginalImageInfoState(null);
      setDetectionResultsState(null); // Clear results when restoring to original
      // Cropped image data and mouse coordinates might also need clearing or re-evaluation
      setCroppedImageDataUrlState(null);
      setMouseImageCoordinatesState(null);
      setSelectedRoi(null); // Clear ROI selection when restoring to original
      setFeatureMatchesState(null); // 清除特征匹配线

      // 添加一个小延迟，确保状态更新完成
      await new Promise(resolve => setTimeout(resolve, 150));
    }
    return Promise.resolve(); // 始终返回一个Promise，即使没有originalImageInfo
  }, [originalImageInfo, currentImageInfo]);

  // 新增：专门用于AI复原的临时状态恢复，不清空originalImageInfo
  const restoreOriginalForAiRestore = useCallback(async () => {
    if (originalImageInfo) {
      console.log('[ImageWorkspaceContext] AI复原：临时恢复原始图像显示，保持originalImageInfo');
      setCurrentImageInfoState(originalImageInfo);
      // 注意：不清空originalImageInfo，保持原始图像信息用于后续修复
      setDetectionResultsState(null); // Clear results when restoring to original
      setCroppedImageDataUrlState(null);
      setMouseImageCoordinatesState(null);
      setSelectedRoi(null); // Clear ROI selection when restoring to original
      setFeatureMatchesState(null); // 清除特征匹配线

      // 添加一个小延迟，确保状态更新完成
      await new Promise(resolve => setTimeout(resolve, 150));
    }
    return Promise.resolve();
  }, [originalImageInfo]);

  const clearWorkspace = useCallback(() => {
    if (currentImageInfo?.url && currentImageInfo.url.startsWith('blob:')) {
      URL.revokeObjectURL(currentImageInfo.url);
    }
    if (originalImageInfo?.url && originalImageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(originalImageInfo.url); // Also revoke original if it's a blob
    }
    setImageList([]);
    setCurrentImageIndex(-1);
    setCurrentPristineImage(null); // This will set currentImageInfo to null and clear originalImageInfo
    // DetectionResults, selectedRoi, croppedImageDataUrl are cleared by setCurrentPristineImage or should be cleared here too
    setSelectedRoi(null);
    setFeatureMatchesState(null); // 清除特征匹配线
  }, [currentImageInfo, originalImageInfo, setCurrentPristineImage]);

  const loadImage = useCallback(async (file: File) => {
    // clearWorkspace(); // clearWorkspace now calls setCurrentPristineImage, which might be too much here
    // Instead, directly manage current and original image state
    if (currentImageInfo?.url && currentImageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(currentImageInfo.url);
    }
    if (originalImageInfo?.url && originalImageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(originalImageInfo.url);
    }

    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      console.error('Unsupported image format:', file.name, file.type);
      alert(`Unsupported image format: ${file.name} (${file.type}). Please select a JPG, PNG, BMP, WEBP, or AVIF file.`);
      setCurrentPristineImage(null); // Ensure clean state if load fails early
      setImageList([]);
      setCurrentImageIndex(-1);
      return;
    }
    try {
      const info = await getImageInfoFromFile(file);
      setImageList([file]);
      setCurrentImageIndex(0);
      setCurrentPristineImage(info); // Set as new pristine image
      addImageToRecentList({ name: file.name, path: file.name, type: 'local' });
    } catch (error) {
      console.error('Error loading image:', error);
      alert(`Error loading image "${file.name}": ${error instanceof Error ? error.message : String(error)}`);
      clearWorkspace(); // Full clear on error
    }
  }, [clearWorkspace, addImageToRecentList, setCurrentPristineImage, currentImageInfo, originalImageInfo]);

  const loadFolder = useCallback(async (files: FileList) => {
    if (currentImageInfo?.url && currentImageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(currentImageInfo.url);
    }
    if (originalImageInfo?.url && originalImageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(originalImageInfo.url);
    }

    const imageFiles = Array.from(files)
      .filter(file => ALLOWED_IMAGE_TYPES.includes(file.type))
      .sort((a, b) => a.name.localeCompare(b.name));

    if (imageFiles.length === 0) {
      console.warn('No supported image files found in the selected folder.');
      alert('No supported image files (JPG, PNG, BMP, WEBP, AVIF) found in the selected folder.');
      setCurrentPristineImage(null); // Ensure clean state
      setImageList([]);
      setCurrentImageIndex(-1);
      return;
    }

    setImageList(imageFiles);
    setCurrentImageIndex(0);
    try {
      const firstImageInfo = await getImageInfoFromFile(imageFiles[0]);
      setCurrentPristineImage(firstImageInfo); // Set as new pristine image
      imageFiles.forEach(f => addImageToRecentList({ name: f.name, path: f.name, type: 'local' }));
    } catch (error) {
      console.error('Error loading first image from folder:', error);
      alert(`Error loading first image from folder: ${error instanceof Error ? error.message : String(error)}`);
      clearWorkspace(); // Full clear on error
    }
  }, [clearWorkspace, addImageToRecentList, setCurrentPristineImage, currentImageInfo, originalImageInfo]);

  const setCurrentImageIndexHandler = useCallback(async (index: number) => {
    if (index >= 0 && index < imageList.length && index !== currentImageIndex) {
      // 关键修复：图像切换时直接清理状态，不恢复原图显示
      // 这确保用户切换图像时立即看到新图像，而不是先看到旧图像的原图
      console.log('[ImageWorkspaceContext] 图像切换：直接清理状态，不恢复原图显示');

      // 清理合成视图状态
      if (originalImageInfo) {
        console.log('[ImageWorkspaceContext] 清理合成视图状态');
        setOriginalImageInfoState(null);
      }

      // 清除检测结果，确保在切换图像前没有任何检测结果
      setDetectionResultsState(null);
      // 清除ROI选择，确保在切换图像时ROI区域消失
      setSelectedRoi(null);

      // Revoke previous URLs before setting new one
      if (currentImageInfo?.url && currentImageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(currentImageInfo.url);
      }
      if (originalImageInfo?.url && originalImageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(originalImageInfo.url); // Ensure this is cleaned up too
      }

      try {
        const newImageInfo = await getImageInfoFromFile(imageList[index]);

        // 先设置图像索引，然后设置图像信息
        setCurrentImageIndex(index);

        // 等待一帧，确保索引更新
        await new Promise(resolve => requestAnimationFrame(resolve));

        // 设置新的图像信息
        setCurrentPristineImage(newImageInfo); // Set as new pristine image
        addImageToRecentList({ name: newImageInfo.name, path: newImageInfo.name, type: 'local' });

        // 等待图像信息更新完成
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Error loading image at index ${index}:`, error);
        alert(`Error loading image "${imageList[index]?.name || `at index ${index}`}" : ${error instanceof Error ? error.message : String(error)}`);
        clearWorkspace();
      }
    }
  }, [imageList, currentImageIndex, clearWorkspace, addImageToRecentList, setCurrentPristineImage, currentImageInfo, originalImageInfo, restoreOriginalImageDisplay]);

  const setDetectionResults = useCallback(async (results: DisplayableDetection[] | null) => {
    // If we are setting new detection results, it implies we are working with the original image,
    // so, if a composite view was active, we might want to restore the original image.
    if (originalImageInfo && results !== null) {
        // This means detection results are being set while a composite view's original is stored.
        // This implies the user might have initiated a new detection on what they *see* as the original.
        // So, we should probably restore fully.
        await restoreOriginalImageDisplay(); // This will also clear results. Then set the new ones.
        await new Promise(resolve => setTimeout(resolve, 100)); // 确保状态更新完成
        setDetectionResultsState(results); // Set the new results for the (now current) original image.
    } else {
       setDetectionResultsState(results);
   }
 }, [originalImageInfo, restoreOriginalImageDisplay, currentImageInfo]);

 const setFeatureMatches = useCallback((matches: FeatureMatch[] | null) => {
   setFeatureMatchesState(matches);
 }, []);

  const startRoiSelection = useCallback(() => {
    if (originalImageInfo) {
        alert("ROI selection is intended for the original image. Please clear OCR processed view first if you want to select ROI on the original image.");
        return; // Prevent ROI selection on composite view
    }
    setIsSelectingRoi(true);
    setSelectedRoi(null);
  }, [originalImageInfo]);

  const setSelectedRoiCoordinates = useCallback((roi: Roi | null) => {
    setSelectedRoi(roi);
  }, []);

  const completeRoiSelection = useCallback(() => {
    setIsSelectingRoi(false);
  }, []);

  const setCroppedImageDataUrl = useCallback((url: string | null) => {
    setCroppedImageDataUrlState(url);
  }, []);

  const performCrop = useCallback(async () => {
    if (originalImageInfo) {
        console.warn('Cropping is based on the original image. A composite view is active.');
        // Potentially allow cropping on originalImageInfo if needed, but for now, this is a warning.
    }
    if (currentImageInfo?.url && selectedRoi && selectedRoi.width > 0 && selectedRoi.height > 0) {
      try {
        // Use currentImageInfo.url which SHOULD be the original if originalImageInfo is null
        const imageToCropUrl = originalImageInfo ? originalImageInfo.url : currentImageInfo.url;
        console.log('Performing crop with ROI:', selectedRoi, 'on image:', imageToCropUrl);
        const croppedUrl = await cropImage(imageToCropUrl, selectedRoi);
        setCroppedImageDataUrlState(croppedUrl);
      } catch (error) {
        console.error('Error performing crop:', error);
        setCroppedImageDataUrlState(null);
      }
    } else {
        // ... (existing checks)
    }
  }, [currentImageInfo, selectedRoi, originalImageInfo]);

  const setMouseImageCoordinates = useCallback((coordinates: { x: number, y: number } | null) => {
    setMouseImageCoordinatesState(coordinates);
  }, []);

  const loadRecentImage = useCallback(async (item: RecentImageItem) => {
    // When loading a recent image, it's always a pristine image.
    // Ensure any composite view is cleared.
    restoreOriginalImageDisplay(); // Clear any active composite view

    addImageToRecentList({ name: item.name, path: item.path, type: item.type });

    if (item.type === 'local') {
      alert(`"${item.name}" is a local file. Please re-select it via "File" > "Open Image..." or by dragging it. This entry has been moved to the top of recent files.`);
      // No direct file loading; UI (MenuBar) guides user.
      // If they select it, loadImage will handle it as a new pristine image.
      return;
    } else if (item.type === 'url') {
      alert(`Loading from URL "${item.name}" is not yet implemented. This entry has been moved to the top of recent files.`);
      // Placeholder for future URL loading logic
    }
  }, [addImageToRecentList, restoreOriginalImageDisplay]);


  useEffect(() => {
    if (selectedRoi && selectedRoi.width > 0 && selectedRoi.height > 0 && (currentImageInfo?.url || originalImageInfo?.url)) {
      const urlForCrop = originalImageInfo ? originalImageInfo.url : currentImageInfo?.url;
      if (urlForCrop) {
        // console.log('useEffect triggered for performCrop due to selectedRoi change:', selectedRoi);
        // performCrop(); // performCrop is called, but let's ensure it uses the right base image if originalImageInfo exists
         cropImage(urlForCrop, selectedRoi)
          .then(croppedUrl => setCroppedImageDataUrlState(croppedUrl))
          .catch(error => {
            console.error('Error performing crop from useEffect:', error);
            setCroppedImageDataUrlState(null);
          });
      }
    } else if (!selectedRoi || (selectedRoi && (selectedRoi.width === 0 || selectedRoi.height === 0))) {
      if (croppedImageDataUrl) {
        setCroppedImageDataUrlState(null);
      }
    }
  }, [selectedRoi, currentImageInfo, originalImageInfo, croppedImageDataUrl]); // Removed performCrop from deps to avoid re-trigger cycle

  // 添加一个函数，强制刷新当前图像
  const forceRefreshImage = useCallback(async (index: number) => {
    if (index < 0 || index >= imageList.length) {
      return false;
    }

    try {
      // 清除检测结果
      setDetectionResultsState(null);
      // 清除ROI选择
      setSelectedRoi(null);

      // 如果当前是合成视图，先恢复原始图像
      if (originalImageInfo) {
        setCurrentImageInfoState(originalImageInfo);
        setOriginalImageInfoState(null);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 获取图像信息
      const file = imageList[index];
      const newImageInfo = await getImageInfoFromFile(file);

      // 直接设置当前图像信息，而不是通过setCurrentImageIndex
      setCurrentImageIndex(index);
      setCurrentImageInfoState(newImageInfo);

      // 等待图像加载完成
      await new Promise(resolve => setTimeout(resolve, 200));

      return true;
    } catch (error) {
      console.error('强制刷新图像失败:', error);
      return false;
    }
  }, [imageList, originalImageInfo, getImageInfoFromFile]);

  // --- New function for setting composite image ---
  const setCompositeImageDisplay = useCallback((
    compositeImageUrl: string,
    compositeImageWidth: number,
    compositeImageHeight: number, // Changed from originalImageHeight
    compositeImageName: string // Changed from originalImageName
  ) => {
    if (currentImageInfo) {
      // 关键修复：只有在originalImageInfo为空时才保存当前图像信息
      // 这确保originalImageInfo始终保存的是真正的原始图像，而不是合成图
      if (!originalImageInfo) {
        console.log('[ImageWorkspaceContext] 首次设置合成视图，保存原始图像信息:', {
          name: currentImageInfo.name,
          width: currentImageInfo.width,
          height: currentImageInfo.height
        });
        setOriginalImageInfoState({ ...currentImageInfo }); // Save current as original only if not already saved
      } else {
        console.log('[ImageWorkspaceContext] 多次设置合成视图，保持原始图像信息不变:', {
          originalName: originalImageInfo.name,
          originalWidth: originalImageInfo.width,
          originalHeight: originalImageInfo.height
        });
      }

      setCurrentImageInfoState({ // Update current to composite
        name: compositeImageName, // Use the provided full name
        url: compositeImageUrl, // This is likely a dataURL, no revoke needed for it
        width: compositeImageWidth,
        height: compositeImageHeight, // Use the provided composite height
        type: 'image/png', // Composite is always PNG (or should be determined by API if variable)
      });
      setDetectionResultsState(null); // Clear SvgOverlay drawings as composite is complete
      setSelectedRoi(null); // Clear ROI
      setCroppedImageDataUrlState(null); // Clear any cropped view
    }
  }, [currentImageInfo, originalImageInfo]); // 添加originalImageInfo作为依赖

  const contextValue: ImageWorkspaceContextType = {
    imageList,
    currentImageIndex,
    currentImageInfo,
    loadImage,
    loadFolder,
    setCurrentImageIndex: setCurrentImageIndexHandler,
    clearWorkspace,
    detectionResults,
    setDetectionResults,
    isSelectingRoi,
    selectedRoi,
    startRoiSelection,
    setSelectedRoiCoordinates,
    completeRoiSelection,
    croppedImageDataUrl,
    setCroppedImageDataUrl,
    performCrop,
    mouseImageCoordinates,
    setMouseImageCoordinates,
    recentlyOpenedImages,
    loadRecentImage,
    registerDisplayedViewGetter,
    getDisplayedViewDataURL: getDisplayedViewDataURLInternal,
    // New context members
    originalImageInfo,
    setCompositeImageDisplay,
    restoreOriginalImageDisplay,
    restoreOriginalForAiRestore, // 新增：专门用于AI复原的状态恢复
    forceRefreshImage, // 添加强制刷新图像函数
    isBatchProcessingActive, // 新增
    setIsBatchProcessingActive: setIsBatchProcessingActiveState, // 新增
    featureMatches,
    setFeatureMatches,
  };

  return (
    <ImageWorkspaceContext.Provider value={contextValue}>
      {children}
    </ImageWorkspaceContext.Provider>
  );
};

// --- Hook for consuming context ---
export const useImageWorkspace = (): ImageWorkspaceContextType => {
  const context = useContext(ImageWorkspaceContext);
  if (context === undefined) {
    throw new Error('useImageWorkspace must be used within an ImageWorkspaceProvider');
  }
  return context;
};