import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ConfigProvider, App as AntApp } from 'antd'
import './index.css'
import App from './App'
import { ImageWorkspaceProvider } from './contexts/ImageWorkspaceContext.tsx'
import { FunctionPanelProvider } from './contexts/FunctionPanelContext.tsx'
import { BarcodeDetectionProvider } from './contexts/BarcodeDetectionContext.tsx'
import { OcrDetectionProvider } from './contexts/OcrDetectionContext.tsx'
import { AiRestoreDetectionProvider } from './contexts/AiRestoreDetectionContext.tsx' // Import AiRestoreDetectionProvider
import { AdminProvider } from './contexts/AdminContext.tsx' // Import AdminProvider

const queryClient = new QueryClient()

// 自定义Ant Design主题
const customTheme = {
  token: {
    colorPrimary: '#1890ff', // 主色调
    colorPrimaryHover: '#40a9ff', // 鼠标悬停色
    colorPrimaryActive: '#096dd9', // 点击时的颜色，更深
  },
  components: {
    Radio: {
      // 强化单选按钮选中状态的背景色
      colorPrimary: '#1890ff',
      colorPrimaryHover: '#40a9ff',
      colorPrimaryActive: '#096dd9',
    },
    Button: {
      // 增强按钮选中状态的视觉效果
      colorPrimaryHover: '#40a9ff',
      colorPrimaryActive: '#096dd9',
    }
  }
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <ConfigProvider theme={customTheme}>
        <AntApp>
          <AdminProvider> {/* AdminProvider added here */}
            <ImageWorkspaceProvider>
              <FunctionPanelProvider>
                <BarcodeDetectionProvider>
                  <OcrDetectionProvider>
                    <AiRestoreDetectionProvider> {/* AiRestoreDetectionProvider added here */}
                      <BrowserRouter>
                        <App />
                      </BrowserRouter>
                    </AiRestoreDetectionProvider>
                  </OcrDetectionProvider>
                </BarcodeDetectionProvider>
              </FunctionPanelProvider>
            </ImageWorkspaceProvider>
          </AdminProvider>
        </AntApp>
      </ConfigProvider>
    </QueryClientProvider>
  </React.StrictMode>,
)
