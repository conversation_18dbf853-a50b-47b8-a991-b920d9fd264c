import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Spin, Result, Button } from 'antd';
import { LoadingOutlined, LockOutlined } from '@ant-design/icons';
import { useAdmin } from '../contexts/AdminContext';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * 管理员权限保护路由组件
 * 用于保护需要管理员权限才能访问的页面
 */
const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { isAdmin, isLoading } = useAdmin();
  const navigate = useNavigate();

  // 如果正在检查管理员状态，显示加载指示器
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)'
      }}>
        <div style={{ textAlign: 'center' }}>
          <Spin
            indicator={<LoadingOutlined style={{ fontSize: 48, color: '#1890ff' }} spin />}
            size="large"
          />
          <div style={{
            marginTop: '24px',
            fontSize: '16px',
            color: '#666',
            fontWeight: 500
          }}>
            正在验证管理员权限...
          </div>
        </div>
      </div>
    );
  }

  // 如果不是管理员，显示权限不足页面
  if (!isAdmin) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)'
      }}>
        <Result
          icon={<LockOutlined style={{ color: '#ff4d4f' }} />}
          title="访问受限"
          subTitle="您需要管理员权限才能访问此页面"
          extra={[
            <Button
              type="primary"
              key="back"
              onClick={() => window.history.back()}
              style={{
                borderRadius: '6px',
                fontWeight: 500
              }}
            >
              返回上一页
            </Button>,
            <Button
              key="home"
              onClick={() => navigate('/', { replace: true })}
              style={{
                borderRadius: '6px',
                fontWeight: 500
              }}
            >
              返回首页
            </Button>
          ]}
          style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '40px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            maxWidth: '500px',
            width: '90%'
          }}
        />
      </div>
    );
  }

  // 如果是管理员，渲染子组件
  return <>{children}</>;
};

export default AdminProtectedRoute;
