import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Form, App } from 'antd';
import { useScanner } from '../contexts/ScannerContext';

interface DeviceConnectModalProps {
  visible: boolean;
  onCancel: () => void;
}

const DeviceConnectModal: React.FC<DeviceConnectModalProps> = ({ visible, onCancel }) => {
  const [form] = Form.useForm();
  const { connect } = useScanner();
  const { message } = App.useApp();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      // 从localStorage获取保存的IP地址
      const savedIp = localStorage.getItem('scannerDeviceIP');
      if (savedIp) {
        form.setFieldsValue({ ipAddress: savedIp });
      }
    }
  }, [visible, form]);

  const handleConnect = async (values: { ipAddress: string }) => {
    setIsLoading(true);
    message.loading({ content: `正在连接到 ${values.ipAddress}...`, key: 'connect' });
    
    const success = await connect(values.ipAddress);
    
    setIsLoading(false);
    if (success) {
      message.success({ content: '设备连接成功！', key: 'connect', duration: 2 });
      // 保存IP地址到localStorage
      localStorage.setItem('scannerDeviceIP', values.ipAddress);
      onCancel(); // 关闭模态框
    } else {
      message.error({ content: '设备连接失败，请检查IP地址或网络。', key: 'connect', duration: 3 });
    }
  };

  return (
    <Modal
      title="连接扫描仪设备"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={isLoading} onClick={() => form.submit()}>
          连接
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleConnect}
        initialValues={{ ipAddress: '' }}
      >
        <Form.Item
          name="ipAddress"
          label="设备IP地址"
          rules={[
            { required: true, message: '请输入IP地址' },
            {
              pattern: /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/,
              message: '请输入有效的IP地址格式',
            },
          ]}
        >
          <Input placeholder="例如: *************" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DeviceConnectModal;