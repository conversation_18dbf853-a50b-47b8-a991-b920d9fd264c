import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Typography, Space, Button, Alert, Spin } from 'antd';
import {
  DatabaseOutlined,
  PictureOutlined,
  SettingOutlined,
  RightOutlined,
  InfoCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { getExampleImagesForDashboard, getGroupedVisionModels } from '../services/api';

const { Title, Paragraph, Text } = Typography;

/**
 * 管理后台主页
 * 显示系统概览和快速操作入口
 */
const AdminDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [systemStats, setSystemStats] = useState({
    systemModels: 0,
    userModels: 0,
    exampleImages: 0,
    functionModules: 3 // 固定值：条码检测、OCR识别、AI复原
  });
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // 获取系统统计数据
  const fetchSystemStats = async () => {
    try {
      setLoading(true);

      // 并行获取模型数据和示例图片数据
      const [modelsResponse, imagesResponse] = await Promise.all([
        getGroupedVisionModels('all'),
        getExampleImagesForDashboard()
      ]);

      // 统计模型数量
      let systemModelsCount = 0;
      let userModelsCount = 0;

      Object.values(modelsResponse).forEach(models => {
        models.forEach(model => {
          if (model.is_system_model) {
            systemModelsCount++;
          } else {
            userModelsCount++;
          }
        });
      });

      // 统计示例图片数量
      const exampleImagesCount =
        imagesResponse.barcode.length +
        imagesResponse.ocr.length +
        imagesResponse.ai_restored.length;

      // 更新状态
      setSystemStats({
        systemModels: systemModelsCount,
        userModels: userModelsCount,
        exampleImages: exampleImagesCount,
        functionModules: 3
      });

      setLastUpdated(new Date());
    } catch (error) {
      console.error('获取系统统计数据失败:', error);
      // 保持默认值，不显示错误给用户
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchSystemStats();
  }, []);

  // 监听路由变化，当从其他管理页面返回时刷新数据
  useEffect(() => {
    // 如果当前在管理后台概览页面，且之前可能在其他管理页面，则刷新数据
    if (location.pathname === '/admin' && lastUpdated) {
      const timeSinceLastUpdate = Date.now() - lastUpdated.getTime();
      // 如果距离上次更新超过30秒，则自动刷新
      if (timeSinceLastUpdate > 30000) {
        fetchSystemStats();
      }
    }
  }, [location.pathname, lastUpdated]);

  // 快速操作卡片数据
  const quickActions = [
    {
      title: '模型管理',
      description: '管理AI模型文件，包括系统模型和用户上传的自定义模型',
      icon: <DatabaseOutlined style={{ fontSize: '32px', color: '#1890ff' }} />,
      path: '/admin/models',
      features: ['查看模型列表', '删除模型文件', '上传新模型', '模型分类管理'],
      stats: `系统模型 ${systemStats.systemModels} 个，用户模型 ${systemStats.userModels} 个`
    },
    {
      title: '示例图片管理',
      description: '管理示例图片库，为用户提供测试和演示用的图像资源',
      icon: <PictureOutlined style={{ fontSize: '32px', color: '#52c41a' }} />,
      path: '/admin/images',
      features: ['查看图片列表', '删除图片文件', '上传新图片', '图片分类管理'],
      stats: `当前共有 ${systemStats.exampleImages} 张示例图片`
    }
  ];

  // 系统统计数据配置
  const systemStatsConfig = [
    {
      title: '系统模型',
      value: systemStats.systemModels,
      suffix: '个',
      valueStyle: { color: '#1890ff' }
    },
    {
      title: '用户模型',
      value: systemStats.userModels,
      suffix: '个',
      valueStyle: { color: '#722ed1' }
    },
    {
      title: '示例图片',
      value: systemStats.exampleImages,
      suffix: '张',
      valueStyle: { color: '#52c41a' }
    },
    {
      title: '功能模块',
      value: systemStats.functionModules,
      suffix: '个',
      valueStyle: { color: '#fa8c16' }
    }
  ];

  const handleQuickAction = (path: string) => {
    navigate(path);
  };

  return (
    <div style={{
      width: '100%',
      maxWidth: '1400px', // 设置最大宽度，避免在超大屏幕上过度拉伸
      margin: '0 auto' // 居中显示
    }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '28px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            <SettingOutlined style={{ marginRight: '12px' }} />
            管理后台概览
          </Title>
          <Space>
            {lastUpdated && (
              <Text type="secondary" style={{ fontSize: '14px' }}>
                最后更新: {lastUpdated.toLocaleTimeString()}
              </Text>
            )}
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchSystemStats}
              loading={loading}
              type="text"
              size="small"
            >
              刷新数据
            </Button>
          </Space>
        </div>
        <Paragraph style={{
          fontSize: '16px',
          color: '#666',
          marginBottom: 0
        }}>
          欢迎使用AI视觉应用管理后台，您可以在这里管理模型文件和示例图片资源
        </Paragraph>
      </div>

      {/* 欢迎提示 */}
      <Alert
        message="管理员权限已激活"
        description="您现在可以访问所有管理功能，包括模型管理和示例图片管理。请谨慎操作，确保系统稳定运行。"
        type="info"
        icon={<InfoCircleOutlined />}
        showIcon
        style={{
          marginBottom: '28px',
          borderRadius: '8px',
          border: '1px solid #bae7ff',
          width: '100%'
        }}
      />

      {/* 系统统计 */}
      <Row gutter={[20, 20]} style={{ marginBottom: '28px', width: '100%' }}>
        {systemStatsConfig.map((stat, index) => (
          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6} key={index}>
            <Card
              style={{
                textAlign: 'center',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                border: '1px solid #f0f0f0',
                height: '100%'
              }}
            >
              <Spin spinning={loading && stat.title !== '功能模块'}>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  suffix={stat.suffix}
                  valueStyle={stat.valueStyle}
                />
              </Spin>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 快速操作 */}
      <Title level={3} style={{ marginBottom: '24px', color: '#333' }}>
        快速操作
      </Title>

      <Row gutter={[24, 24]} style={{ width: '100%' }}>
        {quickActions.map((action, index) => (
          <Col xs={24} sm={24} md={12} lg={12} xl={12} xxl={12} key={index}>
            <Card
              hoverable
              style={{
                borderRadius: '12px',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08)',
                border: '1px solid #f0f0f0',
                height: '100%',
                minHeight: '280px'
              }}
              styles={{
                body: {
                  padding: '24px',
                  display: 'flex',
                  flexDirection: 'column',
                  height: '100%'
                }
              }}
            >
              {/* 卡片内容区域 */}
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                {/* 图标和标题 */}
                <Space size="middle" style={{ marginBottom: '16px' }}>
                  {action.icon}
                  <div style={{ flex: 1 }}>
                    <Title level={4} style={{ margin: 0, color: '#333' }}>
                      {action.title}
                    </Title>
                    <Text type="secondary" style={{ fontSize: '14px', display: 'block', marginBottom: '4px' }}>
                      {action.description}
                    </Text>
                    <Text type="secondary" style={{ fontSize: '12px', color: '#999' }}>
                      {action.stats}
                    </Text>
                  </div>
                </Space>

                {/* 功能列表 */}
                <div style={{ flex: 1, marginBottom: '20px' }}>
                  <Text strong style={{ color: '#666', fontSize: '13px' }}>
                    主要功能：
                  </Text>
                  <ul style={{
                    margin: '8px 0 0 0',
                    paddingLeft: '16px',
                    color: '#666'
                  }}>
                    {action.features.map((feature, idx) => (
                      <li key={idx} style={{ fontSize: '13px', lineHeight: '1.6' }}>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* 操作按钮 - 固定在底部 */}
                <Button
                  type="primary"
                  icon={<RightOutlined />}
                  onClick={() => handleQuickAction(action.path)}
                  style={{
                    width: '100%',
                    height: '40px',
                    borderRadius: '6px',
                    fontWeight: 500,
                    marginTop: 'auto'
                  }}
                >
                  进入管理
                </Button>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 使用说明 */}
      <Card
        title="使用说明"
        style={{
          marginTop: '28px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          width: '100%'
        }}
      >
        <Row gutter={[32, 20]} style={{ width: '100%' }}>
          <Col xs={24} md={12} lg={12} xl={12}>
            <Title level={5} style={{ color: '#1890ff' }}>模型管理</Title>
            <Paragraph style={{ color: '#666', fontSize: '14px' }}>
              • 系统模型：预置的AI模型，支持条码检测、OCR识别、图像修复<br/>
              • 用户模型：用户上传的自定义模型文件<br/>
              • 支持.pt格式的模型文件上传和管理
            </Paragraph>
          </Col>
          <Col xs={24} md={12} lg={12} xl={12}>
            <Title level={5} style={{ color: '#52c41a' }}>示例图片管理</Title>
            <Paragraph style={{ color: '#666', fontSize: '14px' }}>
              • 按AI功能分类：条码、OCR、AI复原<br/>
              • 支持JPG、PNG、BMP、WEBP等格式<br/>
              • 为用户提供测试和演示用的标准图像
            </Paragraph>
          </Col>
        </Row>
      </Card>

      {/* 添加一些额外内容以确保滚动条显示 */}
      <div style={{
        marginTop: '32px',
        padding: '20px',
        background: '#f9f9f9',
        borderRadius: '8px',
        textAlign: 'center',
        color: '#666'
      }}>
        <p style={{ margin: 0, fontSize: '14px' }}>
          管理后台功能正在持续完善中，更多功能敬请期待...
        </p>
      </div>
    </div>
  );
};

export default AdminDashboardPage;
