# AI Vision Platform - 前端应用

基于 React + TypeScript + Vite 构建的现代化 AI 视觉处理前端应用，提供直观易用的图像处理、AI模型管理和推理任务界面。

## 🎯 项目概述

AI Vision Platform 前端是一个功能丰富的 Web 应用，支持多种 AI 视觉任务：
- **条码检测** - 智能识别各类条码和二维码
- **OCR 文字识别** - 支持多种场景的文字提取
- **AI 图像修复** - 智能图像增强和修复
- **图像预处理** - 多种图像处理工具

## 📁 项目结构

```
Frontend/
├── 📄 配置文件
│   ├── package.json                 # 项目依赖和脚本配置
│   ├── vite.config.ts              # Vite 构建配置
│   ├── tsconfig.json               # TypeScript 配置
│   ├── eslint.config.js            # ESLint 代码规范配置
│   ├── electron-builder.json       # Electron 打包配置
│   └── index.html                  # 应用入口 HTML
├── 🌍 环境配置
│   ├── .env.development            # 开发环境配置
│   ├── .env.production             # 生产环境配置 (Docker)
│   ├── .env.hotreload              # 热重载环境配置
│   └── .env.server                 # 服务器环境配置
├── 🖥️ Electron 桌面应用
│   └── electron/
│       ├── main.cjs                # Electron 主进程
│       ├── preload.cjs             # 预加载脚本
│       └── backend.cjs             # 内置后端服务
├── 📦 构建产物
│   └── dist/                       # 生产构建输出目录
├── 🎨 静态资源
│   └── public/
│       ├── loading.html            # 加载页面
│       └── vite.svg               # 应用图标
└── 💻 源代码
    └── src/
        ├── 🧩 组件层
        │   ├── components/
        │   │   ├── ImageDisplay/           # 图像显示核心组件
        │   │   │   ├── hooks/              # 图像交互自定义 Hooks
        │   │   │   │   ├── useImageDropUpload.ts    # 拖拽上传
        │   │   │   │   ├── useImagePanZoom.ts       # 缩放平移
        │   │   │   │   ├── useMouseCoordinates.ts   # 鼠标坐标
        │   │   │   │   └── useRoiInteraction.ts     # ROI 区域选择
        │   │   │   ├── SvgOverlay.tsx      # SVG 检测结果覆盖层
        │   │   │   └── ImageDisplay.tsx    # 图像显示主组件
        │   │   ├── parameterPanels/        # AI 功能参数面板
        │   │   │   ├── BarcodeDetectionPanel.tsx    # 条码检测参数
        │   │   │   ├── OcrDetectionPanel.tsx        # OCR 检测参数
        │   │   │   └── AiRestorePanel.tsx           # AI 修复参数
        │   │   ├── AdminLoginModal.tsx     # 管理员登录模态框
        │   │   ├── AdminProtectedRoute.tsx # 管理员路由保护组件
        │   │   ├── BaseInfoPanel.tsx       # 基础信息面板
        │   │   ├── ExampleImagesModal.tsx  # 示例图片选择模态框
        │   │   ├── FunctionTree.tsx        # 功能选择树
        │   │   ├── ImageInfoPanel.tsx      # 图像信息面板
        │   │   ├── MenuBar.tsx             # 顶部菜单栏
        │   │   ├── ParameterPanel.tsx      # 参数配置容器
        │   │   ├── PanelInfo.tsx           # 动态信息面板
        │   │   └── StatusBar.tsx           # 底部状态栏
        │   └── layouts/
        │       ├── AdminLayout.tsx         # 管理后台布局
        │       └── DashboardLayout.tsx     # 主布局组件
        ├── 📄 页面层
        │   └── pages/
        │       ├── AdminDashboardPage.tsx  # 管理概览页面
        │       ├── AdminImagesPage.tsx     # 示例图片管理页面
        │       ├── AdminModelsPage.tsx     # 模型管理页面
        │       └── DashboardPage.tsx       # 主操作界面
        ├── 🔄 状态管理
        │   └── contexts/
        │       ├── AdminContext.tsx                 # 管理员状态管理
        │       ├── ImageWorkspaceContext.tsx       # 图像工作区状态
        │       ├── FunctionPanelContext.tsx        # 功能面板状态
        │       ├── BarcodeDetectionContext.tsx     # 条码检测状态
        │       ├── OcrDetectionContext.tsx         # OCR 检测状态
        │       └── AiRestoreDetectionContext.tsx   # AI 修复状态
        ├── 🌐 服务层
        │   └── services/
        │       └── api.ts                  # API 请求封装
        ├── 🛠️ 工具函数
        │   └── utils/
        │       └── imageUtils.ts           # 图像处理工具
        ├── 📊 数据层
        │   └── data/
        │       └── exampleImagesData.ts    # 示例图片数据
        ├── 🎨 资源文件
        │   └── assets/                     # 图标和示例图片
        ├── 🛣️ 路由配置
        │   └── router.tsx                  # 应用路由定义
        ├── 📱 应用入口
        │   ├── main.tsx                    # 应用启动入口
        │   ├── App.tsx                     # 根组件
        │   ├── App.css                     # 应用样式
        │   ├── index.css                   # 全局样式
        │   └── vite-env.d.ts              # Vite 类型定义
```

## ✨ 核心功能

### 🖼️ 智能图像工作区

#### 图像管理
- **多格式支持**: JPG, PNG, BMP, WEBP, AVIF 等主流图像格式
- **灵活加载**: 支持文件选择器、拖拽上传、文件夹批量导入
- **图像导航**: 左右箭头切换、键盘快捷键、缩略图预览
- **历史记录**: 自动保存最近打开的图像列表
- **示例图片**: 内置示例图片库，按AI功能分类提供测试图像

#### 交互体验
- **缩放平移**: 基于 `react-zoom-pan-pinch` 的流畅图像操作
- **ROI 选择**: 智能区域选择，支持矩形框选和精确坐标
- **坐标显示**: 实时鼠标坐标跟踪和像素级定位
- **结果导出**: 支持将处理结果保存为 PNG 格式

### 🎛️ 响应式布局系统

#### 双布局架构
- **主操作界面**: 三栏式布局，专注于AI视觉任务处理
  - **左侧功能树**: 分层级的 AI 功能选择界面
  - **中央图像区**: 主要的图像显示和交互区域
  - **右侧参数区**: 动态加载的功能参数配置面板
- **管理后台界面**: 专业的管理系统布局
  - **侧边导航**: 管理功能模块导航
  - **主内容区**: 数据表格和操作界面
  - **统计面板**: 实时数据统计和系统状态

#### 界面组件
- **顶部菜单栏**: 文件操作、设置和帮助功能
- **底部状态栏**: 实时状态信息和进度显示
- **信息面板**: 图像属性和处理结果详情
- **可调整大小**: 基于 `allotment` 的灵活布局调整

### 🔍 AI 视觉功能

#### 条码检测 (`BarcodeDetectionPanel`)
- **模型管理**: 系统内置模型 + 自定义模型上传 (`.pt` 格式)
- **预处理选项**: 全图缩放 / ROI 区域检测
- **参数调节**: 置信度阈值 (0.0-1.0) 精确控制
- **批量处理**: 支持多图像批量检测
- **结果可视化**: SVG 覆盖层显示边界框、类别和置信度

#### OCR 文字识别 (`OcrDetectionPanel`)
- **多场景支持**: 通用文字、车牌识别、身份证等
- **模型选择**: 检测模型 + 识别模型组合
- **结果展示**: 文字内容、位置坐标、置信度
- **格式导出**: 支持文本和结构化数据导出

#### AI 图像修复 (`AiRestorePanel`)
- **修复模式**: 图像增强、去噪、超分辨率
- **显示选项**: 仅复原图 / 原图对比 / 合成视图
- **智能处理**: 自动检测开关，切换图片时自动修复
- **批处理**: 可配置间隔时间的批量修复
- **模型选择**: 支持多版本AI复原模型切换

### 🔐 管理员系统

#### 身份认证
- **安全登录**: 基于会话的管理员身份验证
- **权限控制**: 多层级权限验证机制
- **状态管理**: 实时管理员状态检查和自动登出
- **路由保护**: 管理页面访问权限控制

#### 示例图片管理 (`AdminImagesPage`)
- **分类管理**: 按AI功能分类（条码、OCR、AI复原）
- **批量上传**: 支持多文件拖拽上传和进度显示
- **预览功能**: 基于Ant Design Image组件的图片预览
- **批量操作**: 多选删除、全选/反选功能
- **格式支持**: JPG、PNG、BMP、WEBP、AVIF等格式
- **实时刷新**: 智能数据更新和状态同步

#### 模型管理 (`AdminModelsPage`)
- **模型分类**: 系统模型和用户模型分类管理
- **OCR模型配对**: 检测+识别模型集合管理
- **批量操作**: 模型上传、删除、编辑功能
- **统计信息**: 实时模型数量统计和分类展示
- **版本管理**: 模型版本控制和描述信息
- **文件验证**: 模型文件格式和完整性验证

#### 管理概览 (`AdminDashboardPage`)
- **数据统计**: 系统模型、用户模型、示例图片统计
- **快速操作**: 管理功能快速入口和导航
- **系统状态**: 实时系统运行状态监控
- **使用指南**: 内置功能说明和操作指导

### 🔄 状态管理架构

#### Context 状态管理
- **`AdminContext`**: 管理员身份认证和权限状态
- **`ImageWorkspaceContext`**: 图像工作区全局状态
- **`FunctionPanelContext`**: 功能面板选择状态
- **`BarcodeDetectionContext`**: 条码检测专用状态
- **`OcrDetectionContext`**: OCR 检测专用状态
- **`AiRestoreDetectionContext`**: AI 修复专用状态

#### 数据流设计
- **单向数据流**: 确保状态变更的可预测性
- **组件解耦**: 通过 Context 实现组件间通信
- **状态持久化**: 关键设置的本地存储

### 🌐 API 服务层

#### 后端集成 (`api.ts`)
**AI 功能 API**:
- **模型管理**: `getGroupedVisionModels()` - 获取分组模型列表
- **条码检测**: `detectBarcode()` - 条码检测 API
- **OCR 识别**: `detectOcrPaddle()` - OCR 文字识别 API
- **图像修复**: `restoreImage()` - AI 图像修复 API
- **OCR任务**: `getOcrTasks()` - 获取可用OCR任务列表

**管理员 API**:
- **身份认证**: `adminLogin()`, `adminLogout()`, `adminCheckStatus()`
- **模型管理**: `uploadModel()`, `deleteModels()`, `updateModel()`
- **图片管理**: `uploadExampleImage()`, `deleteExampleImages()`, `getExampleImages()`

#### 错误处理
- **统一错误处理**: 全局错误拦截和用户友好提示
- **重试机制**: 网络异常自动重试
- **状态反馈**: 加载状态和进度指示器

## 🚀 技术栈

### 🏗️ 核心框架
- **React 18.2.0** - 现代化的用户界面库
- **TypeScript 5.7.2** - 类型安全的 JavaScript 超集
- **Vite 6.3.1** - 快速的前端构建工具

### 🎨 UI 组件库
- **Ant Design 5.24.8** - 企业级 UI 设计语言
- **Allotment 1.20.3** - 可调整大小的面板布局
- **React Zoom Pan Pinch 3.7.0** - 图像缩放平移交互

### 🛣️ 路由与状态
- **React Router DOM 7.5.3** - 声明式路由管理
- **React Context API** - 内置状态管理
- **TanStack React Query 5.74.11** - 服务端状态管理
- **路由保护** - 基于权限的路由访问控制

### 🌐 网络请求
- **Axios 1.9.0** - HTTP 客户端库
- **自定义 API 封装** - 统一的后端接口调用

### 🖥️ 桌面应用
- **Electron 36.2.1** - 跨平台桌面应用框架
- **Electron Builder 26.0.12** - 应用打包和分发

### 🔧 开发工具
- **ESLint 9.22.0** - 代码质量检查
- **TypeScript ESLint 8.26.1** - TypeScript 专用规则
- **Concurrently 9.1.2** - 并行脚本执行

## 🛠️ 开发指南

### 📦 环境配置

#### 1. 安装依赖
```bash
# 进入前端目录
cd Frontend

# 安装 npm 依赖
npm install
```

#### 2. 环境变量配置
```bash
# 开发环境 (.env.development)
VITE_API_BASE_URL=http://127.0.0.1:8000/api

# 生产环境 (.env.production)
VITE_API_BASE_URL=/api

# 服务器环境 (.env.server)
VITE_API_BASE_URL=http://*************:8000/api
```

### 🚀 启动开发

#### Web 开发模式
```bash
# 启动开发服务器
npm run dev

# 访问地址: http://localhost:5173
# 管理后台: http://localhost:5173/admin (需要管理员权限)
```

#### Electron 桌面应用开发
```bash
# 启动 Electron 开发模式
npm run electron:dev

# 同时启动 Web 服务和 Electron 窗口
```

### 📦 构建部署

#### Web 应用构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

#### Electron 应用打包
```bash
# 打包 Windows 应用
npm run electron:build:win

# 打包 macOS 应用
npm run electron:build:mac

# 打包 Linux 应用
npm run electron:build:linux
```

### 🔍 代码质量

#### ESLint 检查
```bash
# 运行代码检查
npm run lint

# 自动修复可修复的问题
npm run lint -- --fix
```

#### TypeScript 检查
```bash
# 类型检查
npx tsc --noEmit

# 构建时类型检查
npm run build
```

## 📋 开发规范

### 🏗️ 项目架构原则
- **组件化设计** - 可复用的 UI 组件
- **状态管理** - 使用 Context API 管理应用状态
- **类型安全** - 严格的 TypeScript 类型定义
- **模块化** - 清晰的文件组织和依赖关系

### 📝 命名规范
- **组件文件** - PascalCase (如 `ImageDisplay.tsx`)
- **Hook 函数** - camelCase + use 前缀 (如 `useImageUpload.ts`)
- **工具函数** - camelCase (如 `imageUtils.ts`)
- **常量** - UPPER_SNAKE_CASE (如 `API_BASE_URL`)

### 🎯 组件设计原则
- **单一职责** - 每个组件只负责一个功能
- **Props 接口** - 明确定义组件的输入输出
- **错误边界** - 合理的错误处理和用户反馈
- **性能优化** - 使用 React.memo 和 useMemo 优化渲染

### 🔄 状态管理模式
- **Context 分离** - 按功能模块划分不同的 Context
- **状态不可变** - 使用不可变数据更新模式
- **副作用管理** - 使用 useEffect 处理副作用
- **本地存储** - 关键设置的持久化存储

### 🔧 技术实现细节

#### 管理员权限实现
- **AdminContext**: 全局管理员状态管理
- **AdminProtectedRoute**: 路由级别的权限保护
- **会话持久化**: 基于后端Django会话的状态同步
- **自动登出**: 会话过期自动处理

#### 文件上传机制
- **批量上传**: 支持多文件选择和拖拽上传
- **进度跟踪**: 实时上传进度和状态反馈
- **格式验证**: 前端文件格式和大小验证
- **错误处理**: 完善的错误提示和重试机制

#### 数据同步策略
- **React Query**: 服务端状态缓存和同步
- **乐观更新**: 提升用户体验的乐观更新策略
- **错误恢复**: 网络错误自动重试和状态恢复
- **实时刷新**: 关键操作后的数据自动刷新

### 🎯 最佳实践

#### 组件设计
- **单一职责**: 每个组件专注单一功能
- **Props 类型**: 严格的TypeScript类型定义
- **错误边界**: 组件级别的错误处理
- **性能优化**: React.memo和useMemo的合理使用

#### API 调用
- **统一封装**: 所有API调用通过api.ts统一管理
- **错误处理**: 统一的错误处理和用户提示
- **类型安全**: 完整的请求和响应类型定义
- **请求拦截**: 统一的请求头和认证处理

## 🔐 管理员系统使用指南

### 访问管理后台

1. **访问地址**:
   - 开发环境: `http://localhost:5173/admin`
   - 生产环境: `http://your-domain/admin`

2. **登录认证**:
   - 管理员密码: `mindeo`
   - 基于会话的身份验证
   - 自动权限验证和路由保护

### 管理功能模块

#### 📊 管理概览页面 (`/admin`)
- **系统统计**: 实时显示模型和图片数量统计
- **快速操作**: 提供管理功能的快速入口
- **使用指南**: 内置功能说明和操作指导

#### 🗃️ 模型管理页面 (`/admin/models`)
- **模型列表**: 分类显示系统模型和用户模型
- **模型上传**: 支持多种AI模型格式上传
- **批量操作**: 模型删除、编辑、分类管理
- **OCR配对**: 检测+识别模型集合管理
- **统计信息**: 实时模型数量和分类统计

#### 🖼️ 示例图片管理页面 (`/admin/images`)
- **分类浏览**: 按AI功能分类查看图片
- **批量上传**: 支持多文件拖拽上传
- **预览功能**: 基于Ant Design的图片预览
- **批量删除**: 多选删除和全选操作
- **进度显示**: 上传进度和状态反馈

### 操作流程示例

#### 上传示例图片
1. 进入示例图片管理页面
2. 选择图片分类（条码/OCR/AI复原）
3. 点击"上传图片"按钮
4. 拖拽或选择图片文件
5. 确认上传，查看进度状态

#### 管理AI模型
1. 进入模型管理页面
2. 查看现有模型列表和统计
3. 点击"上传模型"添加新模型
4. 填写模型信息和配置
5. 选择模型类别（系统/用户）
6. 确认上传并验证

### 权限和安全

- **会话管理**: 基于Django会话的安全认证
- **路由保护**: 自动权限验证和访问控制
- **操作日志**: 重要操作的日志记录
- **错误处理**: 友好的错误提示和异常处理

## 📚 相关文档

- **项目主文档**: `../README.md`
- **后端文档**: `../Backend_Django/README.md`
- **Docker 部署**: `../docker/README.md`
- **管理员系统**: `../docs/admin_system.md`

---
