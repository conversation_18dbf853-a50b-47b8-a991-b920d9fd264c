numpy<2.0.0,>=1.23.0
matplotlib>=3.3.0
opencv-python>=4.6.0
pillow>=7.1.2
pyyaml>=5.3.1
requests>=2.23.0
scipy>=1.4.1
torch>=1.8.0
torchvision>=0.9.0
tqdm>=4.64.0
psutil
py-cpuinfo
pandas>=1.1.4
seaborn>=0.11.0
ultralytics-thop>=2.0.0

[:sys_platform == "win32"]
torch!=2.4.0,>=1.8.0

[dev]
ipython
pytest
pytest-cov
coverage[toml]
mkdocs>=1.6.0
mkdocs-material>=9.5.9
mkdocstrings[python]
mkdocs-jupyter
mkdocs-redirects
mkdocs-ultralytics-plugin>=0.1.8
mkdocs-macros-plugin>=1.0.5

[explorer]
lancedb
duckdb<=0.9.2
streamlit

[export]
onnx>=1.12.0
openvino>=2024.0.0
tensorflow>=2.0.0
tensorflowjs>=3.9.0
keras

[export:platform_machine == "aarch64"]
flatbuffers<100,>=23.5.26
numpy==1.23.5
h5py!=3.11.0

[export:platform_machine == "aarch64" and python_version >= "3.9"]
tensorstore>=0.1.63

[export:platform_system != "Windows" and python_version <= "3.11"]
coremltools>=7.0

[extra]
hub-sdk>=0.0.12
ipython
albumentations>=1.4.6
pycocotools>=2.0.7

[logging]
comet
tensorboard>=2.13.0
dvclive>=2.12.0
