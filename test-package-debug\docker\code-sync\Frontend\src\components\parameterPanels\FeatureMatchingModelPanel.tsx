import React, { useState, useEffect, useContext } from 'react';
import { <PERSON><PERSON>, Card, Col, Divider, InputNumber, Row, Select, Space, Upload, Alert, App, Slider, Tooltip } from 'antd';
import { InboxOutlined, PictureOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { useImageWorkspace } from '../../contexts/ImageWorkspaceContext';
import { RcFile } from 'antd/es/upload';
import { featureMatchingModelApi, getGroupedVisionModels, VisionModel, CustomApiError, GroupedVisionModelsApiResponse } from '../../services/api';
import ExampleImagesModal from '../ExampleImagesModal';
import { FeatureMatchingModelContext } from '../../contexts/FeatureMatchingModelContext';

const { Dragger } = Upload;

// --- 类型定义 ---

/**
 * @interface FeatureMatchingModelResult
 * @description 定义了基于模型的特征匹配功能的状态。
 *
 * @property {FeatureMatchingModelResult | null} matchingResult - 存储后端返回的匹配结果。
 * @property {boolean} isLoading - 标识匹配过程是否正在进行中。
 * @property {(result: FeatureMatchingModelResult | null) => void} setMatchingResult - 更新匹配结果的函数。
 * @property {(loading: boolean) => void} setIsLoading - 设置加载状态的函数。
 */

interface ParameterSettingsProps {
    handleRunMatching: () => void;
    isLoading: boolean;
    templateImage: File | null;
    targetImage: File | null;
    models: VisionModel[];
    selectedModel: number | null;
    setSelectedModel: (id: number) => void;
    keypointsCount: number;
    setKeypointsCount: (value: number) => void;
    nmsGridSize: number;
    setNmsGridSize: (value: number) => void;
    matchRatioThreshold: number;
    setMatchRatioThreshold: (value: number) => void;
    minMatchCount: number;
    setMinMatchCount: (value: number) => void;
    ransacThreshold: number;
    setRansacThreshold: (value: number) => void;
}

// --- 内部组件 ---

const ParameterSettings: React.FC<ParameterSettingsProps> = ({
    handleRunMatching,
    isLoading,
    templateImage,
    targetImage,
    models,
    selectedModel,
    setSelectedModel,
    keypointsCount,
    setKeypointsCount,
    nmsGridSize,
    setNmsGridSize,
    matchRatioThreshold,
    setMatchRatioThreshold,
    minMatchCount,
    setMinMatchCount,
    ransacThreshold,
    setRansacThreshold,
}) => {
    return (
        <Card title="2. 设置参数并执行" size="small">
            <Row gutter={[16, 8]} align="middle">
                <Col span={8}>算法模型:</Col>
                <Col span={16}>
                    <Select
                        value={selectedModel}
                        onChange={setSelectedModel}
                        style={{ width: '100%' }}
                        placeholder="请选择一个模型"
                        loading={!models.length}
                    >
                        {models.map(model => (
                            <Select.Option key={model.id} value={model.id}>
                                {model.name} (v{model.version})
                            </Select.Option>
                        ))}
                    </Select>
                </Col>

                <Col span={8}>
                    <Tooltip title="每张图提取的关键点数量。run.py默认使用100，可适当增加以提高精度">
                        关键点数量:
                    </Tooltip>
                </Col>
                <Col span={10}>
                    <Slider min={50} max={1000} step={50} value={keypointsCount} onChange={setKeypointsCount} />
                </Col>
                <Col span={6}>
                    <InputNumber min={50} max={1000} step={50} value={keypointsCount} onChange={v => setKeypointsCount(v ?? 100)} />
                </Col>

                <Col span={8}>
                    <Tooltip title="NMS网格划分大小，用于抑制相邻的重复特征点。建议值：1,2,4,8,16">
                        NMS网格大小:
                    </Tooltip>
                </Col>
                 <Col span={10}>
                    <Slider min={1} max={8} step={1} value={nmsGridSize} onChange={setNmsGridSize} marks={{1:'1',2:'2',4:'4',8:'8'}} />
                </Col>
                <Col span={6}>
                    <InputNumber min={1} max={8} step={1} value={nmsGridSize} onChange={v => setNmsGridSize(v ?? 4)} />
                </Col>

                <Col span={8}>
                    <Tooltip title="Lowe's ratio test的阈值，用于过滤模糊匹配">
                        匹配率阈值:
                    </Tooltip>
                </Col>
                 <Col span={10}>
                    <Slider min={0.1} max={1.0} step={0.05} value={matchRatioThreshold} onChange={setMatchRatioThreshold} />
                </Col>
                <Col span={6}>
                    <InputNumber min={0.1} max={1.0} step={0.05} value={matchRatioThreshold} onChange={v => setMatchRatioThreshold(v ?? 0.8)} />
                </Col>

                <Col span={8}>
                    <Tooltip title="RANSAC算法要求的最少内点数量">
                        最少匹配点:
                    </Tooltip>
                </Col>
                 <Col span={10}>
                    <Slider min={1} max={50} step={1} value={minMatchCount} onChange={setMinMatchCount} />
                </Col>
                <Col span={6}>
                    <InputNumber min={1} max={50} step={1} value={minMatchCount} onChange={v => setMinMatchCount(v ?? 4)} />
                </Col>

                <Col span={8}>
                    <Tooltip title="RANSAC重投影误差阈值（像素），run.py中使用5.0">
                        RANSAC阈值:
                    </Tooltip>
                </Col>
                 <Col span={10}>
                    <Slider min={1.0} max={10.0} step={0.5} value={ransacThreshold} onChange={setRansacThreshold} />
                </Col>
                <Col span={6}>
                    <InputNumber min={1.0} max={10.0} step={0.5} value={ransacThreshold} onChange={v => setRansacThreshold(v ?? 5.0)} />
                </Col>
            </Row>
            <Divider style={{ margin: '12px 0' }} />
            <Button
                type="primary"
                onClick={handleRunMatching}
                loading={isLoading}
                style={{ width: '100%' }}
                disabled={!templateImage || !targetImage || !selectedModel}
            >
                开始匹配
            </Button>
        </Card>
    );
};

// --- 主面板组件 ---

const FeatureMatchingModelPanel: React.FC = () => {
    const { message } = App.useApp();
    const { isLoading, setIsLoading, setMatchingResult } = useContext(FeatureMatchingModelContext);
    const {
        croppedImageDataUrl,
        selectedRoi,
        loadImage,
        setDetectionResults,
        setFeatureMatches, // 直接在顶层获取
        imageList,
        currentImageIndex
    } = useImageWorkspace();

    const [templateImage, setTemplateImage] = useState<File | null>(null);
    const [templatePreview, setTemplatePreview] = useState<string | null>(null);
    const [targetImage, setTargetImage] = useState<File | null>(null);
    const [targetPreview, setTargetPreview] = useState<string | null>(null);
    const [roi, setRoi] = useState<{ x: number; y: number; width: number; height: number; } | null>(null);

    const [models, setModels] = useState<VisionModel[]>([]);
    const [selectedModel, setSelectedModel] = useState<number | null>(null);
    
    // 参数状态 - 调整为与run.py一致的默认值
    const [keypointsCount, setKeypointsCount] = useState(100); // run.py默认值：100
    const [nmsGridSize, setNmsGridSize] = useState(4); // run.py默认值：4
    const [matchRatioThreshold, setMatchRatioThreshold] = useState(0.8); // run.py默认值：0.8
    const [minMatchCount, setMinMatchCount] = useState(4); // run.py默认值：4 (min_inliers)
    const [ransacThreshold, setRansacThreshold] = useState(5.0); // run.py实际使用：5.0

    const [isModalVisible, setIsModalVisible] = useState(false);
    const [modalCaller, setModalCaller] = useState<'template' | 'target' | null>(null);

    const {
        data: fetchedModelsData,
    } = useQuery<GroupedVisionModelsApiResponse, CustomApiError>({
        queryKey: ['featureMatchingModels'],
        queryFn: () => getGroupedVisionModels('all'),
    });

    useEffect(() => {
        if (fetchedModelsData) {
            const matchingModels = fetchedModelsData['feature_matching'] || [];
            setModels(matchingModels);
            if (matchingModels.length > 0 && !matchingModels.some((m: VisionModel) => m.id === selectedModel)) {
                setSelectedModel(matchingModels[0].id);
            }
        }
    }, [fetchedModelsData, selectedModel]);


    useEffect(() => {
        if (croppedImageDataUrl && selectedRoi && imageList[currentImageIndex]) {
            setTemplatePreview(croppedImageDataUrl);
            const originalImageFile = imageList[currentImageIndex];
            setTemplateImage(originalImageFile);
            setRoi(selectedRoi);
            message.success('已从主视图截取模板');
        }
    }, [croppedImageDataUrl, selectedRoi, imageList, currentImageIndex, message]);

    const handleImageUpload = (file: RcFile, type: 'template' | 'target') => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            if (type === 'template') {
                setTemplateImage(file);
                setTemplatePreview(imageUrl);
                setRoi(null);
            } else {
                setTargetImage(file);
                setTargetPreview(imageUrl);
            }
        };
        reader.readAsDataURL(file);
        return false;
    };

    const handleOpenModal = (caller: 'template' | 'target') => {
        setModalCaller(caller);
        setIsModalVisible(true);
    };

    const handleImageFromModal = (imageFile: File) => {
        if (modalCaller) {
            handleImageUpload(imageFile as RcFile, modalCaller);
        }
    };

    const handleRunMatching = async () => {
        if (!templateImage || !targetImage || !selectedModel) {
            message.error('请确保已提供模板图片、目标图片并已选择模型！');
            return;
        }

        setIsLoading(true);
        setMatchingResult(null);
        const key = 'matching_model';
        message.loading({ content: '正在执行模型匹配...', key });

        try {
            const result = await featureMatchingModelApi({
                template_image: templateImage,
                target_image: targetImage,
                model_id: selectedModel,
                template_roi: roi ?? undefined,
                keypoints_count: keypointsCount,
                nms_grid_size: nmsGridSize,
                match_ratio_threshold: matchRatioThreshold,
                min_match_count: minMatchCount,
                ransac_threshold: ransacThreshold, // 添加RANSAC阈值参数
            });
            
            setMatchingResult(result);

            if (result.status === 'success' && result.data.homography) {
                message.success({ content: '模型匹配成功! 正在主视图中展示结果...', key });
                await loadImage(targetImage);
                await new Promise(resolve => setTimeout(resolve, 200));

                const { matches, homography } = result.data;
                
                // 1. 设置匹配线 (需要转换数据结构)
                const featureMatchesForDisplay = matches.map(match => ({
                    point1: match.template_point,
                    point2: match.target_point,
                    distance: match.distance,
                }));
                setFeatureMatches(featureMatchesForDisplay);

                // 2. 计算并设置变换后的包围盒
                if (roi && homography) {
                    const { x, y, width, height } = roi;
                    const corners = [
                        [x, y],
                        [x + width, y],
                        [x + width, y + height],
                        [x, y + height]
                    ];

                    const transformedCorners = corners.map(pt => {
                        const [px, py] = pt;
                        const pz = homography[2][0] * px + homography[2][1] * py + homography[2][2];
                        const tx = (homography[0][0] * px + homography[0][1] * py + homography[0][2]) / pz;
                        const ty = (homography[1][0] * px + homography[1][1] * py + homography[1][2]) / pz;
                        return [tx, ty];
                    });

                    const detection = {
                        box: [
                            Math.min(...transformedCorners.map(p => p[0])),
                            Math.min(...transformedCorners.map(p => p[1])),
                            Math.max(...transformedCorners.map(p => p[0])),
                            Math.max(...transformedCorners.map(p => p[1])),
                        ] as [number, number, number, number],
                        polygon: transformedCorners as [number, number][],
                        label: `模型匹配区域 (${result.data.match_count}点)`,
                        type: 'general_object' as const,
                    };
                    setDetectionResults([detection]);
                }

            } else {
                message.error({ content: `匹配失败: ${result.message}`, key });
                setFeatureMatches(null);
                setDetectionResults(null);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            setMatchingResult({
                status: 'error',
                message: errorMessage,
                data: {
                    match_count: 0,
                    matches: [],
                    template_keypoints_count: 0,
                    target_keypoints_count: 0,
                    processing_time: 0,
                }
            });
            message.error({ content: `匹配异常: ${errorMessage}`, key });
        } finally {
            setIsLoading(false);
        }
    };

    const renderUploadCard = (title: string, type: 'template' | 'target') => {
        const preview = type === 'template' ? templatePreview : targetPreview;
        const fileName = type === 'template' ? templateImage?.name : targetImage?.name;

        return (
            <Card title={title} size="small">
                <Dragger
                    name={type}
                    multiple={false}
                    beforeUpload={(file) => handleImageUpload(file, type)}
                    showUploadList={false}
                    accept="image/*"
                    height={preview ? undefined : 150}
                    style={{ marginBottom: '10px', padding: '10px' }}
                >
                    {preview ? (
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ height: '120px', position: 'relative', marginBottom: '8px' }}>
                                <img
                                    src={preview}
                                    alt={`${type} preview`}
                                    style={{
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        maxWidth: '100%',
                                        maxHeight: '100%',
                                        objectFit: 'contain'
                                    }}
                                />
                            </div>
                            <Alert message={fileName} type="success" style={{ fontSize: '12px', wordBreak: 'break-all' }} />
                            <p className="ant-upload-text" style={{ fontSize: '12px', color: '#888', marginTop: '8px' }}>
                                可再次拖拽或点击此区域替换图片
                            </p>
                        </div>
                    ) : (
                        <div style={{ transform: 'translateY(-10px)' }}>
                            <p className="ant-upload-drag-icon"><InboxOutlined /></p>
                            <p className="ant-upload-text">点击或拖拽文件到此区域</p>
                        </div>
                    )}
                </Dragger>
                <Button icon={<PictureOutlined />} style={{ width: '100%' }} onClick={() => handleOpenModal(type)}>
                    从示例库选择
                </Button>
            </Card>
        );
    };

    return (
        <>
            <div style={{ padding: '16px' }}>
                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                    <Card title="1. 提供图片" size="small">
                        <Alert
                            message="操作提示"
                            description="您可以通过主菜单栏的 '编辑' -> '框选工具' 在主图上绘制矩形, 该区域将自动作为模板图片载入。也可以选择传入图片"
                            type="info"
                            showIcon
                            closable
                            style={{ marginBottom: '16px' }}
                        />
                        <Row gutter={16}>
                            <Col span={12}>{renderUploadCard('模板 (Template)', 'template')}</Col>
                            <Col span={12}>{renderUploadCard('目标 (Target)', 'target')}</Col>
                        </Row>
                    </Card>
                    <ParameterSettings
                        handleRunMatching={handleRunMatching}
                        isLoading={isLoading}
                        templateImage={templateImage}
                        targetImage={targetImage}
                        models={models}
                        selectedModel={selectedModel}
                        setSelectedModel={setSelectedModel}
                        keypointsCount={keypointsCount}
                        setKeypointsCount={setKeypointsCount}
                        nmsGridSize={nmsGridSize}
                        setNmsGridSize={setNmsGridSize}
                        matchRatioThreshold={matchRatioThreshold}
                        setMatchRatioThreshold={setMatchRatioThreshold}
                        minMatchCount={minMatchCount}
                        setMinMatchCount={setMinMatchCount}
                        ransacThreshold={ransacThreshold}
                        setRansacThreshold={setRansacThreshold}
                    />
                </Space>
            </div>
            <ExampleImagesModal
                visible={isModalVisible}
                onCancel={() => {
                    setIsModalVisible(false);
                    setModalCaller(null);
                }}
                selectionMode="single"
                onSelectImage={handleImageFromModal}
            />
        </>
    );
};

export default FeatureMatchingModelPanel;