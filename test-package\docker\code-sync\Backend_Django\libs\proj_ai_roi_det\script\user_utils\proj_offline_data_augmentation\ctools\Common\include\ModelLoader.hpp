#ifndef _MODEL_LOADER_H
#define _MODEL_LOADER_H

//-----------------------------------------------------------------------------
//  Includes

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

#include "Log.hpp"
#include "AIEngineCommon.h"

//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

/**
 * @brief    
 *           模型加载器基类
 *           
 * @date     2024-02-27 Created by Huang<PERSON>
 */
class BaseModelLoader {
public:
    typedef enum {
        AIENGINE_INFERENCE_FRAMEWORK_SSC377 = 0, // SSC377推理框架
        AIENGINE_INFERENCE_FRAMEWORK_MNN, // MNN推理框架
        AIENGINE_INFERENCE_FRAMEWORK_PADDLE_LITE, // Paddle-Lite推理框架
    }inference_framework_t; // 推理框架

    typedef enum {
        AIENGINE_BACKEND_CPU = 0,
        AIENGINE_BACKEND_GPU,
        AIENGINE_BACKEND_IPU,
        AIENGINE_BACKEND_NN,
    }backend_t; // 模型推理后端数据枚举

    typedef enum {
        AIENGINE_PRECISION_LOW = 0, // 低精度推理，推理速度较快，但推理效果会有所下降。使用此精度时，请注意评估推理效果是否能满足预期
        AIENGINE_PRECISION_LOW_BF16, // 低精度推理（BF16优化），推理速度较快，但推理效果会有所下降。使用此精度时，请注意评估推理效果是否能满足预期
        AIENGINE_PRECISION_NORMAL, // 普通精度推理
        AIENGINE_PRECISION_HIGH, // 高精度推理
    }precision_t; // 模型推理精度

    typedef enum {
        AIENGINE_MEMORY_FORMAT_NCHW = 0, // NCHW(contiguous format)。以RGB为例，排列格式为：[1]RRR [2]GGG [3]BBB
        AIENGINE_MEMORY_FORMAT_NC4HW4, // NC4HW4。以RGBA为例，排列格式为：[1]RGBA [2]RGBA [3]RGBA
        AIENGINE_MEMORY_FORMAT_NHWC, // NHWC(channels last)。以RGB为例，排列格式为：[1]RGB [2]RGB [3]RGB
    }memory_format_t; // 张量内存存储格式

    typedef struct {
        std::vector<void *> input_tensor_index_list; // 输入张量索引列表
        std::vector<void *> output_tensor_index_list; // 输出张量索引列表
        std::vector<unsigned char> data; // 模型数据存储地址
        std::string name; // 模型文件名
        std::string version; // 模型版本号
        int data_size; // 模型大小
        inference_framework_t inference_framework; // 模型推理框架
        task_type_t task_type; // 模型适用任务类型
        postprocess_method_t postprocess_method; // 后处理方法
        backend_t backend; // 模型使用的推理后端
        precision_t precision; // 模型推理精度
        memory_format_t memory_format; // 内存存储格式
        int num_threads; // 模型推理使用的线程数（仅使用CPU推理时有效）
        std::vector<const char *> *ocr_rec_dict; // OCR_Rec模型使用的字典（仅在OCR_Rec模型中使用）
        std::unordered_map<const char *, float> params; // 自定义参数
    }ModelData; // 模型数据结构体

    typedef std::unordered_map<std::string, ModelData> model_data_map; // 用于模型标签到模型数据映射的数据类型

    /**
     * @brief    
     *           检查模型是否适用目标任务
     *           
     * @param    model_tag:     模型标签（搜索模型的关键字）
     * @param    task_type:     目标任务类型
     *           
     * @retval   true: 模型适用于目标任务。
     * @retval   false: 模型与目标任务不适配。或没有找到指定模型。
     *           
     * @date     2024-02-27 Created by HuangJP
     */
    static bool Check_Model_Task_Type(std::string model_tag, task_type_t task_type)
    {
        // 判断模型是否不存在
        if (Check_Model_Exist(model_tag) == false)
        {
            return false;
        }

        bool is_match = model_data[model_tag].task_type == task_type;
        if (is_match == false)
        {
            LOGE("The model is not suitable for the target task.");
            LOGD("model_tag: %s, model task type: %d, target task: %d.", model_tag.c_str(), model_data[model_tag].task_type, task_type);
        }

        return is_match;
    }

    /**
     * @brief    
     *           获取模型数据
     *           
     * @param    model_tag:     模型标签（搜索模型的关键字）
     *           
     * @retval   指向模型数据的指针。若找不到对应模型，则返回空指针。
     *           
     * @date     2024-02-27 Created by HuangJP
     */
    static const ModelData *Get_Model_Data(std::string model_tag)
    {
        // 判断模型是否不存在
        if (Check_Model_Exist(model_tag) == false)
        {
            LOGE("Unable to get model data. Please check whether the `model_tag` is correct or not.");
            LOGD("model_tag: %s.", model_tag.c_str());
            return nullptr;
        }

        return (const ModelData *)&model_data[model_tag];
    }

    /**
     * @brief    
     *           检查模型是否存在
     *           
     * @param    model_tag:     模型标签（搜索模型的关键字）
     *           
     * @retval   true: 模型存在，fasle: 模型不存在
     *           
     * @date     2024-03-06 Created by HuangJP
     */
    static inline bool Check_Model_Exist(std::string model_tag)
    {
        bool is_model_exist = model_data.find(model_tag) != model_data.end();

        if (is_model_exist == false)
        {
            LOGE("Model does not exist. Please check whether the `model_tag` is correct or not.");
            LOGD("model_tag: %s.", model_tag.c_str());
        }

        return is_model_exist;
    }

    /**
     * @brief    
     *           获取参数
     *           
     * @param    model:             模型数据
     * @param    name:              需要获取的参数名称
     * @param    default_value:     获取失败时的默认返回值
     *           
     * @retval   转换后的参数值
     *           
     * @date     2024-10-28 Created by HuangJP
     */
    template<typename target_type_t>
    static inline target_type_t Get_Parameter(const ModelData *model, const char *name, target_type_t default_value)
    {
        auto it = model->params.find(name);
        if (it == model->params.end())
        {
            return default_value;
        }

        return (target_type_t)it->second;
    }

protected:
    static model_data_map model_data; // 模型数据表

    /**
     * @brief    
     *           BaseModelLoader析构函数
     *           
     * @param    
     *           
     * @retval   
     *           
     * @date     2024-08-06 Created by HuangJP
     */
    virtual ~BaseModelLoader()
    {

    }
    
    /**
     * @brief    
     *           BaseModelLoader构造函数
     *           
     * @param    release:   是否为发布版本
     *           
     * @date     2024-02-27 Created by HuangJP
     */
    BaseModelLoader()
    {

    }
};

#endif
//-----------------------------------------------------------------------------
//  End of file