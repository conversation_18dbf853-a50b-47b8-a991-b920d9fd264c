# Ultralytics YOLO 🚀, AGPL-3.0 license
# Package-seg dataset by Ultralytics
# Documentation: https://docs.ultralytics.com/datasets/segment/package-seg/
# Example usage: yolo train data=package-seg.yaml
# parent
# ├── ultralytics
# └── datasets
#     └── package-seg  ← downloads here (102 MB)

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: ../datasets/package-seg # dataset root dir
train: images/train # train images (relative to 'path') 1920 images
val: images/val # val images (relative to 'path') 89 images
test: test/images # test images (relative to 'path') 188 images

# Classes
names:
  0: package

# Download script/URL (optional)
download: https://github.com/ultralytics/assets/releases/download/v0.0.0/package-seg.zip
