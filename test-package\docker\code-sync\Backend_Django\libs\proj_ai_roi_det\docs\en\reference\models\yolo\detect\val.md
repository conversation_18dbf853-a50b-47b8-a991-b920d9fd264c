---
description: Explore the DetectionValidator class for YOLO models in Ultralytics. Learn validation techniques, metrics, and dataset handling for object detection.
keywords: YOLO validation, detection validation, YOLO metrics, Ultralytics, object detection, machine learning, AI
---

# Reference for `ultralytics/models/yolo/detect/val.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/detect/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/detect/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/detect/val.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.detect.val.DetectionValidator

<br><br>
