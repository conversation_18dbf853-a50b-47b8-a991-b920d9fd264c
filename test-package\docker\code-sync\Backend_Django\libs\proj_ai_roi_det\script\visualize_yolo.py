﻿from graphviz import Digraph
import yaml

def create_network_graph(yaml_file):
    # 读取YAML配置
    with open(yaml_file, 'r') as f:
        cfg = yaml.safe_load(f)
    
    dot = Digraph(comment='YOLO Network')
    dot.attr(rankdir='TB')
    
    # 添加节点和边
    nodes = {}
    
    # 添加输入节点
    dot.node('input', 'Input\n(1, 1, 320, 320)')
    nodes[-1] = 'input'
    
    # Backbone
    for i, layer in enumerate(cfg['backbone']):
        from_layer, repeats, module, args = layer
        node_name = f"{module}_{i}"
        
        # 格式化参数显示
        if isinstance(args, list):
            args_str = f"\n{args}"
        else:
            args_str = f"\n{args}"
            
        label = f"{module}{args_str}\nx{repeats}"
        dot.node(node_name, label)
        
        # 添加边
        if isinstance(from_layer, int):
            if from_layer in nodes:
                dot.edge(nodes[from_layer], node_name)
        
        nodes[i] = node_name
    
    # Head
    backbone_len = len(cfg['backbone'])
    for i, layer in enumerate(cfg['head']):
        node_idx = backbone_len + i
        
        # 处理不同类型的层
        if isinstance(layer[0], list):  # 多输入层
            inputs = layer[0]
            module = layer[2]
            args = layer[3] if len(layer) > 3 else None
            
            node_name = f"{module}_{node_idx}"
            label = f"{module}\n{args if args else ''}"
            dot.node(node_name, label)
            
            # 添加所有输入连接
            for input_idx in inputs:
                if input_idx in nodes:
                    dot.edge(nodes[input_idx], node_name)
        else:  # 单输入层
            from_layer = layer[0]
            module = layer[2]
            args = layer[3] if len(layer) > 3 else None
            
            node_name = f"{module}_{node_idx}"
            label = f"{module}\n{args if args else ''}"
            dot.node(node_name, label)
            
            if from_layer in nodes:
                dot.edge(nodes[from_layer], node_name)
                
        nodes[node_idx] = node_name
    
    return dot

if __name__ == "__main__":
    # 使用方法
    dot = create_network_graph('../mindeo/configs/yolo11.yaml')
    dot.render('yolo_network', format='png', cleanup=True)
    print("Network visualization saved as 'yolo_network.png'") 