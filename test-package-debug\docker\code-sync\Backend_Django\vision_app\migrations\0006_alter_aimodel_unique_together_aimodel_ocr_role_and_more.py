# Generated by Django 5.2.1 on 2025-05-15 07:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0005_alter_aimodel_is_system_model_and_more"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="aimodel",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="aimodel",
            name="ocr_role",
            field=models.CharField(
                blank=True,
                choices=[
                    ("detection", "Detection Model"),
                    ("recognition", "Recognition Model"),
                ],
                help_text="Role of the OCR model (e.g., detection, recognition). Null if not an OCR model or role not applicable.",
                max_length=20,
                null=True,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="aimodel",
            unique_together={("name", "model_type", "is_system_model", "ocr_role")},
        ),
    ]
