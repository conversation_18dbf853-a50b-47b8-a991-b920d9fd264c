"""
AI推理服务模块

提供各种AI模型推理的统一接口，包括：
- OCR文字识别推理
- AI图像修复推理  
- 特征匹配推理
- 条码检测推理
- 统一的错误处理和结果格式化


完整的的 prediction_service 架构：

class PredictionService(BaseService):
    # ==================== OCR推理 ====================
    def predict_ocr_paddle()                    # PaddleOCR文字识别
    def _get_ocr_models()                       # 获取OCR模型
    def _build_ocr_model_paths()                # 构建OCR模型路径
    def _build_ocr_predictor_params()           # 构建OCR预测器参数
    def _get_ocr_char_dict_path()               # 智能查找OCR字典
    def _format_ocr_result()                    # 格式化OCR结果
    
    # ==================== AI图像修复推理 ====================
    def predict_image_restore()                 # AI图像修复
    def _get_ai_restore_model_path()            # 获取AI修复模型路径
    
    # ==================== 特征匹配推理 ====================
    def predict_feature_matching_traditional()  # 传统特征匹配
    def predict_feature_matching_model()        # 基于模型的特征匹配
    def _decode_image_bytes()                   # 图像字节解码
    def _get_feature_matching_model_path()      # 获取特征匹配模型路径
    
    # ==================== 条码检测推理 ====================
    def predict_barcode_ultralytics()           # YOLO条码检测
    def _get_barcode_model_path()               # 条码模型路径解析
    def _load_yolo_model()                      # YOLO模型加载
    def _execute_barcode_inference()            # 条码推理执行

"""

import os
import cv2
import numpy as np
import base64
from typing import Dict, Any, List, Optional, Tuple, Union
from django.conf import settings

from .base import BaseService
from .file_service import FileService
from .model_service import ModelService

# 模块级YOLO缓存，避免重复导入
_YOLO_CLASS = None

def get_yolo_class():
    """获取YOLO类，使用缓存避免重复导入"""
    global _YOLO_CLASS
    if _YOLO_CLASS is None:
        import os
        # 设置环境变量避免DLL问题
        os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
        from ultralytics import YOLO
        _YOLO_CLASS = YOLO
    return _YOLO_CLASS
from ..models import AIModel
from ..ocr_paddle_predictor import PaddleOCRSystemPredictor
from ..ai_restored_onnxruntime_predictor import AIRestoredOnnxRuntimePredictor
from ..feature_matching_traditional_predictor import TraditionalFeatureMatcher
from ..feature_matching_onnxruntime_model_predictor import ModelBasedFeatureMatcher


class PredictionService(BaseService):
    """
    AI推理服务类
    
    提供各种AI模型推理的统一接口和错误处理。
    """
    
    def __init__(self):
        """初始化推理服务"""
        super().__init__()
        self.file_service = FileService()
        self.model_service = ModelService()
        # 注意：ModelService内部已经有FileService实例，这里保持独立实例以避免循环依赖
    
    # ==================== OCR推理 ====================
    
    def predict_ocr_paddle(self, image_file, ocr_task_name: str, use_gpu: bool = False) -> Dict[str, Any]:
        """
        执行PaddleOCR推理
        
        Args:
            image_file: 上传的图像文件
            ocr_task_name: OCR任务名称
            use_gpu: 是否使用GPU
            
        Returns:
            OCR推理结果字典
        """
        self.log_info(f"Starting OCR prediction: task='{ocr_task_name}', use_gpu={use_gpu}")
        
        # 验证图像文件
        is_valid, error_msg = self.file_service.validate_image_file(image_file)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 查找OCR模型
        det_model, rec_model = self._get_ocr_models(ocr_task_name)
        
        # 构建模型路径
        det_model_dir, rec_model_dir = self._build_ocr_model_paths(det_model, rec_model)
        
        # 构建预测器参数
        predictor_params = self._build_ocr_predictor_params(det_model, rec_model, use_gpu)
        
        # 保存临时图像文件
        temp_image_path = self.file_service.save_temp_file(image_file)
        
        try:
            # 初始化预测器
            predictor = PaddleOCRSystemPredictor(
                det_model_dir=det_model_dir,
                rec_model_dir=rec_model_dir,
                **predictor_params
            )
            
            # 执行推理
            filter_boxes, filter_rec_res, time_dict = predictor.predict(temp_image_path)
            
            # 格式化结果
            result = self._format_ocr_result(filter_boxes, filter_rec_res, time_dict)
            
            self.log_info(f"OCR prediction completed: found {len(filter_rec_res)} text regions")
            return result
            
        except Exception as e:
            self.log_error(f"OCR prediction failed: {str(e)}")
            raise Exception(f'OCR推理失败: {str(e)}')
        finally:
            # 清理临时文件
            self.file_service.cleanup_temp_files()
    
    def _get_ocr_models(self, ocr_task_name: str) -> Tuple[AIModel, AIModel]:
        """获取OCR检测和识别模型"""
        return self.model_service.get_ocr_model_pair(ocr_task_name)
    
    def _build_ocr_model_paths(self, det_model: AIModel, rec_model: AIModel) -> Tuple[str, str]:
        """构建OCR模型路径"""
        det_model_dir = self.model_service.get_model_path(det_model)
        rec_model_dir = self.model_service.get_model_path(rec_model)

        return det_model_dir, rec_model_dir
    
    def _build_ocr_predictor_params(self, det_model: AIModel, rec_model: AIModel, use_gpu: bool) -> Dict[str, Any]:
        """构建OCR预测器参数"""
        # 从Django设置获取OCR任务配置
        ocr_task_configs = self.get_setting('OCR_TASK_CONFIGS', {})
        task_name = det_model.name  # 使用模型名称作为任务名称

        # 获取任务特定配置，如果没有则使用默认配置
        task_config = ocr_task_configs.get(task_name, ocr_task_configs.get('default', {}))

        # 基础参数（从配置中获取或使用默认值）
        params = {
            'use_gpu': use_gpu,
            'use_angle_cls': task_config.get('use_angle_cls', False),
            'drop_score': task_config.get('drop_score', 0.5),
            'det_algorithm': task_config.get('det_algorithm', 'DB'),
            'rec_algorithm': task_config.get('rec_algorithm', 'CRNN'),
            'lang': task_config.get('lang', 'ch'),
        }

        # 处理rec_image_shape参数（OCR预测器期望字符串格式）
        rec_image_shape = task_config.get('rec_image_shape', "3,32,320")
        if isinstance(rec_image_shape, list):
            # 如果是列表格式，转换为字符串格式
            params['rec_image_shape'] = ",".join(map(str, rec_image_shape))
        elif isinstance(rec_image_shape, str):
            # 如果已经是字符串格式，直接使用
            params['rec_image_shape'] = rec_image_shape
        else:
            # 默认值
            params['rec_image_shape'] = "3,32,320"

        # 添加其他OCR参数
        additional_params = [
            'det_limit_side_len', 'det_db_thresh', 'det_db_box_thresh',
            'det_db_unclip_ratio', 'use_space_char', 'det_db_score_mode',
            'rec_batch_num', 'max_text_length', 'use_onnx', 'use_npu',
            'use_mlu', 'use_xpu', 'return_word_box'
        ]

        for param in additional_params:
            if param in task_config:
                params[param] = task_config[param]

        # 字典路径（必需参数）
        rec_char_dict_path = self._get_ocr_char_dict_path(rec_model, task_config)
        if rec_char_dict_path:
            params['rec_char_dict_path'] = rec_char_dict_path
        else:
            # 如果没有找到字典文件，尝试使用配置中的路径
            config_dict_path = task_config.get('rec_char_dict_path')
            if config_dict_path and os.path.exists(config_dict_path):
                params['rec_char_dict_path'] = config_dict_path
            else:
                raise ValueError(f'OCR字典文件未找到，任务: {task_name}')

        # 处理角度分类模型路径（如果启用）
        if params.get('use_angle_cls') and task_config.get('cls_model_dir'):
            cls_model_dir = task_config['cls_model_dir']
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(cls_model_dir):
                system_models_root = self.get_setting('SYSTEM_MODELS_ROOT')
                if system_models_root:
                    cls_model_dir = os.path.join(system_models_root, cls_model_dir)
            params['cls_model_dir'] = cls_model_dir

        return params
    
    def _get_ocr_char_dict_path(self, rec_model: AIModel, task_config: Dict[str, Any]) -> Optional[str]:
        """获取OCR字符字典路径"""
        # 首先尝试使用配置中指定的字典路径
        config_dict_path = task_config.get('rec_char_dict_path')
        if config_dict_path and os.path.exists(config_dict_path):
            return config_dict_path

        # 如果配置中没有或文件不存在，尝试自动查找
        system_models_root = self.get_setting('SYSTEM_MODELS_ROOT')
        if not system_models_root:
            base_dir = self.get_setting('BASE_DIR')
            system_models_root = os.path.join(base_dir, 'models', 'system_models')

        # 构建识别模型的完整路径
        # 根据目录结构：ocr/car_liencese_ch/AI_OCR_Rec_CHNLP_NCHW_1x3x64x320/ppocr_keys_v1.txt
        model_file_name = rec_model.model_file.name if rec_model.model_file else None

        possible_paths = []
        if model_file_name:
            # 识别模型目录的完整路径
            rec_model_full_path = os.path.join(system_models_root, model_file_name)

            # 字典文件可能在识别模型的根目录下
            possible_paths.extend([
                os.path.join(rec_model_full_path, 'ppocr_keys_v1.txt'),
                os.path.join(rec_model_full_path, 'dict.txt'),
                os.path.join(rec_model_full_path, 'character_dict.txt'),
                os.path.join(rec_model_full_path, 'en_dict.txt'),
                # 也可能在inference子目录下
                os.path.join(rec_model_full_path, 'inference', 'ppocr_keys_v1.txt'),
                os.path.join(rec_model_full_path, 'inference', 'dict.txt'),
            ])

            # 根据任务名称推断可能的路径
            task_name = rec_model.name  # 如 'license_plate_cn'
            if 'license_plate' in task_name or 'car_liencese' in task_name:
                # 车牌识别任务的特定路径
                possible_paths.extend([
                    os.path.join(system_models_root, 'ocr', 'car_liencese_ch', 'AI_OCR_Rec_CHNLP_NCHW_1x3x64x320', 'ppocr_keys_v1.txt'),
                ])
            elif 'general' in task_name:
                # 通用OCR任务的路径
                if 'ch_en' in task_name:
                    possible_paths.extend([
                        os.path.join(system_models_root, 'ocr', 'general_ocr_mobile_ch_en', 'PP-OCRv4_mobile_rec_inference_ch_en', 'ppocr_keys_v1.txt'),
                    ])
                elif 'en' in task_name:
                    possible_paths.extend([
                        os.path.join(system_models_root, 'ocr', 'general_ocr_mobile_en', 'PP-OCRv4_mobile_rec_inference_en', 'en_dict.txt'),
                    ])
            elif 'id_card' in task_name or 'Identity_card' in task_name:
                # 身份证识别任务的路径
                possible_paths.extend([
                    os.path.join(system_models_root, 'ocr', 'Identity_card_number_en', 'Identity_card_rec_model', 'en_dict.txt'),
                ])

        # 通用备选路径
        possible_paths.extend([
            os.path.join(system_models_root, 'ocr', 'ppocr_keys_v1.txt'),
            os.path.join(system_models_root, 'ocr', 'dict.txt'),
        ])

        for path in possible_paths:
            if os.path.exists(path):
                self.log_info(f"Found OCR dictionary at: {path}")
                return path

        self.log_warning(f"No OCR dictionary found for model: {rec_model.name}")
        return None
    
    def _format_ocr_result(self, filter_boxes: List, filter_rec_res: List, time_dict: Dict) -> Dict[str, Any]:
        """格式化OCR推理结果"""
        # 构建结果数据
        ocr_results = []
        for i, (box, (text, confidence)) in enumerate(zip(filter_boxes, filter_rec_res)):
            ocr_results.append({
                'text': text,
                'confidence': float(confidence),
                'box': box.tolist() if hasattr(box, 'tolist') else box,
                'index': i
            })
        
        return {
            'status': 'success',
            'message': f'OCR识别完成，共识别出 {len(ocr_results)} 个文本区域',
            'results': ocr_results,
            'performance': {
                'detection_time': time_dict.get('det', 0),
                'recognition_time': time_dict.get('rec', 0),
                'classification_time': time_dict.get('cls', 0),
                'total_time': time_dict.get('all', 0)
            },
            'total_text_regions': len(ocr_results)
        }
    
    # ==================== AI图像修复推理 ====================
    
    def predict_image_restore(self, image_file, model_name: str, output_format: str = 'PNG') -> Dict[str, Any]:
        """
        执行AI图像修复推理
        
        Args:
            image_file: 上传的图像文件
            model_name: 模型名称
            output_format: 输出格式 ('PNG' 或 'JPEG')
            
        Returns:
            图像修复结果字典
        """
        self.log_info(f"Starting image restoration: model='{model_name}', format='{output_format}'")
        
        # 验证图像文件
        is_valid, error_msg = self.file_service.validate_image_file(image_file)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 验证输出格式
        if output_format.upper() not in ['PNG', 'JPEG']:
            output_format = 'PNG'
        
        # 获取模型路径
        model_path = self._get_ai_restore_model_path(model_name)
        
        try:
            # 初始化预测器
            predictor = AIRestoredOnnxRuntimePredictor(model_path=model_path)
            
            # 读取图像数据
            image_bytes = image_file.read()
            
            # 执行推理
            prediction_result = predictor.predict_restore_image(
                image_input=image_bytes,
                output_format=output_format
            )
            
            self.log_info("AI image restoration completed successfully")
            return prediction_result
            
        except Exception as e:
            self.log_error(f"AI image restoration failed: {str(e)}")
            raise Exception(f'AI图像修复失败: {str(e)}')
    
    def _get_ai_restore_model_path(self, model_name: str) -> str:
        """获取AI图像修复模型路径"""
        model = self.model_service.get_model_by_name(model_name, 'ai_restored', is_system_model=True)
        return self.model_service.get_model_path(model)

    # ==================== 特征匹配推理 ====================

    def predict_feature_matching_traditional(self, template_image_file, target_image_file,
                                           template_roi: Optional[Dict] = None,
                                           algorithm: str = 'SIFT',
                                           match_ratio_threshold: float = 0.7,
                                           min_match_count: int = 10) -> Dict[str, Any]:
        """
        执行传统特征匹配推理

        Args:
            template_image_file: 模板图像文件
            target_image_file: 目标图像文件
            template_roi: 模板图像ROI区域
            algorithm: 特征检测算法 ('SIFT' 或 'ORB')
            match_ratio_threshold: 匹配比例阈值
            min_match_count: 最小匹配点数量

        Returns:
            特征匹配结果字典
        """
        self.log_info(f"Starting traditional feature matching: algorithm='{algorithm}'")

        # 验证图像文件
        for img_file, name in [(template_image_file, '模板图像'), (target_image_file, '目标图像')]:
            is_valid, error_msg = self.file_service.validate_image_file(img_file)
            if not is_valid:
                raise ValueError(f'{name}验证失败: {error_msg}')

        try:
            # 读取图像数据
            template_bytes = template_image_file.read()
            target_bytes = target_image_file.read()

            # 解码图像
            template_image = self._decode_image_bytes(template_bytes)
            target_image = self._decode_image_bytes(target_bytes)

            # 初始化特征匹配器
            matcher = TraditionalFeatureMatcher(
                algorithm=algorithm,
                match_ratio_threshold=match_ratio_threshold,
                min_match_count=min_match_count
            )

            # 执行匹配
            result = matcher.match(template_image, target_image, template_roi)

            self.log_info(f"Traditional feature matching completed: status={result.get('status')}")
            return result

        except Exception as e:
            self.log_error(f"Traditional feature matching failed: {str(e)}")
            raise Exception(f'传统特征匹配失败: {str(e)}')

    def predict_feature_matching_model(self, template_image_file, target_image_file,
                                     model_id: int,
                                     template_roi: Optional[Dict] = None,
                                     keypoints_count: int = 100,
                                     nms_grid_size: int = 4,
                                     match_ratio_threshold: float = 0.8,
                                     min_match_count: int = 4,
                                     ransac_threshold: float = 5.0) -> Dict[str, Any]:
        """
        执行基于模型的特征匹配推理

        Args:
            template_image_file: 模板图像文件
            target_image_file: 目标图像文件
            model_id: 特征匹配模型ID
            template_roi: 模板图像ROI区域
            keypoints_count: 关键点数量
            nms_grid_size: NMS网格大小
            match_ratio_threshold: 匹配比例阈值
            min_match_count: 最小匹配点数量
            ransac_threshold: RANSAC阈值

        Returns:
            特征匹配结果字典
        """
        self.log_info(f"Starting model-based feature matching: model_id={model_id}")

        # 验证图像文件
        for img_file, name in [(template_image_file, '模板图像'), (target_image_file, '目标图像')]:
            is_valid, error_msg = self.file_service.validate_image_file(img_file)
            if not is_valid:
                raise ValueError(f'{name}验证失败: {error_msg}')

        # 获取模型路径
        model_path = self._get_feature_matching_model_path(model_id)

        try:
            # 读取图像数据
            template_bytes = template_image_file.read()
            target_bytes = target_image_file.read()

            # 解码图像
            template_image = self._decode_image_bytes(template_bytes)
            target_image = self._decode_image_bytes(target_bytes)

            # 初始化模型特征匹配器
            matcher = ModelBasedFeatureMatcher(model_path=model_path)

            # 执行匹配
            result = matcher.predict(
                template_image=template_image,
                target_image=target_image,
                template_roi=template_roi,
                keypoints_count=keypoints_count,
                nms_grid_size=nms_grid_size,
                match_ratio_threshold=match_ratio_threshold,
                min_match_count=min_match_count,
                ransac_threshold=ransac_threshold
            )

            self.log_info(f"Model-based feature matching completed: status={result.get('status')}")
            return result

        except Exception as e:
            self.log_error(f"Model-based feature matching failed: {str(e)}")
            raise Exception(f'基于模型的特征匹配失败: {str(e)}')

    def _decode_image_bytes(self, image_bytes: bytes) -> np.ndarray:
        """解码图像字节数据为OpenCV格式"""
        try:
            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_bytes, np.uint8)
            # 使用OpenCV解码
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is None:
                raise ValueError("无法解码图像数据")

            return image

        except Exception as e:
            raise ValueError(f"图像解码失败: {str(e)}")

    def _get_feature_matching_model_path(self, model_id: int) -> str:
        """获取特征匹配模型路径"""
        if not self.model_service.validate_model_exists(model_id):
            raise ValueError(f'特征匹配模型未找到: ID={model_id}')

        model = AIModel.objects.get(id=model_id, model_type='feature_matching')
        return self.model_service.get_model_path(model)

    # ==================== 条码检测推理 ====================

    def predict_barcode_ultralytics(self, image_file, model_name: str = None,
                                   model_file=None, confidence_threshold: float = 0.25,
                                   device: str = '', preprocessing_method: str = 'full_scale') -> Dict[str, Any]:
        """
        使用YOLO模型进行条码检测推理

        Args:
            image_file: 上传的图像文件
            model_name: 系统模型名称（可选）
            model_file: 上传的模型文件（可选）
            confidence_threshold: 置信度阈值
            device: 设备设置（'' 为自动）
            preprocessing_method: 预处理方法

        Returns:
            检测结果字典

        Raises:
            ValueError: 当参数无效时
        """
        # 参数验证
        if not image_file:
            raise ValueError('必须提供图像文件')

        if model_name and model_file:
            raise ValueError('请只提供 model_name 或 model_file，不能同时提供')

        if not model_name and not model_file:
            raise ValueError('必须提供 model_name (使用系统模型) 或 model_file (上传临时模型)')

        self.log_info(f"Starting barcode detection: model_name='{model_name}', conf={confidence_threshold}")

        # 验证图像文件
        is_valid, error_msg = self.file_service.validate_image_file(image_file)
        if not is_valid:
            raise ValueError(error_msg)

        try:
            # 获取模型路径
            model_path = self._get_barcode_model_path(model_name, model_file)

            # 加载YOLO模型
            model = self._load_yolo_model(model_path)

            # 执行推理
            result = self._execute_barcode_inference(
                model, image_file,
                confidence_threshold, device, preprocessing_method
            )

            self.log_info(f"Barcode detection completed: {len(result['detections'])} detections")
            return result

        except Exception as e:
            self.log_error(f"Barcode detection failed: {str(e)}")
            raise Exception(f'条码检测失败: {str(e)}')
        finally:
            # 确保清理临时文件
            self.file_service.cleanup_temp_files()

    def _get_barcode_model_path(self, model_name: str, model_file) -> str:
        """
        获取条码检测模型路径

        Args:
            model_name: 模型名称
            model_file: 模型文件

        Returns:
            模型文件路径
        """
        if model_name:
            # 使用系统或自定义模型
            try:
                # 优先查找系统模型
                ai_model = self.model_service.get_model_by_name(model_name, 'barcode', is_system_model=True)
            except ValueError:
                # 查找自定义模型
                ai_model = self.model_service.get_model_by_name(model_name, 'barcode', is_system_model=False)

            return self.model_service.get_model_path(ai_model)

        else:
            # 使用上传的模型文件
            self.log_info(f"Using uploaded model file: {model_file.name}")

            # 验证模型文件
            is_valid, error_msg = self.model_service.validate_model_file(model_file, 'barcode')
            if not is_valid:
                raise ValueError(error_msg)

            # 保存临时模型文件
            original_suffix = os.path.splitext(model_file.name)[1]
            suffix = original_suffix if original_suffix else '.pt'
            temp_model_path = self.file_service.save_temp_file(model_file, suffix)

            self.log_info(f"Uploaded model saved to: {temp_model_path}")
            return temp_model_path

    def _load_yolo_model(self, model_path: str):
        """
        加载YOLO模型

        Args:
            model_path: 模型文件路径

        Returns:
            加载的YOLO模型实例
        """
        from ultralytics import YOLO

        try:
            self.log_info(f"Loading YOLO model from: {model_path}")
            model = YOLO(model_path)
            self.log_info("YOLO model loaded successfully")
            return model

        except Exception as e:
            self.log_error(f"Failed to load YOLO model: {str(e)}")
            raise Exception(f'YOLO模型加载失败: {str(e)}')

    def _execute_barcode_inference(self, model, image_file,
                                 confidence_threshold: float, device: str,
                                 preprocessing_method: str) -> Dict[str, Any]:
        """
        执行条码检测推理

        Args:
            model: YOLO模型实例
            image_file: 图像文件
            confidence_threshold: 置信度阈值
            device: 设备设置
            preprocessing_method: 预处理方法

        Returns:
            检测结果字典
        """
        temp_image_path = None

        try:
            # 保存临时图像文件
            suffix = os.path.splitext(image_file.name)[1] or '.jpg'
            temp_image_path = self.file_service.save_temp_file(image_file, suffix)
            self.log_info(f"Image saved to: {temp_image_path}")

            # 记录预处理方法
            if preprocessing_method == "full_scale":
                self.log_info("Processing with 'full_scale': Full image with auto scaling")
            elif preprocessing_method == "roi_area":
                self.log_info("Processing with 'roi_area': Pre-cropped ROI image")
            else:
                self.log_warning(f"Unknown preprocessing_method: '{preprocessing_method}'. Using 'full_scale'")

            # 执行推理
            self.log_info(f"Performing inference: conf={confidence_threshold}, device='{device}'")
            results = model.predict(source=temp_image_path, conf=confidence_threshold, device=device)
            self.log_info("Inference completed")

            # 处理检测结果
            detections = []
            if results and len(results) > 0:
                for result in results:
                    if result.boxes:
                        for box in result.boxes:
                            try:
                                detections.append({
                                    'box': box.xyxy[0].tolist(),
                                    'confidence': box.conf[0].item(),
                                    'class_id': int(box.cls[0].item()),
                                    'class_name': model.names[int(box.cls[0].item())]
                                })
                            except Exception as box_e:
                                self.log_error(f"Error processing detection box: {str(box_e)}")

            return {
                'detections': detections,
                'total_detections': len(detections),
                'preprocessing_method': preprocessing_method
            }

        except Exception as e:
            self.log_error(f"Barcode inference execution failed: {str(e)}")
            raise Exception(f'推理执行失败: {str(e)}')
