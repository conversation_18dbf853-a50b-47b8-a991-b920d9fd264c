#ifndef _AI_ENGINE_PADDLE_LITE_H
#define _AI_ENGINE_PADDLE_LITE_H

//-----------------------------------------------------------------------------
//  Includes

#include <dirent.h>

#include "paddle_api.h"
#include "paddle_place.h"

#include "autoconf.h"
#include "NIU.hpp"
#include "ModelLoader.hpp"
#include "Preprocess.hpp"
#include "Postprocess.hpp"
//-----------------------------------------------------------------------------
//  Definitions

// 自适应子位深度提取范围功能参考区域尺寸
#ifndef MACR_ADAP_SBER_REF_SIZE
#define MACR_ADAP_SBER_REF_SIZE                 (100)
#endif

// 离线模型存储路径
#ifndef MACR_OFFLINE_MODEL_PATH
#define MACR_OFFLINE_MODEL_PATH                 "./"
#endif
//-----------------------------------------------------------------------------
//  Declarations

namespace AIEnginePaddleLite{
/**
 * @brief    
 *           模型加载器
 *           
 * @date     2024-11-01 Created by HuangJP
 */
class ModelLoader : public BaseModelLoader
{
public:
    /**
     * @brief    
     *           获取ModelLoader实例，该实例具备唯一性
     *           
     * @retval   指向ModelLoader实例的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    static ModelLoader *Instance(void)
    {
        static ModelLoader model_loader;
        return &model_loader;
    }

    /**
     * @brief    
     *           获取解释器
     *           
     * @param    model_tag:     模型标签
     *           
     * @retval   指向解释器的指针，获取失败时返回空指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    std::shared_ptr<paddle::lite_api::PaddlePredictor> Get_Interpreter(std::string model_tag)
    {
        // 尝试获取解释器
        if (interpreter_map.find(model_tag) == interpreter_map.end())
        {
            // 获取失败时，尝试创建解释器
            const ModelData *model = Get_Model_Data(model_tag);  // 尝试获取模型数据
            if (model == nullptr)
            {
                // 获取模型数据失败，返回空指针
                return nullptr;
            }

            paddle::lite_api::MobileConfig config;

            // 判断模型文件是否嵌入到了ModelData中
            if (model->data_size == 0)
            {
                // 尝试获取模型文件
                std::string model_path(MACR_OFFLINE_MODEL_PATH); // 模型存放路径
                std::vector<std::string> model_list = this->_get_offline_model_list(model_path); // 获取离线模型列表
                std::string model_file;
                for (auto filename: model_list)
                {
                    // 遍历离线模型列表，找到目标模型文件
                    if (filename.find(model_tag) != std::string::npos)
                    {
                        // 找到模型文件，停止遍历离线模型列表，并记录模型文件路径
                        model_file.assign(model_path+filename);
                        break;
                    }
                }

                // 判断模型文件是否获取成功
                if (model_file.empty())
                {
                    LOGE("The corresponding model was not found in the specified path.");
                    LOGD("An attempt was made to locate model `%s` from `%s`, but it failed.", model_tag.c_str(), MACR_OFFLINE_MODEL_PATH);
                    return nullptr;
                }

                // 从文件创建解释器
                config.set_model_from_file(model_file);
            }
            else
            {
                // 从内存创建解释器
                config.set_model_from_buffer(std::string(reinterpret_cast<const char*>(model->data.data()), model->data.size()));
            }

            // 配置解释器
            config.set_threads(model->num_threads); // 工作线程数
            config.set_power_mode(paddle::lite_api::LITE_POWER_HIGH); // 能耗模式

            // 保存解释器
            std::shared_ptr<paddle::lite_api::PaddlePredictor> net = paddle::lite_api::CreatePaddlePredictor(config);
            if (net == nullptr)
            {
                // 创建解释器失败，返回空指针
                LOGE("Failed to create interpreter");
                return nullptr;
            }
            interpreter_map[model_tag] = net;
        }

        return interpreter_map[model_tag];
    }

protected:
    // 模型标签(string)映射到解释器的数据类型
    typedef std::unordered_map<std::string, std::shared_ptr<paddle::lite_api::PaddlePredictor>> interpreter_map_t;
    interpreter_map_t interpreter_map;

    // 析构函数
    ~ModelLoader() {}
    // 构造函数
    ModelLoader(): BaseModelLoader() {}

private:
    /**
     * @brief    
     *           获取离线模型列表
     *           
     * @param    path:      模型存放路径
     *           
     * @retval   离线模型存放路径列表
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    std::vector<std::string> _get_offline_model_list(std::string path)
    {
        std::vector<std::string> offline_models;
        DIR *dir; // 打开路径
        struct dirent *dr;

        // 打开模型存放路径
        dir = opendir(path.c_str());
        if (dir == nullptr)
        {
            return offline_models; // 打开目标路径失败，返回空的模型列表
        }

        // 逐个读目录下的文件
        while ((dr = readdir(dir)) != nullptr)
        {
            // 获取文件后缀
            std::string filename(dr->d_name);
            size_t pos = filename.find_last_of('.');
            if (pos == std::string::npos)
            {
                continue; // 获取后缀分隔符失败，跳过当前文件
            }

            // 判断当前文件是否为模型文件
            std::string suffix = filename.substr(pos+1);
            if (suffix != "nb")
            {
                continue; // 当前文件不是模型文件，跳过当前文件
            }

            offline_models.emplace_back(filename); // 返回模型文件名
        }

        closedir(dir); // 关闭打开的路径

        return offline_models; // 返回离线模型列表
    }
};

/**
 * @brief    
 *           前处理模块
 *           
 * @date     2024-11-01 Created by HuangJP
 */
class Preprocess : public BasePreprocess
{
public:
    /**
     * @brief    
     *           前处理模块构造函数（生产函数）
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @retval   指向前处理模块对象的指针，构造失败时返回空指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    static Preprocess *Construct(std::shared_ptr<paddle::lite_api::PaddlePredictor> interpreter, const BaseModelLoader::ModelData *model_data)
    {
        // 检查传入参数是否合法
        if ((interpreter == nullptr)
            || (model_data == nullptr))
        {
            LOGE("A null pointer have been detected");
            return nullptr;
        }

        // 构造前处理模块
        Preprocess *pre = new (std::nothrow)Preprocess(interpreter, model_data);

        // 判断构造是否成功
        if (pre == nullptr)
        {
            LOGE("Failed to allocate memory");
            return nullptr;
        }

        return pre;
    }

    /**
     * @brief    
     *           前处理模块销毁函数
     *           
     * @param    pre:       指向前处理模块的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    static void Destruct(Preprocess *pre)
    {
        if (pre != nullptr)
        {
            delete pre;
        }
    }

protected:
    /**
     * @brief    
     *           获取输入图片工具实例
     *           
     * @param    color_format:      输入图片颜色格式
     * @param    rsn:               右移位数
     * @param    memory_format:     内存存储格式
     *           
     * @retval   指向输入图片工具实例的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    InputImage *Get_Input_Image_Instance(bit_depth_t bit_depth, color_format_t color_format) override
    {
        return _Get_Input_Image_Instance<tensor_t, true>(bit_depth, color_format, this->_model_data->memory_format);
    }

    /**
     * @brief    
     *           前处理数据构造函数
     *           
     * @param    index:     输入张量索引
     * @param    data:      指向前处理数据的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    int Construct_Preprocessed_Data(void *index, PreprocessedData *data) override
    {
        // 判断传入参数是否合法
        if ((index == nullptr)
            || (data == nullptr))
        {
            LOGE("A null pointer have been detected");
            return AIENGINE_INVALID_PARAM;
        }

        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 尝试根据索引获取输入张量
        if (input_tensor_map.find(idx) == input_tensor_map.end())
        {
            std::unique_ptr<paddle::lite_api::Tensor> input_tensor(std::move(this->_interpreter->GetInput(idx)));
            if (input_tensor == nullptr)
            {
                LOGE("Failed to get input tensor");
                return AIENGINE_GOT_NULLPTR;
            }

            input_tensor_map[idx] = std::move(input_tensor);
        }

        // 获取输入张量信息
        std::vector<int64_t> input_shape = input_tensor_map[idx]->shape();
        if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
        {
            data->resize_n = input_shape[0];
            data->resize_c = input_shape[1];
            data->resize_h = input_shape[2];
            data->resize_w = input_shape[3];
        }
        else if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
        {
            data->resize_n = input_shape[0];
            data->resize_h = input_shape[1];
            data->resize_w = input_shape[2];
            data->resize_c = input_shape[3];
        }
        else
        {
            LOGE("Unsupported input tensor memory format.");
            return AIENGINE_INVALID_PARAM;
        }

        data->input_tensor_ptr = input_tensor_map[idx]->mutable_data<float>();

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           同步输入张量数据到内存
     *           
     * @param    index:     输入张量索引
     *           
     * @retval   错误码
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    int Input_Tensor_Flush_Memory(void *index) override
    {
        return AIENGINE_NO_ERROR;
    }

private:
    typedef int tensor_index_t;
    typedef float tensor_t;

    std::unordered_map<tensor_index_t, std::unique_ptr<paddle::lite_api::Tensor>> input_tensor_map;

    const std::shared_ptr<paddle::lite_api::PaddlePredictor> _interpreter;

    /**
     * @brief    
     *           前处理模块析构函数
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    ~Preprocess()
    {

    }

    /**
     * @brief    
     *           前处理模块构造函数
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    Preprocess(std::shared_ptr<paddle::lite_api::PaddlePredictor> interpreter, const BaseModelLoader::ModelData *model_data): 
        BasePreprocess(MACR_ADAP_SBER_REF_SIZE, model_data),
        _interpreter(interpreter)
    {
        
    }
};

/**
 * @brief    
 *           后处理模块
 *           
 * @date     2024-11-01 Created by HuangJP
 */
class Postprocess : public BasePostprocess
{
public:
    /**
     * @brief    
     *           后处理模块构造函数（生产函数）
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @retval   指向前处理模块对象的指针，构造失败时返回空指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    static Postprocess *Construct(std::shared_ptr<paddle::lite_api::PaddlePredictor> interpreter, const BaseModelLoader::ModelData *model_data)
    {
        // 检查传入参数是否合法
        if ((interpreter == nullptr)
            || (model_data == nullptr))
        {
            LOGE("A null pointer has been detected");
            return nullptr;
        }

        // 构造前处理模块
        Postprocess *post = new (std::nothrow)Postprocess(interpreter, model_data);

        // 判断构造是否成功
        if (post == nullptr)
        {
            LOGE("Failed to allocate memory");
            return nullptr;
        }

        return post;
    }

    /**
     * @brief    
     *           后处理模块销毁函数
     *           
     * @param    post:  指向后处理模块的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    static void Destruct(Postprocess *post)
    {
        if (post != nullptr)
        {
            delete post;
        }
    }

private:
    typedef int tensor_index_t;
    typedef float tensor_t;

    struct paddle_output_tensor: output_tensor_t {
        std::unique_ptr<const paddle::lite_api::Tensor> output_tensor;    // NCHW格式的输出张量
    }; // MNN输出张量

    std::unordered_map<tensor_index_t, paddle_output_tensor> paddle_output_tensor_map;
    const std::shared_ptr<paddle::lite_api::PaddlePredictor> _interpreter;

    /**
     * @brief    
     *           获取输出张量
     *           
     * @param    index:     输出张量索引
     *           
     * @retval   指向输出张量组的指针，获取失败时返回空指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    output_tensor_t *Get_Output_Tensor(void *index) override
    {
        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 尝试根据索引获取输入张量组
        if (paddle_output_tensor_map.find(idx) == paddle_output_tensor_map.end())
        {
            // 获取失败，尝试创建输出张量组
            std::unique_ptr<const paddle::lite_api::Tensor> output_tensor(std::move(this->_interpreter->GetOutput(idx)));
            if (output_tensor == nullptr)
            {
                LOGE("Failed to get output tensor");
                return nullptr;
            }

            paddle_output_tensor_map[idx] = paddle_output_tensor{};
            paddle_output_tensor_map[idx].output_tensor = std::move(output_tensor);
            paddle_output_tensor_map[idx].data = output_tensor->mutable_data<float>();
            for (auto num: output_tensor->shape()) paddle_output_tensor_map[idx].shape.emplace_back(num);
        }

        return &paddle_output_tensor_map[idx];
    }

    /**
     * @brief    
     *           后处理模块析构函数
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    ~Postprocess()
    {

    }

    /**
     * @brief    
     *           后处理模块构造函数
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    Postprocess(std::shared_ptr<paddle::lite_api::PaddlePredictor> interpreter, const BaseModelLoader::ModelData *model_data): 
        BasePostprocess(model_data),
        _interpreter(interpreter)
    {

    }
};

/**
 * @brief    
 *           神经网络推理单元
 *           
 * @date     2024-11-01 Created by HuangJP
 */
class NIU : public BaseNIU
{
public:
    /**
     * @brief    
     *           NIU构造函数（生产函数）
     *           
     * @param    model_tag:     模型标签
     *           
     * @retval   指向NIU的指针，构造失败时，返回空指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    static NIU *Construct(std::string model_tag)
    {
        // 构造NIU
        NIU *niu = new (std::nothrow)NIU(model_tag);
        if (niu == nullptr)
        {
            LOGE("Failed to allocate memory");
            return nullptr;
        }

        // 判断NIU是否未准备好
        if (niu->is_ready == false)
        {
            // NIU未准备好，释放申请的内存
            delete niu;
            niu = nullptr;
            return nullptr;
        }

        return niu;
    }

    /**
     * @brief    
     *           NIU销毁函数
     *           
     * @param    指向NIU的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    static void Destruct(NIU *niu)
    {
        if (niu != nullptr)
        {
            delete niu;
        }
    }

    /**
     * @brief    
     *           获取前处理模块接口
     *           
     * @retval   指向前处理模块的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    class Preprocess *Preprocess(void) override
    {
        return this->pre.get();
    }

    /**
     * @brief    
     *           获取后处理模块接口
     *           
     * @retval   指向后处理模块的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    class Postprocess *Postprocess(void) override
    {
        return this->post.get();
    }

    /**
     * @brief    
     *           推理接口
     *           
     * @retval   错误码
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    int Inference(void) override
    {
        // 调用推理
        interpreter->Run();

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           获取输入张量索引列表
     *           
     * @retval   指向输入张量索引列表的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    const std::vector<void *> *Get_Input_Tensor_Index_List(void) override
    {
        return &model->input_tensor_index_list;
    }

    /**
     * @brief    
     *           获取输出张量索引列表
     *           
     * @retval   指向输出张量索引列表的指针
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    const std::vector<void *> *Get_Output_Tensor_Index_List(void) override
    {
        return &model->output_tensor_index_list;
    }

private:
    bool is_ready; // 是否准备好
    std::shared_ptr<paddle::lite_api::PaddlePredictor> interpreter; // 解释器
    const BaseModelLoader::ModelData *model; // 模型数据
    std::shared_ptr<class Preprocess> pre; // 前处理模块
    std::shared_ptr<class Postprocess> post; // 后处理模块

    /**
     * @brief    
     *           神经网络推理单元析构函数
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    ~NIU()
    {

    }

    /**
     * @brief    
     *           神经网络推理单元构造函数
     *           
     * @param    model_tag:     模型标签
     *           
     * @date     2024-11-01 Created by HuangJP
     */
    NIU(std::string model_tag)
    {
        // 变量初始化
        is_ready = false;
        interpreter = nullptr;
        model = nullptr;
        pre = nullptr;
        post = nullptr;
        
        // 尝试获取解释器
        interpreter = ModelLoader::Instance()->Get_Interpreter(model_tag);
        if (interpreter == nullptr)
        {
            return; // 获取解释器失败，错误返回
        }

        // 尝试获取模型数据
        model = ModelLoader::Get_Model_Data(model_tag);
        if (model == nullptr)
        {
            return;
        }

        // 构造前处理模块
        pre = std::shared_ptr<class Preprocess>(
            Preprocess::Construct(interpreter, this->model), 
            Preprocess::Destruct);
        if (pre == nullptr)
        {
            LOGE("Failed to construct preprocess module");
            return;
        }

        // 构造后处理模块
        post = std::shared_ptr<class Postprocess>(
            Postprocess::Construct(interpreter, this->model), 
            Postprocess::Destruct);
        if (pre == nullptr)
        {
            LOGE("Failed to construct postprocess module");
            return;
        }

        // 通知NIU创建成功
        is_ready = true;
    }
};

} /* namespace AIEnginePaddleLite */
#endif
//-----------------------------------------------------------------------------
//  End of file