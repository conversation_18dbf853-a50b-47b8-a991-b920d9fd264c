import React, { useState, useEffect, useRef } from 'react';
import {
    Form,
    Slider,
    InputNumber,
    Button,
    Typography,
    Space,
    Row,
    Col,
    message,
    Alert,
    Radio,
    Select,
    Upload,
    Modal,
    Tooltip,
    Switch,
    Divider,
    Spin
} from 'antd';
import { UploadOutlined, SaveOutlined, SyncOutlined } from '@ant-design/icons';
import type { UploadFile /*, UploadProps */ } from 'antd/es/upload/interface';
import { useQuery } from '@tanstack/react-query';
import { useImageWorkspace } from '../../contexts/ImageWorkspaceContext';
import { useBarcodeDetectionParams, PreprocessingMethod } from '../../contexts/BarcodeDetectionContext';
import { useFunctionPanel } from '../../contexts/FunctionPanelContext';
import {
    detectBarcode,
    BarcodeDetectionParams,
    BarcodeDetectionResult,
    CustomApiError,
    Barcode,
    VisionModel,
    uploadCustomModel,
    UploadCustomModelParams,
    getBarcodeModels
} from '../../services/api';

const { Title } = Typography;
const { Option } = Select;

interface BarcodeDetectionFormValues {
    confidence_threshold: number;
    preprocessing_method: PreprocessingMethod;
    batch_processing_interval: number;
    auto_inference: boolean;
}

interface BatchResultItem {
    index: number;
    fileName: string;
    detections: number;
    imageDataUrl: string | null;
    processed: boolean;
    error?: string;
}

const BarcodeDetectionPanel: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm<BarcodeDetectionFormValues>();
    // 移除本地状态，使用Context中的状态
    // const [isLoading, setIsLoading] = useState<boolean>(false);
    // const [isBatchLoading, setIsBatchLoading] = useState<boolean>(false);
    const imageWorkspaceContext = useImageWorkspace();
    const { selectedRoi, setSelectedRoiCoordinates } = imageWorkspaceContext || {};
    const { selectedFunction } = useFunctionPanel();
    const {
        preprocessingMethod,
        setPreprocessingMethod,
        confidenceThreshold,
        setConfidenceThreshold,
        batchProcessingInterval,
        setBatchProcessingInterval,
        autoInferenceEnabled,
        setAutoInferenceEnabled,
        isInferring,
        setIsInferring,
        isBatchProcessing,
        setIsBatchProcessing,
        // 模型管理相关状态 - 从Context获取
        selectedModelName,
        setSelectedModelName,
        modelSelectionType,
        setModelSelectionType,
        systemBarcodeModels,
        setSystemBarcodeModels,
        customBarcodeModels,
        setCustomBarcodeModels,
        modelsLoading,
        setModelsLoading,
        modelsError,
        setModelsError,
    } = useBarcodeDetectionParams();

    // 使用react-query获取条码模型
    const {
        isLoading: reactQueryModelsLoadingInitial,
        error: reactQueryModelsError,
        data: fetchedModelsData,
        refetch: refetchBarcodeModels,
    } = useQuery<{ systemModels: VisionModel[], customModels: VisionModel[] }, CustomApiError>({
        queryKey: ['barcodeModels'],
        queryFn: getBarcodeModels,
    });

    // 同步react-query状态到context
    useEffect(() => {
        setModelsLoading(reactQueryModelsLoadingInitial);
    }, [reactQueryModelsLoadingInitial, setModelsLoading]);

    useEffect(() => {
        if (fetchedModelsData) {
            setSystemBarcodeModels(fetchedModelsData.systemModels);
            setCustomBarcodeModels(fetchedModelsData.customModels);
            setModelsError(null);
            // 设置默认选择的模型
            if (modelSelectionType === 'system' && fetchedModelsData.systemModels.length > 0) {
                if (!selectedModelName || !fetchedModelsData.systemModels.some(m => m.name === selectedModelName)) {
                    setSelectedModelName(fetchedModelsData.systemModels[0].name);
                }
            } else if (modelSelectionType === 'custom' && fetchedModelsData.customModels.length > 0) {
                if (!selectedModelName || !fetchedModelsData.customModels.some(m => m.name === selectedModelName)) {
                    setSelectedModelName(fetchedModelsData.customModels[0].name);
                }
            }
        }
    }, [fetchedModelsData, setSystemBarcodeModels, setCustomBarcodeModels, setSelectedModelName, selectedModelName, modelSelectionType, setModelsError]);

    useEffect(() => {
        if (reactQueryModelsError) {
            const errMsg = reactQueryModelsError.message || '获取条码模型列表失败';
            setModelsError(errMsg);
            setSystemBarcodeModels([]);
            setCustomBarcodeModels([]);
        }
    }, [reactQueryModelsError, setModelsError, setSystemBarcodeModels, setCustomBarcodeModels]);

    // 保留的本地状态
    const [customModelFile, setCustomModelFile] = useState<UploadFile | null>(null);
    const [isUploading, setIsUploading] = useState<boolean>(false);

    const [showSaveConfirm, setShowSaveConfirm] = useState<boolean>(false);
    const [batchResults, setBatchResults] = useState<{
        totalImages: number,
        processedImages: number,
        totalDetections: number,
        resultItems: BatchResultItem[]
    }>({
        totalImages: 0,
        processedImages: 0,
        totalDetections: 0,
        resultItems: []
    });
    const [isSavingBatch, setIsSavingBatch] = useState<boolean>(false);

    // 用于跟踪当前面板状态，避免重复加载
    const currentPanelRef = useRef<string | null>(null);

    const initialFormValues: Partial<BarcodeDetectionFormValues> = {
        confidence_threshold: confidenceThreshold,
        preprocessing_method: preprocessingMethod,
        batch_processing_interval: batchProcessingInterval,
        auto_inference: autoInferenceEnabled,
    };



    // 监听面板切换，每次切换到条码检测面板时显示模型加载成功提示（完全仿照AI复原面板逻辑）
    useEffect(() => {
        const currentKey = selectedFunction?.key as string;
        if (currentKey === 'barcode-detection' && currentPanelRef.current !== currentKey && fetchedModelsData && (fetchedModelsData.systemModels.length > 0 || fetchedModelsData.customModels.length > 0) && !modelsLoading) {
            console.log('[BarcodeDetectionPanel] 面板切换到条码检测，模型已加载');
            currentPanelRef.current = currentKey;
            messageApi.success('条码模型列表已成功加载！');
        } else if (currentKey !== 'barcode-detection') {
            // 当切换到其他面板时，重置状态
            currentPanelRef.current = null;
        }
    }, [selectedFunction?.key, fetchedModelsData, modelsLoading, messageApi]);

    useEffect(() => {
        form.setFieldsValue({
            confidence_threshold: confidenceThreshold,
            preprocessing_method: preprocessingMethod,
            batch_processing_interval: batchProcessingInterval,
        });
    }, [confidenceThreshold, preprocessingMethod, batchProcessingInterval, form]);

    // 监听预处理方式切换，当从ROI切换到全图缩放时清除ROI区域
    useEffect(() => {
        // 当预处理方式从ROI切换到全图缩放时，清除ROI选择
        if (preprocessingMethod === 'full_scale' && selectedRoi && setSelectedRoiCoordinates) {
            console.log('预处理方式切换到全图缩放，清除ROI区域');
            setSelectedRoiCoordinates(null);
        }
    }, [preprocessingMethod, selectedRoi, setSelectedRoiCoordinates]);

    // 监听模型选择类型变化，清理自定义模型文件
    useEffect(() => {
        if (modelSelectionType === 'system') {
            setCustomModelFile(null);
        }
    }, [modelSelectionType]);

    const handleCustomModelUpload = async () => {
        if (!customModelFile) {
            messageApi.error('请先选择一个模型文件 (.pt)！');
            return;
        }

        if (!customModelFile.name?.toLowerCase().endsWith('.pt')) {
            messageApi.error('仅支持 .pt 格式的模型文件!');
            return;
        }
        const fileToUpload = customModelFile.originFileObj || (customModelFile as unknown as File);
        if (!(fileToUpload instanceof File)) {
             messageApi.error('无法获取有效的模型文件对象进行上传。');
             return;
        }

        setIsUploading(true);
        const uploadMessageKey = 'customModelUpload';

        let modelNameForApi = customModelFile.name;
        if (modelNameForApi.toLowerCase().endsWith('.pt')) {
            modelNameForApi = modelNameForApi.substring(0, modelNameForApi.length - 3);
        }

        messageApi.loading({ content: `正在上传模型 "${modelNameForApi}"...`, key: uploadMessageKey, duration: 0 });

        const params: UploadCustomModelParams = {
            model_file: fileToUpload,
            name: modelNameForApi,
            model_type: 'barcode',
        };

        try {
            const uploadedModel = await uploadCustomModel(params);
            messageApi.success({ content: `模型 "${uploadedModel.name}" 上传成功！`, key: uploadMessageKey, duration: 3 });

            setCustomModelFile(null);

            await refetchBarcodeModels();

            if (modelSelectionType === 'custom') {
                 setSelectedModelName(uploadedModel.name);
            }

        } catch (error) {
            const apiError = error as CustomApiError;
            console.error('模型上传失败 (raw error object):', JSON.stringify(apiError, null, 2));
            console.error('模型上传失败 (apiError.message):', apiError.message);
            console.error('模型上传失败 (apiError.status):', apiError.status);
            console.error('模型上传失败 (apiError.detail):', JSON.stringify(apiError.detail, null, 2));

            let displayErrorMessage = apiError.message || '未知错误';

            if (apiError.status === 400) {
                let detailMessage = '';
                if (typeof apiError.detail === 'string') {
                    detailMessage = apiError.detail.toLowerCase();
                } else if (apiError.detail && typeof apiError.detail === 'object' && !Array.isArray(apiError.detail)) {
                    const errorValues = Object.values(apiError.detail).flat();
                    for (const val of errorValues) {
                        if (typeof val === 'string') {
                            detailMessage += val.toLowerCase() + ' ';
                        }
                    }
                    const currentDetailObject = apiError.detail as Record<string, any>;
                    if (!detailMessage && currentDetailObject['detail'] && typeof currentDetailObject['detail'] === 'string') {
                        detailMessage = currentDetailObject['detail'].toLowerCase();
                    }
                    if (!detailMessage) {
                        detailMessage = JSON.stringify(apiError.detail).toLowerCase();
                    }
                } else if (Array.isArray(apiError.detail)) {
                     detailMessage = JSON.stringify(apiError.detail).toLowerCase();
                }

                if (detailMessage.includes("already exists") ||
                    detailMessage.includes("duplicate") ||
                    detailMessage.includes("unique constraint") ||
                    detailMessage.includes("name must be unique") ||
                    detailMessage.includes("already has a model with this name")
                ) {
                    displayErrorMessage = '模型名称已存在。请选择其他文件，或先删除同名模型。';
                } else if (typeof apiError.detail === 'string' && apiError.detail.trim() !== '') {
                    displayErrorMessage = apiError.detail;
                } else {
                    displayErrorMessage = `请求无效 (错误码: 400)。`;
                }
            }

            messageApi.error({ content: `模型上传失败: ${displayErrorMessage}`, key: uploadMessageKey, duration: 5 });
        } finally {
            setIsUploading(false);
        }
    };

    const handleDetection = async (values: BarcodeDetectionFormValues) => {
        if (!imageWorkspaceContext || imageWorkspaceContext.currentImageIndex === -1 || !imageWorkspaceContext.imageList[imageWorkspaceContext.currentImageIndex]) {
            messageApi.error('请先加载一张图片！');
            return;
        }
        const currentImageFile = imageWorkspaceContext.imageList[imageWorkspaceContext.currentImageIndex];
        if (!currentImageFile) {
            messageApi.error('无法获取当前图像文件！');
            return;
        }
        const { croppedImageDataUrl, selectedRoi, setDetectionResults } = imageWorkspaceContext;

        if (!selectedModelName) {
             messageApi.error(`请在下拉列表中选择一个${modelSelectionType === 'system' ? '系统' : '自定义'}模型进行检测！`);
             return;
        }

        setIsInferring(true);
        // 移除loading提示，使用Spin组件统一显示加载状态
        // const hideLoadingMessage = messageApi.loading({ content: '正在检测条码...', key: 'barcodeDetection', duration: 0 });

        // 检查ROI预处理方式的前置条件
        if (preprocessingMethod === "roi" && !selectedRoi) {
            messageApi.warning({ content: '请先到菜单栏「编辑」→「截取ROI范围」选择检测区域。', duration: 4 });
            setIsInferring(false);
            return;
        }

        let imageSourceForApi: File | string;
        if (preprocessingMethod === "roi") {
            if (croppedImageDataUrl && croppedImageDataUrl.startsWith('data:image')) {
                imageSourceForApi = croppedImageDataUrl;
            } else {
                messageApi.warning({ content: '请先选择并确认一个 ROI 区域，然后再进行检测。', duration: 3 });
                setIsInferring(false);
                // hideLoadingMessage();
                return;
            }
        } else {
            imageSourceForApi = currentImageFile;
        }

        const apiPreprocessingMethod: BarcodeDetectionParams['preprocessing_method'] =
            preprocessingMethod === 'roi' ? 'roi_area' : 'full_scale';

        const apiParams: BarcodeDetectionParams = {
            image: imageSourceForApi,
            confidence_threshold: values.confidence_threshold,
            preprocessing_method: apiPreprocessingMethod,
            model_name: selectedModelName,
        };

        try {
            const result: BarcodeDetectionResult = await detectBarcode(apiParams);
            let finalDetections = result.detections;

            if (preprocessingMethod === "roi" && selectedRoi) {
                const roiX = selectedRoi.x;
                const roiY = selectedRoi.y;
                finalDetections = result.detections.map(detection => ({
                    ...detection,
                    box: [
                        detection.box[0] + roiX,
                        detection.box[1] + roiY,
                        detection.box[2] + roiX,
                        detection.box[3] + roiY,
                    ] as [number, number, number, number],
                }));
            }

            // 统一显示成功提示，与OCR和AI复原保持一致
            messageApi.success('条码检测成功！');

            if (setDetectionResults) {
                const displayableResults = finalDetections.map((barcode: Barcode, idx: number) => ({
                    box: barcode.box,
                    label: `${barcode.class_name || 'N/A'} (${barcode.confidence.toFixed(2)})`,
                    type: 'barcode' as const,
                    confidence: barcode.confidence,
                    class_name: barcode.class_name,
                    class_id: barcode.class_id,
                    displayIndex: idx + 1,
                    id: `${barcode.class_name || barcode.class_id || 'detection'}-${barcode.box.join('-')}-${Math.random().toString(36).substring(7)}`
                }));
                setDetectionResults(displayableResults);
            } else {
                console.warn("ImageWorkspaceContext.setDetectionResults is not defined.");
                messageApi.warning({ content: "检测结果处理函数未定义，无法在图像上显示。", duration: 3 });
            }

        } catch (error) {
            const apiError = error as CustomApiError;
            console.error('条码检测失败:', apiError);
            messageApi.error({ content: `条码检测失败: ${apiError.message || '未知错误'}`, duration: 4 });
        } finally {
            setIsInferring(false);
            // hideLoadingMessage();
        }
    };

    const handleMultiImageDetection = async () => {
        if (!imageWorkspaceContext || !imageWorkspaceContext.imageList || imageWorkspaceContext.imageList.length <= 1) {
            messageApi.error('请先通过"打开文件夹"加载多张图片后再执行多图推理！');
            return;
        }
        if (preprocessingMethod === "roi") {
            messageApi.error('多图推理不支持"指定ROI区域"预处理方式，请选择"全图缩放"。');
            return;
        }
        if (!selectedModelName) {
            messageApi.error(`请在下拉列表中选择一个${modelSelectionType === 'system' ? '系统' : '自定义'}模型进行检测！`);
            return;
        }

        setIsBatchProcessing(true);
        const batchDetectionMessageKey = 'batchBarcodeDetection';
        messageApi.loading({ content: `开始批量检测 ${imageWorkspaceContext.imageList.length} 张图片...`, key: batchDetectionMessageKey, duration: 0 });

        if (imageWorkspaceContext.setIsBatchProcessingActive) imageWorkspaceContext.setIsBatchProcessingActive(true);

        let totalDetectionsAcrossImages = 0;
        let imagesProcessed = 0;
        const totalImages = imageWorkspaceContext.imageList.length;

        const resultItems: BatchResultItem[] = imageWorkspaceContext.imageList.map((file, index) => ({
            index,
            fileName: file.name,
            detections: 0,
            imageDataUrl: null,
            processed: false
        }));

        setBatchResults({
            totalImages,
            processedImages: 0,
            totalDetections: 0,
            resultItems
        });

        const processNextImage = async (index: number) => {
            if (index >= totalImages) {
                setIsBatchProcessing(false);
                messageApi.success({
                    content: `批量检测完成！共处理 ${imagesProcessed} 张图片，发现 ${totalDetectionsAcrossImages} 个条码。`,
                    key: batchDetectionMessageKey,
                    duration: 5
                });

                setBatchResults(prev => ({
                    ...prev,
                    processedImages: imagesProcessed,
                    totalDetections: totalDetectionsAcrossImages
                }));
                setShowSaveConfirm(true);
                if (imageWorkspaceContext.setIsBatchProcessingActive) imageWorkspaceContext.setIsBatchProcessingActive(false);
                return;
            }

            const imageFile = imageWorkspaceContext.imageList[index];
            if (!imageFile) {
                processNextImage(index + 1);
                return;
            }

            imageWorkspaceContext.setCurrentImageIndex(index);

            messageApi.loading({
                content: `正在处理图片 ${index + 1} / ${totalImages} (${imageFile.name})...`,
                key: batchDetectionMessageKey,
                duration: 0
            });

            const apiPreprocessingMethod: BarcodeDetectionParams['preprocessing_method'] = 'full_scale';

            const apiParams: BarcodeDetectionParams = {
                image: imageFile,
                confidence_threshold: confidenceThreshold,
                preprocessing_method: apiPreprocessingMethod,
                model_name: selectedModelName,
            };

            try {
                const result: BarcodeDetectionResult = await detectBarcode(apiParams);
                totalDetectionsAcrossImages += result.detections.length;
                imagesProcessed++;

                if (imageWorkspaceContext.setDetectionResults) {
                    const displayableResults = result.detections.map((barcode: Barcode, idxB: number) => ({
                        box: barcode.box,
                        label: `${barcode.class_name || 'N/A'} (${barcode.confidence.toFixed(2)})`,
                        type: 'barcode' as const,
                        confidence: barcode.confidence,
                        class_name: barcode.class_name,
                        class_id: barcode.class_id,
                        displayIndex: idxB + 1,
                        id: `${barcode.class_name || barcode.class_id || 'detection'}-${barcode.box.join('-')}-${Math.random().toString(36).substring(7)}`
                    }));
                    imageWorkspaceContext.setDetectionResults(displayableResults);

                    await new Promise(resolve => requestAnimationFrame(resolve));
                }

                let imageDataUrl: string | null = null;
                if (imageWorkspaceContext.getDisplayedViewDataURL) {
                    try {
                        await new Promise(resolve => setTimeout(resolve, 50));
                        imageDataUrl = await imageWorkspaceContext.getDisplayedViewDataURL();
                    } catch (err) {
                        console.error('无法捕获图像:', err);
                    }
                }

                setBatchResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[index] = {
                        index,
                        fileName: imageFile.name,
                        detections: result.detections.length,
                        imageDataUrl,
                        processed: true
                    };

                    return {
                        ...prev,
                        processedImages: imagesProcessed,
                        totalDetections: totalDetectionsAcrossImages,
                        resultItems: updatedItems
                    };
                });

                messageApi.success({
                    content: `图片 ${imageFile.name} 处理完成，发现 ${result.detections.length} 个条码。`,
                    key: `${batchDetectionMessageKey}_individual_${index}`,
                    duration: 2
                });

            } catch (error) {
                const apiError = error as CustomApiError;
                console.error(`条码检测失败 (图片: ${imageFile.name}):`, apiError);
                imagesProcessed++;
                setBatchResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[index] = {
                        index,
                        fileName: imageFile.name,
                        detections: 0,
                        imageDataUrl: null,
                        processed: true,
                        error: apiError.message || '未知错误'
                    };

                    return {
                        ...prev,
                        processedImages: imagesProcessed,
                        resultItems: updatedItems
                    };
                });

                messageApi.error({
                    content: `图片 ${imageFile.name} 检测失败: ${apiError.message || '未知错误'}`,
                    key: `${batchDetectionMessageKey}_error_${index}`,
                    duration: 4
                });
            }

            setTimeout(() => {
                processNextImage(index + 1);
            }, batchProcessingInterval);
        };

        processNextImage(0);
    };

    const handleSaveAllResults = async () => {
        const validResults = batchResults.resultItems.filter(item => item.processed && item.imageDataUrl);

        if (validResults.length === 0) {
            messageApi.error('没有有效的结果可以保存');
            return;
        }

        setIsSavingBatch(true);
        messageApi.loading({ content: '正在准备批量保存...', key: 'batchSave', duration: 0 });

        setTimeout(async () => {
            try {
                let savedCount = 0;

                for (let i = 0; i < validResults.length; i++) {
                    const item = validResults[i];
                    if (!item.imageDataUrl) continue;

                    const fileName = item.fileName;
                    const nameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
                    const saveFileName = `${nameWithoutExtension}_result.png`;

                    const link = document.createElement('a');
                    link.href = item.imageDataUrl;
                    link.download = saveFileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    savedCount++;

                    if (i < validResults.length - 1) {
                        messageApi.loading({
                            content: `正在保存 ${i+1}/${validResults.length}: ${saveFileName}`,
                            key: 'batchSave',
                            duration: 0
                        });

                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }

                messageApi.success({
                    content: `成功保存了 ${savedCount} 个检测结果图像`,
                    key: 'batchSave',
                    duration: 3
                });
            } catch (error) {
                console.error('批量保存时出错:', error);
                messageApi.error({
                    content: '批量保存过程中发生错误',
                    key: 'batchSave',
                    duration: 3
                });
            } finally {
                setIsSavingBatch(false);
                setShowSaveConfirm(false);
            }
        }, 100);
    };

    const handleSaveCurrentResult = async () => {
        if (!imageWorkspaceContext?.getDisplayedViewDataURL) {
            messageApi.error('无法访问保存功能');
            return;
        }

        try {
            await new Promise(resolve => setTimeout(resolve, 50));
            const dataUrl = await imageWorkspaceContext.getDisplayedViewDataURL();

            if (dataUrl) {
                const currentImageIndex = imageWorkspaceContext.currentImageIndex;
                const currentFile = imageWorkspaceContext.imageList[currentImageIndex];

                let fileName = 'barcode_detection_result.png';
                if (currentFile && currentFile.name) {
                    const nameWithoutExtension = currentFile.name.substring(0, currentFile.name.lastIndexOf('.')) || currentFile.name;
                    fileName = `${nameWithoutExtension}_result.png`;
                }

                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                messageApi.success(`图像已保存为 ${fileName}`);
            } else {
                messageApi.error('无法获取图像数据');
            }
        } catch (error) {
            console.error('保存图像时出错:', error);
            messageApi.error('保存图像时发生错误');
        }
    };

    const currentImageFile = imageWorkspaceContext?.currentImageIndex !== -1
        ? imageWorkspaceContext?.imageList[imageWorkspaceContext.currentImageIndex]
        : null;

    // 单图推理按钮的tooltip提示
    const singleImageTooltipTitle = (preprocessingMethod === "roi" && !selectedRoi)
        ? "请先到菜单栏「编辑」→「截取ROI范围」选择检测区域。"
        : "";

    // 多图推理按钮的tooltip提示
    let barcodeMultiImageTooltipTitle = "";
    const hasWorkspaceContext = imageWorkspaceContext && imageWorkspaceContext.imageList;
    const imageCount = hasWorkspaceContext ? imageWorkspaceContext.imageList.length : 0;

    if (imageCount <= 1) {
        barcodeMultiImageTooltipTitle = "需要至少两张图片才能进行多图推理。请通过'打开文件夹'或者'选择多张图片'加载多张图片。";
    } else if (preprocessingMethod === "roi") {
        barcodeMultiImageTooltipTitle = "多图推理不支持'指定ROI区域'预处理方式，请选择'全图缩放'。";
    } else if (autoInferenceEnabled) {
        barcodeMultiImageTooltipTitle = "启用'切换时智能检测'时不能使用多图推理。请先关闭智能检测功能。";
    }

    const runBarcodeDetection = async () => {
        if (!imageWorkspaceContext) return Promise.resolve();
        const currentImageFileForAuto =
            imageWorkspaceContext.imageList && imageWorkspaceContext.currentImageIndex !== -1
                ? imageWorkspaceContext.imageList[imageWorkspaceContext.currentImageIndex]
                : null;

        if (!currentImageFileForAuto) {
            return Promise.resolve();
        }

        if (!selectedModelName) {
            return Promise.resolve();
        }

        try {
            // 直接使用Context状态值，不依赖表单
            await handleDetection({
                confidence_threshold: confidenceThreshold,
                preprocessing_method: preprocessingMethod,
                batch_processing_interval: batchProcessingInterval,
                auto_inference: autoInferenceEnabled,
            });
        } catch (error) {
            console.error('Auto barcode detection error:', error);
        }
    };

    useEffect(() => {
        window.runBarcodeDetection = runBarcodeDetection;

        return () => {
            delete window.runBarcodeDetection;
        };
    }, [imageWorkspaceContext, confidenceThreshold, preprocessingMethod, selectedModelName, form]);

    return (
        <Spin spinning={isInferring || isBatchProcessing || modelsLoading} tip={modelsLoading ? "加载条码模型中..." : (isBatchProcessing ? "批量条码推理中..." : "条码推理中...")}>
            {contextHolder}
            <div style={{ padding: '16px' }}>
                <Title level={4} style={{ textAlign: 'center', marginBottom: '24px' }}>条码检测</Title>
                {!currentImageFile && (
                    <Alert message="请先在左侧图像工作区加载或选择一张图片后再进行检测。" type="info" showIcon style={{ marginBottom: 16 }} />
                )}

                <Modal
                    title="多图推理完成"
                    open={showSaveConfirm}
                    onCancel={() => setShowSaveConfirm(false)}
                    footer={[
                        <Button key="cancel" onClick={() => setShowSaveConfirm(false)}>
                            不需要保存
                        </Button>,
                        <Button
                            key="saveCurrent"
                            onClick={() => {
                                handleSaveCurrentResult();
                            }}
                        >
                            仅保存当前图像
                        </Button>,
                        <Button
                            key="saveAll"
                            type="primary"
                            icon={<SaveOutlined />}
                            loading={isSavingBatch}
                            onClick={handleSaveAllResults}
                        >
                            保存所有推理结果
                        </Button>,
                    ]}
                >
                    <p>多图推理已完成！</p>
                    <p>共处理 {batchResults.processedImages}/{batchResults.totalImages} 张图片，检测到 {batchResults.totalDetections} 个条码。</p>

                    <div style={{ marginTop: '10px' }}>
                        <p><strong>您可以选择：</strong></p>
                        <ul>
                            <li><strong>仅保存当前图像</strong>：仅保存当前显示的这张图像及其检测结果</li>
                            <li><strong>保存所有推理结果</strong>：将所有成功处理的图像及检测结果保存为单独的文件</li>
                        </ul>

                        <Alert
                            message="保存提示"
                            description="批量保存时，浏览器可能会依次提示下载多个文件，请确认您的浏览器允许多文件下载。"
                            type="info"
                            showIcon
                            style={{ marginTop: '10px' }}
                        />
                    </div>
                </Modal>

                <Form
                    form={form}
                    layout="horizontal"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 14 }}
                    labelAlign="left"
                    onFinish={handleDetection}
                    initialValues={initialFormValues}
                >
                    {modelsError && !modelsLoading && (
                        <Form.Item style={{ marginBottom: '16px' }}>
                            <Alert
                                message="模型加载错误"
                                description={
                                    <div style={{ 
                                        display: 'flex', 
                                        alignItems: 'flex-start', 
                                        gap: '12px', 
                                        flexWrap: 'wrap',
                                        width: '100%'
                                    }}>
                                        <span style={{ 
                                            wordBreak: 'break-word', 
                                            flex: '1', 
                                            minWidth: '0',
                                            lineHeight: '1.5'
                                        }}>
                                            {modelsError}
                                        </span>
                                        <Button 
                                            size="small" 
                                            type="primary" 
                                            style={{ flexShrink: 0 }}
                                            onClick={() => {
                                                messageApi.loading({ content: '正在刷新模型列表...', key: 'refreshModels' });
                                                refetchBarcodeModels().then(() => {
                                                    messageApi.success({ content: '模型列表已刷新！', key: 'refreshModels' });
                                                }).catch(() => {
                                                    messageApi.error({ content: '刷新模型列表失败。', key: 'refreshModels' });
                                                });
                                            }}
                                        >
                                            重试
                                        </Button>
                                    </div>
                                }
                                type="error"
                                showIcon
                                closable
                                onClose={() => setModelsError(null)}
                            />
                        </Form.Item>
                    )}

                    <Form.Item
                        label="模型类型"
                        tooltip="选择用于条码检测的模型类型"
                    >
                        <Radio.Group
                            onChange={(e) => {
                                const newType = e.target.value;
                                setModelSelectionType(newType);
                            }}
                            value={modelSelectionType}
                            disabled={isInferring || isUploading || isBatchProcessing}
                        >
                            <Radio value="system">系统模型</Radio>
                            <Radio value="custom">自定义模型</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        label="模型选择"
                        tooltip="选择具体的条码检测模型"
                    >
                        <Space.Compact style={{ width: '100%' }}>
                            <Select
                                placeholder={
                                    modelsLoading ? "正在加载模型..." :
                                    modelSelectionType === 'system' ?
                                        (systemBarcodeModels.length === 0 ? "无可用系统模型" : "选择一个系统模型") :
                                        (customBarcodeModels.length === 0 ? "无可用自定义模型" : "选择一个自定义模型")
                                }
                                onChange={(value) => {
                                    setSelectedModelName(value);
                                }}
                                disabled={
                                    isInferring ||
                                    isUploading ||
                                    isBatchProcessing ||
                                    modelsLoading ||
                                    (modelSelectionType === 'system' && systemBarcodeModels.length === 0) ||
                                    (modelSelectionType === 'custom' && customBarcodeModels.length === 0)
                                }
                                loading={modelsLoading}
                                value={selectedModelName}
                                style={{ width: '100%' }}
                            >
                                {(modelSelectionType === 'system' ? systemBarcodeModels : customBarcodeModels).map((model: VisionModel) => (
                                    <Option key={model.name} value={model.name}>
                                        {model.description || model.name}
                                        {modelSelectionType === 'custom' && model.uploaded_at && ` (${new Date(model.uploaded_at).toLocaleDateString()})`}
                                    </Option>
                                ))}
                            </Select>
                            <Tooltip title="刷新条码模型列表">
                                <Button
                                    icon={<SyncOutlined />}
                                    onClick={() => {
                                        messageApi.loading({ content: '正在刷新模型列表...', key: 'refreshModels' });
                                        refetchBarcodeModels().then(() => {
                                            messageApi.success({ content: '模型列表已刷新！', key: 'refreshModels' });
                                        }).catch(() => {
                                            messageApi.error({ content: '刷新模型列表失败。', key: 'refreshModels' });
                                        });
                                    }}
                                    loading={modelsLoading && fetchedModelsData !== undefined}
                                    disabled={isUploading || isInferring || isBatchProcessing || (reactQueryModelsLoadingInitial && !fetchedModelsData)}
                                />
                            </Tooltip>
                        </Space.Compact>
                    </Form.Item>

                    {modelSelectionType === 'custom' && (
                        <React.Fragment>
                            <Form.Item
                                label="上传新模型"
                                name="model_file_upload"
                                help='选择.pt文件后点下方"上传选中文件"按钮。检测时需从上方列表选择.'
                            >
                                <Upload
                                    name="modelFile"
                                    listType="text"
                                    maxCount={1}
                                    fileList={customModelFile ? [customModelFile] : []}
                                    beforeUpload={(file) => {
                                        const isPt = file.name.toLowerCase().endsWith('.pt');
                                        if (!isPt) {
                                            messageApi.error(`${file.name} 不是一个 .pt 文件`);
                                            return Upload.LIST_IGNORE;
                                        }
                                        setCustomModelFile(file);
                                        return false;
                                    }}
                                    onRemove={() => {
                                        setCustomModelFile(null);
                                    }}
                                    accept=".pt"
                                    disabled={isInferring || isUploading || isBatchProcessing}
                                >
                                    <Button icon={<UploadOutlined />} disabled={isInferring || isUploading || isBatchProcessing}>选择 .pt 模型文件</Button>
                                </Upload>
                            </Form.Item>
                            <Form.Item wrapperCol={{ offset: 8, span: 14 }}>
                                <Button
                                    type="default"
                                    onClick={handleCustomModelUpload}
                                    loading={isUploading}
                                    disabled={!customModelFile || isInferring || isUploading || isBatchProcessing}
                                    style={{ width: '100%' }}
                                >
                                    {isUploading ? '正在上传...' : '上传选中文件'}
                                </Button>
                            </Form.Item>
                        </React.Fragment>
                    )}

                    <Form.Item
                        label="预处理方式"
                        name="preprocessing_method"
                        rules={[{ required: true, message: '请选择预处理方式!' }]}
                        tooltip={
                            <div>
                                <div>全图缩放：将整张图片缩放后送入模型检测，适用于大部分场景</div>
                                <div>指定ROI区域：仅对用户选择的区域进行检测，可提高检测精度和速度</div>
                            </div>
                        }
                    >
                        <Select
                            onChange={(value: PreprocessingMethod) => {
                                setPreprocessingMethod(value);
                            }}
                            value={preprocessingMethod}
                            disabled={isInferring || isUploading || isBatchProcessing}
                        >
                            <Option value="full_scale">全图缩放</Option>
                            <Option value="roi">指定ROI区域（自适应缩放）</Option>
                        </Select>
                    </Form.Item>

                    {preprocessingMethod === "roi" && (
                        <Alert
                            message="ROI区域预处理方式"
                            description={
                                !selectedRoi
                                    ? "请先到菜单栏「编辑」→「截取ROI范围」选择检测区域，然后再进行推理。"
                                    : "已选择ROI区域，将仅对选定区域进行条码检测。如需重新选择，请到菜单栏「编辑」→「截取ROI范围」。"
                            }
                            type={!selectedRoi ? "warning" : "info"}
                            showIcon
                            style={{ marginBottom: 16 }}
                        />
                    )}

                    <Form.Item
                        label="置信度阈值"
                        name="confidence_threshold"
                        rules={[{ required: true, message: '请输入置信度阈值!' }]}                    >
                        <Row gutter={8}>
                            <Col span={16}>
                                <Slider
                                    min={0.01}
                                    max={1}
                                    step={0.01}
                                    onChange={(value: number) => {
                                        setConfidenceThreshold(value);
                                        form.setFieldsValue({ confidence_threshold: value });
                                    }}
                                    value={confidenceThreshold}
                                    disabled={isInferring || isUploading || isBatchProcessing}
                                />
                            </Col>
                            <Col span={8}>
                                <InputNumber
                                    min={0.01}
                                    max={1}
                                    step={0.01}
                                    style={{ width: '100%' }}
                                    onChange={(value: number | null ) => {
                                        if (value !== null) {
                                            setConfidenceThreshold(value);
                                            form.setFieldsValue({ confidence_threshold: value });
                                        }
                                    }}
                                    value={confidenceThreshold}
                                    disabled={isInferring || isUploading || isBatchProcessing}
                                />
                            </Col>
                        </Row>
                    </Form.Item>

                    <Form.Item
                        label="多图推理间隔"
                        name="batch_processing_interval"
                        tooltip="多图推理时每张图片处理之间的等待时间（毫秒）"
                        rules={[{ required: true, message: '请设置多图推理间隔!' }]}
                    >
                        <Row gutter={8} align="middle">
                            <Col span={12}>
                                <Slider
                                    min={0}
                                    max={5000}
                                    step={100}
                                    onChange={(value: number) => {
                                        setBatchProcessingInterval(value);
                                        form.setFieldsValue({ batch_processing_interval: value });
                                    }}
                                    value={batchProcessingInterval}
                                    disabled={isInferring || isUploading || isBatchProcessing}
                                    marks={{
                                        1000: '1s',
                                        3000: '3s',
                                        5000: '5s'
                                    }}
                                    style={{ marginRight: '8px' }}
                                />
                            </Col>
                            <Col span={12}>
                                <InputNumber
                                    min={0}
                                    max={5000}
                                    step={100}
                                    style={{ width: '100%' }}
                                    onChange={(value: number | null) => {
                                        if (value !== null) {
                                            setBatchProcessingInterval(value);
                                            form.setFieldsValue({ batch_processing_interval: value });
                                        }
                                    }}
                                    value={batchProcessingInterval}
                                    disabled={isInferring || isUploading || isBatchProcessing}
                                    addonAfter="ms"
                                    size="small"
                                    controls={false}
                                />
                            </Col>
                        </Row>
                    </Form.Item>

                    <Divider style={{ margin: '12px 0' }} />

                    <Form.Item
                        label="切换时智能检测"
                        name="auto_inference"
                        valuePropName="checked"
                        tooltip="启用后，切换图片时自动执行条码检测，检测完成前不可继续切换"
                    >
                        <Switch
                            checked={autoInferenceEnabled}
                            onChange={(checked) => setAutoInferenceEnabled(checked)}
                            disabled={
                                isInferring ||
                                isUploading ||
                                isBatchProcessing ||
                                preprocessingMethod === "roi" ||
                                !imageWorkspaceContext ||
                                !imageWorkspaceContext.imageList ||
                                imageWorkspaceContext.imageList.length <= 1
                            }
                        />
                    </Form.Item>

                    <Form.Item wrapperCol={{ offset: 0, span: 24 }} style={{ marginTop: '32px' }}>
                        <Space style={{ width: '100%', justifyContent: 'center' }}>
                            <Tooltip title={singleImageTooltipTitle}>
                                <span>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        disabled={
                                            !currentImageFile ||
                                            isUploading ||
                                            isBatchProcessing ||
                                            modelsLoading ||
                                            (!systemBarcodeModels.length && !customBarcodeModels.length) ||
                                            (preprocessingMethod === "roi" && !selectedRoi)
                                        }
                                    >
                                        单图推理
                                    </Button>
                                </span>
                            </Tooltip>
                            <Tooltip title={barcodeMultiImageTooltipTitle}>
                              <span>
                                <Button
                                    type="default"
                                    onClick={handleMultiImageDetection}
                                    disabled={
                                        !imageWorkspaceContext ||
                                        !imageWorkspaceContext.imageList ||
                                        imageWorkspaceContext.imageList.length <= 1 ||
                                        isInferring ||
                                        isUploading ||
                                        modelsLoading || (!systemBarcodeModels.length && !customBarcodeModels.length) ||
                                        preprocessingMethod === "roi" ||
                                        autoInferenceEnabled
                                    }
                                >
                                    多图推理
                                </Button>
                              </span>
                            </Tooltip>
                        </Space>
                    </Form.Item>
                </Form>
            </div>
        </Spin>
    );
};

export default BarcodeDetectionPanel;