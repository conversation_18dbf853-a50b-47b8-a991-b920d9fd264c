#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试后端特征匹配功能脚本
测试预测器与run.py逻辑的一致性
"""

import os
import sys
import cv2
import json
import numpy as np
import requests
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'vision_app'))

# 导入后端预测器
from vision_app.feature_matching_onnxruntime_model_predictor import (
    ModelBasedFeatureMatcher, 
    FeatureMatchingPredictor,
    grid_nms,
    extract_keypoints
)

# 导入run.py的函数进行对比
import Backend_Django.scripts.feature_point_onnx as run_module
import onnxruntime

def test_grid_nms_consistency():
    """测试grid_nms函数的一致性"""
    print("🔍 测试 grid_nms 函数一致性...")
    
    # 创建测试数据
    test_scores = np.random.rand(1, 64, 64) * 255
    
    # 测试run.py的grid_nms
    run_result = run_module.grid_nms(test_scores, grid=4, threshold=8)
    
    # 测试后端的grid_nms
    backend_result = grid_nms(test_scores, grid=4, threshold=8)
    
    # 比较结果
    is_same = np.array_equal(run_result, backend_result)
    print(f"grid_nms结果一致性: {'✅ 一致' if is_same else '❌ 不一致'}")
    
    if not is_same:
        print(f"run.py结果形状: {run_result.shape}")
        print(f"后端结果形状: {backend_result.shape}")
        print(f"差异统计:")
        diff = np.abs(run_result - backend_result)
        print(f"  最大差异: {np.max(diff)}")
        print(f"  平均差异: {np.mean(diff)}")
    
    return is_same

def test_feature_extraction_consistency():
    """测试特征提取的一致性"""
    print("\n🔍 测试特征提取一致性...")
    
    # 检查模型文件是否存在
    model_path = "superpoint.onnx"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    # 检查测试图片是否存在
    test_image_path = "sample4/1.bmp"
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图片不存在: {test_image_path}")
        return False
    
    try:
        # 使用run.py的inference函数
        model = onnxruntime.InferenceSession(model_path)
        run_keypoints = run_module.inference(model, test_image_path, N=100)
        
        # 使用后端预测器
        predictor = FeatureMatchingPredictor(model_path)
        backend_keypoints = predictor.inference(test_image_path, N=100)
        
        print(f"run.py提取关键点数量: {len(run_keypoints)}")
        print(f"后端预测器提取关键点数量: {len(backend_keypoints)}")
        
        # 比较前几个关键点的坐标和分数
        is_consistent = True
        for i in range(min(5, len(run_keypoints), len(backend_keypoints))):
            run_kp = run_keypoints[i]
            backend_kp = backend_keypoints[i]
            
            coord_diff = np.abs(np.array(run_kp["coord"]) - np.array(backend_kp["coord"]))
            score_diff = abs(run_kp["score"] - backend_kp["score"])
            
            print(f"关键点 {i+1}:")
            print(f"  run.py:    坐标{run_kp['coord']}, 分数{run_kp['score']:.2f}")
            print(f"  后端:      坐标{backend_kp['coord']}, 分数{backend_kp['score']:.2f}")
            print(f"  坐标差异:  {coord_diff}, 分数差异: {score_diff:.4f}")
            
            if np.max(coord_diff) > 2 or score_diff > 1.0:
                is_consistent = False
        
        print(f"特征提取一致性: {'✅ 基本一致' if is_consistent else '❌ 存在差异'}")
        return is_consistent
        
    except Exception as e:
        print(f"❌ 特征提取测试失败: {str(e)}")
        return False

def test_matching_consistency():
    """测试完整匹配流程的一致性"""
    print("\n🔍 测试完整匹配流程一致性...")
    
    # 检查所需文件
    model_path = "superpoint.onnx"
    image1_path = "sample4/1.bmp"
    image2_path = "sample4/3.bmp"
    
    for path in [model_path, image1_path, image2_path]:
        if not os.path.exists(path):
            print(f"❌ 文件不存在: {path}")
            return False
    
    try:
        # 1. 使用run.py的完整流程
        print("执行run.py流程...")
        model = onnxruntime.InferenceSession(model_path)
        
        template_keypoints = run_module.inference(model, image1_path, N=100)
        target_keypoints = run_module.inference(model, image2_path, N=100)
        
        # run.py的匹配逻辑
        match_params = [
            {"max_distance": 0.3, "ratio_threshold": 0.8, "name": "严格匹配"},
            {"max_distance": 0.5, "ratio_threshold": 0.8, "name": "中等匹配"},
            {"max_distance": 0.7, "ratio_threshold": 0.9, "name": "宽松匹配"},
        ]
        
        best_matches = []
        for param in match_params:
            matches = run_module.match_descriptors(template_keypoints, target_keypoints, 
                                                 max_distance=param['max_distance'])
            filtered_matches = run_module.filter_matches(matches, ratio_threshold=param['ratio_threshold'])
            if len(filtered_matches) > len(best_matches):
                best_matches = filtered_matches
        
        homography_run, ransac_matches_run = run_module.ransac_homography(
            best_matches, template_keypoints, target_keypoints, 
            threshold=5.0, max_iterations=2000, min_inliers=4
        )
        
        print(f"run.py结果: {len(ransac_matches_run)} 个RANSAC匹配")
        
        # 2. 使用后端预测器
        print("执行后端预测器流程...")
        predictor = ModelBasedFeatureMatcher(model_path)
        
        # 读取图片
        template_image = cv2.imread(image1_path)
        target_image = cv2.imread(image2_path)
        
        # 执行预测
        result = predictor.predict(
            template_image=template_image,
            target_image=target_image,
            template_roi=None,
            keypoints_count=100,
            nms_grid_size=4,
            nms_threshold=8.0/255.0,
            match_ratio_threshold=0.8,
            min_match_count=4,
            ransac_threshold=5.0
        )
        
        print(f"后端预测器结果: {result['data']['match_count']} 个匹配")
        print(f"预测器状态: {result['status']}")
        
        # 比较结果
        run_success = homography_run is not None
        backend_success = result['status'] == 'success'
        
        print(f"\n📊 结果对比:")
        print(f"run.py成功: {'✅' if run_success else '❌'}")
        print(f"后端成功: {'✅' if backend_success else '❌'}")
        
        if run_success and backend_success:
            run_count = len(ransac_matches_run)
            backend_count = result['data']['match_count']
            count_diff = abs(run_count - backend_count)
            
            print(f"匹配数量差异: {count_diff}")
            print(f"匹配结果一致性: {'✅ 基本一致' if count_diff <= 5 else '❌ 存在较大差异'}")
            
            return count_diff <= 5
        else:
            print("❌ 匹配流程存在失败情况")
            return False
            
    except Exception as e:
        print(f"❌ 匹配测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """测试API端点功能"""
    print("\n🔍 测试API端点...")
    
    # 这里可以添加对Django API的测试
    # 需要启动Django服务器才能测试
    print("⚠️  API端点测试需要启动Django服务器")
    print("可以使用以下命令启动服务器: python manage.py runserver")
    print("然后访问: http://localhost:8000/vision_app/detect/feature-matching/model/")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试后端特征匹配功能...")
    print("=" * 60)
    
    # 切换到scripts目录
    os.chdir(os.path.dirname(__file__))
    
    test_results = []
    
    # 1. 测试grid_nms一致性
    test_results.append(("grid_nms一致性", test_grid_nms_consistency()))
    
    # 2. 测试特征提取一致性  
    test_results.append(("特征提取一致性", test_feature_extraction_consistency()))
    
    # 3. 测试完整匹配流程
    test_results.append(("匹配流程一致性", test_matching_consistency()))
    
    # 4. 测试API端点
    test_results.append(("API端点", test_api_endpoints()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！后端功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 