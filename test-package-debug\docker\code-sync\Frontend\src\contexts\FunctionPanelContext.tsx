import React, { createContext, useState, useContext, ReactNode } from 'react';

// --- Types ---
type SelectedKey = React.Key; // Ant Design Tree key can be string or number

export interface SelectedFunction {
  key: SelectedKey;
  name: string;
  // isLeaf?: boolean; // 根据 FunctionTree.tsx 的用法，isLeaf 是在节点上，而不是在 context 中传递
}

interface FunctionPanelContextType {
  selectedFunction: SelectedFunction | null;
  setSelectedFunction: (func: SelectedFunction | null) => void;
}

// --- Context Creation ---
const FunctionPanelContext = createContext<FunctionPanelContextType | undefined>(undefined);

// --- Provider Component ---
interface FunctionPanelProviderProps {
  children: ReactNode;
}

export const FunctionPanelProvider: React.FC<FunctionPanelProviderProps> = ({ children }) => {
  const [selectedFunction, setSelectedFunction] = useState<SelectedFunction | null>(null);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => ({
    selectedFunction,
    setSelectedFunction,
  }), [selectedFunction]);

  return (
    <FunctionPanelContext.Provider value={contextValue}>
      {children}
    </FunctionPanelContext.Provider>
  );
};

// --- Hook for consuming context ---
export const useFunctionPanel = (): FunctionPanelContextType => {
  const context = useContext(FunctionPanelContext);
  if (context === undefined) {
    throw new Error('useFunctionPanel must be used within a FunctionPanelProvider');
  }
  return context;
}; 