#ifndef _IMAGE_LOADER_H
#define _IMAGE_LOADER_H

//-----------------------------------------------------------------------------
//  Includes

#include <cctype>
#include <cstddef>
#include <cstdio>
#include <dirent.h>
#include <linux/stat.h>
#include <memory>
#include <sys/stat.h>

#include <stack>
#include <string>
#include <vector>
#include <algorithm>

#ifdef INCLUDE_OPENCV
#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/imgcodecs.hpp"
#endif /* #ifdef INCLUDE_OPENCV */

#include "AIEngineCommon.h"
#include "Log.hpp"
//-----------------------------------------------------------------------------
//  Definitions

#ifndef DIR_SEPARATOR
#define DIR_SEPARATOR       '/'     // 文件分隔符
#endif
//-----------------------------------------------------------------------------
//  Declarations

class ImageLoader
{
public:
    typedef struct {
        std::unique_ptr<char[]> img_data; // 图片数据
        int img_height; // 图片高度
        int img_width; // 图片宽度
        color_format_t color_format; // 颜色格式
        bit_depth_t bit_depth; // 图片位深度
    }LoadedImage; // 加载完的图片数据信息结构体

    /**
     * @brief    
     *           获取图片列表
     *           
     * @param    search_path:   图片搜索路径，此接口会获取该路径下所有支持读取的图片的路径
     *           
     * @retval   图片列表，列表中每个元素都是某一张图片的文件路径
     *           
     * @date     2024-04-22 Created by HuangJP
     */
    static std::vector<std::string> Get_Image_Path_List(const char *search_path)
    {
        std::vector<std::string> img_path_list;
        std::stack<std::string> search_paths;

        // 添加搜索路径
        search_paths.push(search_path);

        // 在搜索路径下搜索图片
        while (!search_paths.empty())
        {
            // 获取搜索路径
            std::string path = search_paths.top();
            search_paths.pop();

            // 打开路径
            DIR *dir = opendir(path.c_str());
            if (dir == nullptr)
            {
                LOGE("Failed to open dir");
                LOGD("Open dir failure: %s", path.c_str());
                continue;
            }

            // 逐个读路径下的文件命名
            struct dirent *dr;
            while ((dr = readdir(dir)) != nullptr)
            {
                std::string file_name(dr->d_name); // 获取文件命名
                std::string file_path = path + DIR_SEPARATOR + file_name; // 获取文件路径

                // 判断是否是目录文件
                struct stat st;
                stat(file_path.c_str(), &st);
                if (S_ISDIR(st.st_mode))
                {
                    // 跳过当前路径和上一级路径
                    if (file_name == "."
                        || file_name == "..")
                    {
                        continue;
                    }

                    // 添加路径到搜索路径
                    search_paths.push(file_path);
                    continue;
                }

                // 获取文件后缀
                std::string suffix = _get_file_suffix(file_path);
                std::transform(suffix.begin(), suffix.end(), suffix.begin(), tolower); // 将后缀转换成小写
                // 将支持读取的图片添加到图片列表中
                if (suffix == "bmp"
                    || suffix == "png"
                    || suffix == "jpg"
                    || suffix == "raw")
                {
                    img_path_list.push_back(file_path);
                }
            }

            closedir(dir); // 关闭打开的路径
        }

        return img_path_list;
    }

    /**
     * @brief    
     *           加载图片数据及信息
     *           
     * @param    img_path:      要加载的图片的文件路径
     * @param    loaded_image:  输出的加载图片数据
     * @param    color_format:  图片颜色格式，不指定时默认为COLOR_FORMAT_GRAY
     *           
     * @retval   错误码
     *           
     * @date     2024-04-22 Created by HuangJP
     */
    static int Load_Image(std::string img_path, LoadedImage &loaded_image, color_format_t color_format = COLOR_FORMAT_GRAY)
    {
        // 重置图片指针，释放原先申请的内存
        loaded_image.img_data.reset();

        // 获取图片后缀
        std::string suffix = _get_file_suffix(img_path);
        std::transform(suffix.begin(), suffix.end(), suffix.begin(), tolower); // 将后缀转换成小写

        // 根据图片后缀采用不同的图片加载方式
        if (suffix == "raw") // RAW
        {
            // 获取图片尺寸
            std::string file_directory = _get_file_directory(img_path);
            if (sscanf(file_directory.c_str(), "%*[^-]-%dx%dx%d", &loaded_image.img_width, &loaded_image.img_height, &loaded_image.bit_depth) != 3)
            {
                LOGE("Unable to retrieve the image size from the directory");
                return AIENGINE_INVALID_PARAM;
            }
            loaded_image.color_format = color_format; // 设置图片颜色格式

            // 获取每像素占用字节数
            int bytes_per_pixel = 0;
            switch (loaded_image.bit_depth)
            {
                case BIT_DEPTH_RAW8:
                    bytes_per_pixel = 1;
                    break;
                case BIT_DEPTH_RAW10:
                case BIT_DEPTH_RAW12:
                    bytes_per_pixel = 2;
                    break;
                default:
                    LOGE("Unsupported image bit depth");
                    return AIENGINE_INVALID_PARAM;
            }

            // 申请图片内存
            int img_size = loaded_image.img_width * loaded_image.img_height * bytes_per_pixel;
            if (img_size < 0)
            {
                LOGE("Error: The image size cannot be less than zero.");
                return AIENGINE_INVALID_PARAM;
            }
            loaded_image.img_data = std::unique_ptr<char[]>(new char[img_size]);

            // 读图片
            FILE *file = fopen(img_path.c_str(), "r");
            if (file == nullptr)
            {
                LOGE("Unable to read image: Failed to open file.");
                return AIENGINE_OPEN_FILE_ERROR;
            }
            fread(loaded_image.img_data.get(), img_size, 1, file);
            fclose(file);
        }
        else // 其它类型的图片，默认用OpenCV加载
        {
#ifdef INCLUDE_OPENCV
            // 根据要求的颜色格式读取图片
            cv::Mat cv_img;
            switch (color_format)
            {
                case COLOR_FORMAT_GRAY:
                    cv_img = cv::imread(img_path, cv::IMREAD_GRAYSCALE);
                    loaded_image.bit_depth = BIT_DEPTH_RAW8; // 默认读取的图片都是RAW8图像
                    loaded_image.color_format = color_format;
                    break;
                default:
                    LOGE("Unsupported color format");
                    return AIENGINE_INVALID_PARAM;
            }

            // 获取图片尺寸
            loaded_image.img_width = cv_img.cols;
            loaded_image.img_height = cv_img.rows;
            int bytes_per_pixel = cv_img.channels();

            // 申请图片内存
            int img_size = loaded_image.img_width * loaded_image.img_height * bytes_per_pixel;
            char *img_data = new char[img_size];
            loaded_image.img_data = std::unique_ptr<char[]>(img_data);

            // 拷贝图片
            memcpy(img_data, cv_img.data, img_size);
#else /* #ifdef INCLUDE_OPENCV */
            LOGE("Unable to read image: Unsupported image type");
            return AIENGINE_INVALID_PARAM;
#endif /* #ifdef INCLUDE_OPENCV */
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           填充图片信息结构体
     *           
     * @param    loaded_image:  输出的加载图片数据
     * @param    image_info:    要填充的图片信息结构体
     *           
     * @date     2024-05-09 Created by HuangJP
     */
    static void Fill_Image_Info(LoadedImage &loaded_image, ImageInfo &image_info)
    {
        image_info.img_data_pt = loaded_image.img_data.get();
        image_info.img_height = loaded_image.img_height;
        image_info.img_width = loaded_image.img_width;
        image_info.bit_depth = loaded_image.bit_depth;
        image_info.color_format = loaded_image.color_format;
    }

private:
    /**
     * @brief    
     *           获取文件后缀
     *           
     * @param    file_path:     文件路径
     *           
     * @retval   文件后缀
     *           
     * @date     2024-04-22 Created by HuangJP
     */
    static inline std::string _get_file_suffix(std::string file_path)
    {
        // 文件后缀，默认值为空字符串
        std::string suffix("");

        // 获取到最后一个'.'的位置
        size_t pos = file_path.find_last_of('.');
        if (pos == std::string::npos)
        {
            return suffix; // 当路径中没有'.'时，返回空字符串
        }

        // 获取文件后缀
        suffix = file_path.substr(pos + 1);
        return suffix;
    }

    /**
     * @brief    
     *           获取文件所在目录
     *           
     * @param    file_path:     文件路径
     *           
     * @retval   文件所在目录
     *           
     * @date     2024-05-09 Created by HuangJP
     */
    static inline std::string _get_file_directory(std::string file_path)
    {
        std::string file_directory("");

        // 获取最后一个分隔符所在位置
        size_t pos = file_path.find_last_of(DIR_SEPARATOR);
        if (pos == std::string::npos)
        {
            return file_directory;
        }

        // 获取最后一个分隔符之前的字符串
        file_directory = file_path.substr(0, pos);
        
        // 获取倒数第二个分隔符所在位置
        pos = file_directory.find_last_of(DIR_SEPARATOR);
        if (pos == std::string::npos)
        {
            return file_directory;
        }

        // 获取文件所在目录
        file_directory = file_directory.substr(pos + 1);
        return file_directory;
    }
};
#endif
//-----------------------------------------------------------------------------
//  End of file