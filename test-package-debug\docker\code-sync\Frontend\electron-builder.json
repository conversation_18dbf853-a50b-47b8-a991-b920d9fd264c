﻿{
  "appId": "com.aivision.app",
  "productName": "AI Vision App",
  "directories": {
    "output": "release"
  },
  "files": [
    "dist/**/*",
    "electron/**/*",
    "package.json"
  ],
  "extraResources": [
    {
      "from": "../Backend_Django",
      "to": "Backend_Django",
      "filter": ["**/*", "!**/__pycache__/**", "!**/*.pyc", "!**/.git/**"]
    }
  ],
  "win": {
    "icon": "public/favicon.ico",
    "target": [
      {
        "target": "nsis",
        "arch": ["x64"]
      }
    ]
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "shortcutName": "AI Vision App"
  },
  "mac": {
    "icon": "public/favicon.ico",
    "target": ["dmg"]
  },
  "linux": {
    "icon": "public/favicon.ico",
    "target": ["AppImage"],
    "category": "Development"
  }
}
