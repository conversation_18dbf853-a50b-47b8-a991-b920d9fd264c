# 更新日志

记录更新内容。

## 2025年01月08日

**V1.0.10** @HuangJP 增加已有工具支持的传入参数。

更新LatentDiffusion工具。
- 工具初始化函数"prompt_file"参数，用于指定提示词文件命名。
- 新增"_read_prompt_file"函数，用于从文件中读取提示词。
- "Perspective_Transform_And_Crop"函数新增"save_label"参数，指定该参数时，会将数据标签（包括提示词、条件图等）写入到指定文件中。

更新YOLO工具。
- "Resize_Image"函数新增"interpolation_method"参数，该参数用于选择resize时采用的插值方法。

## 2024年12月27日

**V1.0.9** @HuangJP 更新YOLO数据增强工具。

- 更新"Distribute_Data_Set"函数，支持只保存标签功能：
    - 函数新增传入参数"only_label"，该参数指定为True时，分配好的数据集仅保存标签而不保存图片。
    - 调整标签保存函数"_save_label_and_image"，支持只保存标签功能。
- 支持数据溯源功能：
    - 为了方便追踪数据来源，在labelme标签中增加了srcPath成员，该成员是一个指向原始图片绝对路径的字符串。
    - 同步修改各个功能函数，在创建labelme标签时记录srcPath成员。
    - "Labelme_To_YOLO"函数中，在保存YOLO标签时，同步会创建一个traces目录，该目录中的txt文件内容指向了数据来源。

## 2024年12月17日

**V1.0.8** @HuangJP 新增LatentDiffusion数据增强工具。

## 2024年11月28日

**V1.0.7** @HuangJP 修复YOLO数据增强工具中旋转增强存在的问题。

- 描述：
    - 当使用旋转增强生成多张图片时，除生成的第一张图片的标签外，其它图片的标签坐标均是错误的。
- 原因：
    - 旋转标签坐标时没有对坐标做深度拷贝，导致后续每次坐标旋转都是在前一次旋转的结果上进行的，这就导致除第一张图像外，其它图像的坐标均存在问题。
- 修复：
    - 旋转标签坐标前先做一次深度拷贝，在拷贝上旋转坐标，避免多次旋转的结果相互影响。

## 2024年11月27日

**V1.0.6** @HuangJP 优化YOLO数据增强工具错误提示。

- 在执行Crop_Ref方法时如果遇到不合法的多边形，会打印出存在问题的标签，并及时退出脚本执行。

**V1.0.5** @HuangJP 调整YOLO数据增强工具，旋转方法支持指定旋转范围。

**V1.0.4** @HuangJP 改进YOLO数据增强工具。

- 改进DatasetYOLO的Crop_Ref方法。支持裁剪任意形状的多边形。
    - 引入shapely库替代自己写的Geometry库，用于计算多边形面积、获取两个多边形的交集区域。
    - 对于新引入的库，请执行`pip install -r requirements.txt`进行安装。

**V1.0.3** @HuangJP 改进YOLO数据增强工具。

- 改进DatasetYOLO的Crop_Ref方法。新增mode参数用于改变裁剪行为：
    - "fix"模式下，采用与原来相同的裁剪方法，裁剪得到的图片尺寸与设置的目标尺寸一致。
    - "dilate"模式下，会基于实例尺寸外扩目标尺寸，裁剪得到的图片尺寸等于实例尺寸加上目标尺寸。

## 2024年11月26日

**V1.0.2** @HuangJP 更新YOLO数据增强工具。

- 优化DatasetYOLO的Crop_Ref方法。目标受到裁剪的情况下，不再直接舍弃当前结果，而是转换裁剪后的目标坐标。
- 修复Geometry模块中“获取两个多边形的交集区域”函数运行报错问题。同时增加“计算多边形面积”函数，用于获取凸多边形的面积大小。

## 2024年11月14日

**V1.0.1** @HuangJP 更新OCR数据增强工具。

- DatasetOCR更新：
    - 创建DatasetOCR对象时，支持指定保存图片的后缀名。
    - 新增“仿射变换”、“添加阴影”、“添加噪声”三种数据增强方法。
    - 使用"_read_original_label"方法读取标签时，允许通过shuffle参数打乱标签顺序。
    - 调整"_write_rec_label"方法，将字符内容相同的图片归纳至同一个列表。
    - 新增"_generate_dataset_sample_based"方法，该方法基于样本生成数据集标签。
- ocrRec更新：
    - 将ocrRec中的"Rotate_And_Crop"方法改进为多进程执行，减少执行耗时。
- ocrDet更新：
    - 新增"Crop_Image"方法，用于从原图中裁剪出目标区域的图片。

## 2024年10月25日

**V1.0.0** @HuangJP 初始化仓库。

添加YOLO数据增强脚本。
- 上传YOLO数据增强脚本YOLO.py。
- 上传脚本说明文档docs/YOLO.md。

添加PaddleOCR数据增强脚本。
- 上传PaddleOCR数据增强脚本OCR.py。
- 上传脚本说明文档docs/OCR.md。