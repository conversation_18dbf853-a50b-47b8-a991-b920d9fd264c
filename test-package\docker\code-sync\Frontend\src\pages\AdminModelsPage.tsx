import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Space,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  DatabaseOutlined,
  EyeOutlined,
  DeleteOutlined,
  PlusOutlined,
  UploadOutlined,
  ReloadOutlined,
  CloudUploadOutlined,
  AppstoreOutlined,
  ExclamationCircleOutlined,
  EditOutlined
} from '@ant-design/icons';
import type { UploadFile, RcFile } from 'antd/es/upload/interface';
import {
  getGroupedVisionModels,
  deleteModels,
  uploadModel,
  updateModel,
  type VisionModel,
  type ModelDeleteParams,
  type ModelUploadParams,
  type ModelUpdateParams,
  type GroupedVisionModelsApiResponse
} from '../services/api';

// 新增：OCR模型集合接口
interface OcrModelCollection {
  id: string; // 使用collection_name作为唯一标识
  collection_name: string;
  detection_model?: VisionModel;
  recognition_model?: VisionModel;
  model_type: 'ocr';
  is_system_model: boolean;
  version?: string | null;
  description?: string | null;
  uploaded_at: string;
}

// 扩展的模型类型，包含OCR集合
type ExtendedModel = VisionModel | OcrModelCollection;

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

// 工具函数：将OCR模型按collection_name分组
const groupOcrModels = (ocrModels: VisionModel[]): OcrModelCollection[] => {
  const collections = new Map<string, OcrModelCollection>();

  ocrModels.forEach(model => {
    const collectionKey = model.ocr_collection_name || model.name;

    if (!collections.has(collectionKey)) {
      collections.set(collectionKey, {
        id: collectionKey,
        collection_name: collectionKey,
        model_type: 'ocr',
        is_system_model: model.is_system_model,
        version: model.version,
        description: model.description,
        uploaded_at: model.uploaded_at
      });
    }

    const collection = collections.get(collectionKey)!;
    if (model.ocr_role === 'detection') {
      collection.detection_model = model;
    } else if (model.ocr_role === 'recognition') {
      collection.recognition_model = model;
    }

    // 更新集合的元数据（使用最新的模型信息）
    if (model.uploaded_at > collection.uploaded_at) {
      collection.uploaded_at = model.uploaded_at;
      collection.version = model.version;
      collection.description = model.description;
    }
  });

  return Array.from(collections.values());
};

/**
 * 模型管理页面
 * 实现完整的模型管理功能：列表查看、上传、删除
 */
const AdminModelsPage: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [models, setModels] = useState<ExtendedModel[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modelScope, setModelScope] = useState<'system' | 'custom' | 'all'>('all');
  const [modelTypeFilter, setModelTypeFilter] = useState<string>('all');

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(6);

  // 上传模态框状态
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadForm] = Form.useForm();
  const [uploadFileList, setUploadFileList] = useState<UploadFile[]>([]);

  // 编辑模态框状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editing, setEditing] = useState(false);
  const [editForm] = Form.useForm();
  const [editingModel, setEditingModel] = useState<VisionModel | null>(null);

  // OCR集合编辑模态框状态
  const [editOcrModalVisible, setEditOcrModalVisible] = useState(false);
  const [editingOcrCollection, setEditingOcrCollection] = useState<OcrModelCollection | null>(null);
  const [editOcrForm] = Form.useForm();

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    system: 0,
    custom: 0,
    barcode: 0,
    ocr: 0,
    ocr_collections: 0, // 新增：OCR集合数量
    ai_restored: 0,
    feature_matching: 0 // 新增：特征匹配模型数量
  });

  // 获取模型列表
  const fetchModels = async () => {
    try {
      setLoading(true);
      const response: GroupedVisionModelsApiResponse = await getGroupedVisionModels(modelScope);

      // 将分组数据转换为平面数组
      const allModels: VisionModel[] = [];
      Object.values(response).forEach(modelGroup => {
        allModels.push(...modelGroup);
      });

      // 处理OCR模型集合
      const processedModels: ExtendedModel[] = [];
      const ocrModels = allModels.filter(model => model.model_type === 'ocr');
      const nonOcrModels = allModels.filter(model => model.model_type !== 'ocr');

      // 将OCR模型按collection_name分组
      const ocrCollections = groupOcrModels(ocrModels);

      // 根据类型过滤决定显示什么
      if (modelTypeFilter === 'all') {
        // 显示所有：非OCR模型 + OCR集合
        processedModels.push(...nonOcrModels, ...ocrCollections);
      } else if (modelTypeFilter === 'ocr') {
        // 只显示OCR集合
        processedModels.push(...ocrCollections);
      } else {
        // 显示指定类型的非OCR模型
        processedModels.push(...nonOcrModels.filter(model => model.model_type === modelTypeFilter));
      }

      setModels(processedModels);

      // 计算统计数据（基于原始模型数据）
      const newStats = {
        total: allModels.length,
        system: allModels.filter(m => m.is_system_model).length,
        custom: allModels.filter(m => !m.is_system_model).length,
        barcode: allModels.filter(m => m.model_type === 'barcode').length,
        ocr: allModels.filter(m => m.model_type === 'ocr').length,
        ocr_collections: ocrCollections.length, // OCR集合数量
        ai_restored: allModels.filter(m => m.model_type === 'ai_restored').length,
        feature_matching: allModels.filter(m => m.model_type === 'feature_matching').length
      };
      setStats(newStats);

    } catch (error: any) {
      console.error('获取模型列表失败:', error);
      message.error(error.message || '获取模型列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchModels();
  }, [modelScope, modelTypeFilter]);

  // 处理删除模型
  const handleDeleteModels = async (modelIds: number[]) => {
    try {
      const params: ModelDeleteParams = { model_ids: modelIds };
      const result = await deleteModels(params);

      if (result.success) {
        message.success(result.message);
        setSelectedRowKeys([]);
        fetchModels(); // 刷新列表
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error: any) {
      console.error('删除模型失败:', error);
      message.error(error.message || '删除模型失败');
    }
  };

  // 处理上传模型
  const handleUploadModel = async (values: any) => {
    if (uploadFileList.length === 0) {
      message.error('请选择要上传的模型文件');
      return;
    }

    try {
      setUploading(true);
      const file = uploadFileList[0].originFileObj as RcFile;

      const params: ModelUploadParams = {
        file,
        name: values.name,
        model_type: values.model_type,
        version: values.version, // 现在是必填的
        description: values.description,
        ocr_role: values.ocr_role,
        is_system_model: values.is_system_model || false, // 新增：模型类别
        ocr_collection_name: values.ocr_collection_name // 新增：OCR集合名称
      };

      const result = await uploadModel(params);

      if (result.success) {
        message.success('模型上传成功！请到对应的检测页面查看新模型。');
        setUploadModalVisible(false);
        uploadForm.resetFields();
        setUploadFileList([]);
        fetchModels(); // 刷新列表
      } else {
        message.error(result.message || '上传失败');
      }
    } catch (error: any) {
      console.error('上传模型失败:', error);

      // 处理重复模型的特殊情况
      if (error.message && error.message.includes('已存在')) {
        Modal.confirm({
          title: '模型已存在',
          content: (
            <div>
              <p>{error.message}</p>
              <p><strong>解决方案：</strong></p>
              <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>
                <li>修改模型名称（推荐）</li>
                <li>添加或修改版本号</li>
                <li>先删除现有模型再重新上传</li>
              </ul>
              <p style={{ color: '#666', fontSize: '12px' }}>
                💡 提示：如果您看到模型已存在的错误，说明模型可能已经成功上传到数据库中。
                请到对应的检测页面（如条码检测）查看模型列表，或点击下方"刷新列表"按钮。
              </p>
            </div>
          ),
          okText: '刷新列表',
          cancelText: '我知道了',
          onOk: () => {
            // 刷新模型列表，以防模型实际已经上传成功
            fetchModels();
          }
        });
      } else {
        message.error(error.message || '上传模型失败');
      }
    } finally {
      setUploading(false);
    }
  };

  // 处理编辑模型
  const handleEditModel = (model: VisionModel) => {
    setEditingModel(model);
    editForm.setFieldsValue({
      name: model.name,
      model_type: model.model_type,
      version: model.version,
      description: model.description,
      ocr_role: model.ocr_role,
      is_system_model: model.is_system_model,
      ocr_collection_name: model.ocr_collection_name
    });
    setEditModalVisible(true);
  };

  // 处理编辑OCR集合
  const handleEditOcrCollection = (collection: OcrModelCollection) => {
    setEditingOcrCollection(collection);
    editOcrForm.setFieldsValue({
      collection_name: collection.collection_name,
      description: collection.description
    });
    setEditOcrModalVisible(true);
  };

  // 处理删除OCR集合
  const handleDeleteOcrCollection = async (collection: OcrModelCollection) => {
    try {
      // 收集集合中所有模型的ID
      const modelIds: number[] = [];
      if (collection.detection_model) {
        modelIds.push(collection.detection_model.id);
      }
      if (collection.recognition_model) {
        modelIds.push(collection.recognition_model.id);
      }

      if (modelIds.length > 0) {
        await handleDeleteModels(modelIds);
      }
    } catch (error: any) {
      console.error('删除OCR集合失败:', error);
      message.error(error.message || '删除OCR集合失败');
    }
  };

  // 处理更新OCR集合
  const handleUpdateOcrCollection = async (values: any) => {
    if (!editingOcrCollection) return;

    try {
      setEditing(true);

      // 更新集合中所有模型的collection_name和description
      const updatePromises: Promise<any>[] = [];

      if (editingOcrCollection.detection_model) {
        const params: ModelUpdateParams = {
          name: editingOcrCollection.detection_model.name,
          model_type: editingOcrCollection.detection_model.model_type as 'ocr',
          version: editingOcrCollection.detection_model.version || '',
          description: values.description,
          ocr_role: editingOcrCollection.detection_model.ocr_role as 'detection',
          is_system_model: editingOcrCollection.detection_model.is_system_model,
          ocr_collection_name: values.collection_name
        };
        updatePromises.push(updateModel(editingOcrCollection.detection_model.id, params));
      }

      if (editingOcrCollection.recognition_model) {
        const params: ModelUpdateParams = {
          name: editingOcrCollection.recognition_model.name,
          model_type: editingOcrCollection.recognition_model.model_type as 'ocr',
          version: editingOcrCollection.recognition_model.version || '',
          description: values.description,
          ocr_role: editingOcrCollection.recognition_model.ocr_role as 'recognition',
          is_system_model: editingOcrCollection.recognition_model.is_system_model,
          ocr_collection_name: values.collection_name
        };
        updatePromises.push(updateModel(editingOcrCollection.recognition_model.id, params));
      }

      const results = await Promise.all(updatePromises);

      // 检查所有更新是否成功
      const allSuccess = results.every(result => result.success);

      if (allSuccess) {
        message.success('OCR集合信息更新成功！');
        setEditOcrModalVisible(false);
        editOcrForm.resetFields();
        setEditingOcrCollection(null);
        fetchModels(); // 刷新列表
      } else {
        message.error('部分模型更新失败');
      }
    } catch (error: any) {
      console.error('更新OCR集合失败:', error);
      message.error(error.message || '更新OCR集合失败');
    } finally {
      setEditing(false);
    }
  };

  // 处理更新模型
  const handleUpdateModel = async (values: any) => {
    if (!editingModel) return;

    try {
      setEditing(true);

      const params: ModelUpdateParams = {
        name: values.name,
        model_type: values.model_type,
        version: values.version,
        description: values.description,
        ocr_role: values.ocr_role,
        is_system_model: values.is_system_model || false,
        ocr_collection_name: values.ocr_collection_name
      };

      const result = await updateModel(editingModel.id, params);

      if (result.success) {
        message.success('模型信息更新成功！');
        setEditModalVisible(false);
        editForm.resetFields();
        setEditingModel(null);
        fetchModels(); // 刷新列表
      } else {
        message.error(result.message || '更新失败');
      }
    } catch (error: any) {
      console.error('更新模型失败:', error);
      message.error(error.message || '更新模型失败');
    } finally {
      setEditing(false);
    }
  };

  return (
    <div style={{
      width: '100%',
      maxWidth: '1400px',
      margin: '0 auto'
    }}>
      {/* 添加表格行样式 */}
      <style>
        {`
          .table-row-even:hover {
            background-color: #f5f5f5 !important;
          }
          .table-row-odd:hover {
            background-color: #f5f5f5 !important;
          }
          .table-row-even {
            background-color: #fafafa;
          }
          .table-row-odd {
            background-color: #ffffff;
          }
          .ant-table-tbody > tr > td {
            transition: background-color 0.2s ease;
          }
          .ant-table-thead > tr > th {
            background-color: #fafafa !important;
            font-weight: 600 !important;
          }
          .ant-table-container {
            border-radius: 8px;
            overflow: hidden;
          }
          .ant-table {
            border-radius: 8px;
          }
        `}
      </style>

      {/* 页面标题 */}
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div>
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            <DatabaseOutlined style={{ marginRight: '12px' }} />
            模型管理
          </Title>
          <Paragraph style={{
            marginTop: '6px',
            fontSize: '14px',
            color: '#666',
            marginBottom: 0
          }}>
            管理AI模型文件，包括系统模型和用户上传的自定义模型
          </Paragraph>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchModels}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setUploadModalVisible(true)}
          >
            上传模型
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <div style={{ marginBottom: '20px' }}>
        {/* 第一行：总模型数、系统模型、用户模型 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col xs={24} sm={8} lg={8}>
            <Card size="small">
              <Statistic
                title="总模型数"
                value={stats.total}
                prefix={<DatabaseOutlined style={{ color: '#1890ff' }} />}
                loading={loading}
                valueStyle={{ fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={8}>
            <Card size="small">
              <Statistic
                title="系统模型"
                value={stats.system}
                prefix={<AppstoreOutlined style={{ color: '#52c41a' }} />}
                loading={loading}
                valueStyle={{ fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={8}>
            <Card size="small">
              <Statistic
                title="用户模型"
                value={stats.custom}
                prefix={<CloudUploadOutlined style={{ color: '#faad14' }} />}
                loading={loading}
                valueStyle={{ fontSize: '20px' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 第二行：AI复原模型、条码检测模型、OCR检测模型 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} lg={8}>
            <Card size="small">
              <Statistic
                title="AI复原模型"
                value={stats.ai_restored}
                prefix={<EyeOutlined style={{ color: '#722ed1' }} />}
                loading={loading}
                valueStyle={{ fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={8}>
            <Card size="small">
              <Statistic
                title="条码检测模型"
                value={stats.barcode}
                prefix={<DatabaseOutlined style={{ color: '#13c2c2' }} />}
                loading={loading}
                valueStyle={{ fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={8}>
            <Card size="small">
              <Statistic
                title="OCR检测模型"
                value={`${stats.ocr_collections}个集合`}
                prefix={<DatabaseOutlined style={{ color: '#fa8c16' }} />}
                loading={loading}
                valueStyle={{ fontSize: '20px' }}
                suffix={
                  <div style={{
                    fontSize: '12px',
                    color: '#8c8c8c',
                    marginTop: '4px',
                    lineHeight: '1'
                  }}>
                    共{stats.ocr}个模型
                  </div>
                }
              />
            </Card>
          </Col>
        </Row>
        {/* 第三行：特征匹配模型 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} lg={8}>
            <Card size="small">
              <Statistic
                title="特征匹配模型"
                value={stats.feature_matching}
                prefix={<EyeOutlined style={{ color: '#1d39c4' }} />}
                loading={loading}
                valueStyle={{ fontSize: '20px' }}
              />
            </Card>
          </Col>
        </Row>
      </div>

      {/* 筛选和操作栏 */}
      <Card style={{ marginBottom: '16px' }} size="small">
        <Row gutter={[12, 12]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Space size="small">
              <Text strong style={{ fontSize: '13px' }}>模型范围:</Text>
              <Select
                value={modelScope}
                onChange={setModelScope}
                style={{ width: 110 }}
                size="small"
              >
                <Option value="all">全部</Option>
                <Option value="system">系统模型</Option>
                <Option value="custom">用户模型</Option>
              </Select>
            </Space>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Space size="small">
              <Text strong style={{ fontSize: '13px' }}>模型类型:</Text>
              <Select
                value={modelTypeFilter}
                onChange={setModelTypeFilter}
                style={{ width: 110 }}
                size="small"
              >
                <Option value="all">全部</Option>
                <Option value="barcode">条码检测</Option>
                <Option value="ocr">OCR识别</Option>
                <Option value="ai_restored">AI复原</Option>
                <Option value="feature_matching">特征匹配</Option>
              </Select>
            </Space>
          </Col>
          <Col xs={24} sm={8} md={12}>
            <div style={{ textAlign: 'right' }}>
              {selectedRowKeys.length > 0 && (
                <Space size="small">
                  <Text style={{ fontSize: '13px' }}>已选择 {selectedRowKeys.length} 个模型</Text>
                  <Popconfirm
                    title="确定要删除选中的模型吗？"
                    description="删除后无法恢复，请谨慎操作。"
                    onConfirm={() => {
                      // 处理选中的项目，包括OCR集合和普通模型
                      const modelIds: number[] = [];
                      const ocrCollectionIds: string[] = [];

                      selectedRowKeys.forEach(key => {
                        if (typeof key === 'string') {
                          if (key.startsWith('model_')) {
                            // 普通模型
                            modelIds.push(parseInt(key.replace('model_', '')));
                          } else if (key.startsWith('ocr_collection_')) {
                            // OCR集合
                            ocrCollectionIds.push(key);
                          }
                        }
                      });

                      // 处理OCR集合删除
                      ocrCollectionIds.forEach(collectionKey => {
                        const collection = models.find(m =>
                          'collection_name' in m && `ocr_collection_${m.id}` === collectionKey
                        ) as OcrModelCollection;
                        if (collection) {
                          handleDeleteOcrCollection(collection);
                        }
                      });

                      // 处理普通模型删除
                      if (modelIds.length > 0) {
                        handleDeleteModels(modelIds);
                      }
                    }}
                    okText="确定"
                    cancelText="取消"
                    icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                  >
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      size="small"
                    >
                      批量删除
                    </Button>
                  </Popconfirm>
                </Space>
              )}
            </div>
          </Col>
        </Row>
      </Card>

      {/* 模型列表表格 */}
      <Card
        styles={{
          body: {
            padding: '16px',
            overflow: 'hidden'
          }
        }}
        style={{
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
        }}
      >
        <Table<ExtendedModel>
          expandable={{
            expandedRowRender: (record) => {
              const isOcrCollection = 'collection_name' in record;
              if (!isOcrCollection) return null;

              const collection = record as OcrModelCollection;
              const subModels = [collection.detection_model, collection.recognition_model].filter(Boolean) as VisionModel[];

              return (
                <Table<VisionModel>
                  columns={[
                    {
                      title: '角色',
                      dataIndex: 'ocr_role',
                      key: 'ocr_role',
                      width: 100,
                      render: (role: string) => (
                        <Tag color={role === 'detection' ? 'blue' : 'green'}>
                          {role === 'detection' ? '检测模型' : '识别模型'}
                        </Tag>
                      ),
                    },
                    {
                      title: '模型名称',
                      dataIndex: 'name',
                      key: 'name',
                      width: 200,
                    },
                    {
                      title: '版本',
                      dataIndex: 'version',
                      key: 'version',
                      width: 100,
                    },
                    {
                      title: '描述',
                      dataIndex: 'description',
                      key: 'description',
                      ellipsis: true,
                      render: (description: string) => (
                        <Text style={{ fontSize: '12px', color: '#595959' }}>
                          {description || '暂无描述'}
                        </Text>
                      ),
                    },
                    {
                      title: '操作',
                      key: 'action',
                      width: 100,
                      render: (_, subRecord: VisionModel) => (
                        <Button
                          type="link"
                          size="small"
                          icon={<EditOutlined />}
                          onClick={() => handleEditModel(subRecord)}
                        >
                          编辑
                        </Button>
                      ),
                    },
                  ]}
                  dataSource={subModels}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  showHeader={true}
                />
              );
            },
            expandRowByClick: false,
            rowExpandable: (record) => 'collection_name' in record,
          }}
          columns={[
            {
              title: <Text strong style={{ fontSize: '14px' }}>模型名称</Text>,
              dataIndex: 'name',
              key: 'name',
              width: 200,
              fixed: 'left',
              ellipsis: {
                showTitle: false,
              },
              render: (_: string, record: ExtendedModel) => {
                // 判断是否为OCR集合
                const isOcrCollection = 'collection_name' in record;
                const displayName = isOcrCollection ? record.collection_name : (record as VisionModel).name;

                return (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    width: '100%'
                  }}>
                    <Tooltip
                      placement="topLeft"
                      title={displayName}
                    >
                      <Text
                        strong
                        style={{
                          fontSize: '13px',
                          color: '#262626',
                          lineHeight: '20px',
                          flex: '1',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '120px'
                        }}
                      >
                        {displayName}
                      </Text>
                    </Tooltip>
                    <div style={{
                      display: 'flex',
                      gap: '4px',
                      flexShrink: 0
                    }}>
                      {isOcrCollection && (
                        <Tag
                          color="orange"
                          style={{
                            fontSize: '9px',
                            padding: '1px 4px',
                            lineHeight: '14px',
                            borderRadius: '8px',
                            border: 'none',
                            margin: 0
                          }}
                        >
                          OCR集合
                        </Tag>
                      )}
                      {record.is_system_model && (
                        <Tag
                          color="blue"
                          style={{
                            fontSize: '9px',
                            padding: '1px 4px',
                            lineHeight: '14px',
                            borderRadius: '8px',
                            border: 'none',
                            margin: 0
                          }}
                        >
                          系统
                        </Tag>
                      )}
                    </div>
                  </div>
                );
              },
            },
            {
              title: <Text strong style={{ fontSize: '14px' }}>类型</Text>,
              dataIndex: 'model_type',
              key: 'model_type',
              width: 100,
              render: (type: string) => {
                const typeMap = {
                  barcode: { text: '条码检测', color: 'green' },
                  ocr: { text: 'OCR识别', color: 'orange' },
                  ai_restored: { text: 'AI复原', color: 'purple' },
                  feature_matching: { text: '特征匹配', color: 'blue' }
                };
                const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
                return (
                  <Tag
                    color={config.color}
                    style={{
                      fontSize: '11px',
                      padding: '2px 8px',
                      lineHeight: '18px',
                      borderRadius: '12px',
                      border: 'none',
                      fontWeight: '500'
                    }}
                  >
                    {config.text}
                  </Tag>
                );
              },
            },
            {
              title: <Text strong style={{ fontSize: '14px' }}>版本</Text>,
              dataIndex: 'version',
              key: 'version',
              width: 80,
              render: (version: string) => (
                <Text
                  style={{
                    fontSize: '12px',
                    color: '#595959',
                    fontFamily: 'Monaco, Consolas, monospace',
                    backgroundColor: '#f5f5f5',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    border: '1px solid #e8e8e8'
                  }}
                >
                  {version || '-'}
                </Text>
              ),
            },
            {
              title: <Text strong style={{ fontSize: '14px' }}>描述</Text>,
              dataIndex: 'description',
              key: 'description',
              width: 250,
              ellipsis: {
                showTitle: false,
              },
              render: (description: string) => (
                <Tooltip
                  placement="topLeft"
                  title={description || '暂无描述'}
                >
                  <Text
                    style={{
                      fontSize: '12px',
                      color: '#595959',
                      lineHeight: '18px',
                      display: 'block',
                      maxWidth: '200px'
                    }}
                  >
                    {description || (
                      <span style={{ color: '#bfbfbf', fontStyle: 'italic' }}>暂无描述</span>
                    )}
                  </Text>
                </Tooltip>
              ),
            },
            {
              title: <Text strong style={{ fontSize: '14px' }}>上传时间</Text>,
              dataIndex: 'uploaded_at',
              key: 'uploaded_at',
              width: 140,
              render: (date: string) => (
                <div style={{ lineHeight: '18px' }}>
                  <Text
                    style={{
                      fontSize: '12px',
                      color: '#595959',
                      display: 'block'
                    }}
                  >
                    {new Date(date).toLocaleDateString('zh-CN')}
                  </Text>
                  <Text
                    style={{
                      fontSize: '11px',
                      color: '#8c8c8c'
                    }}
                  >
                    {new Date(date).toLocaleTimeString('zh-CN', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </Text>
                </div>
              ),
            },
            {
              title: <Text strong style={{ fontSize: '14px' }}>操作</Text>,
              key: 'action',
              width: 140,
              fixed: 'right',
              render: (_, record: ExtendedModel) => {
                const isOcrCollection = 'collection_name' in record;

                if (isOcrCollection) {
                  // OCR集合的操作：编辑集合名称、删除整个集合
                  return (
                    <Space
                      size={8}
                      style={{
                        display: 'flex',
                        flexWrap: 'nowrap',
                        justifyContent: 'flex-start'
                      }}
                    >
                      <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEditOcrCollection(record)}
                        style={{
                          padding: '4px 8px',
                          fontSize: '12px',
                          height: '28px',
                          color: '#1890ff',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          backgroundColor: '#fff',
                          transition: 'all 0.2s'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#f0f8ff';
                          e.currentTarget.style.borderColor = '#1890ff';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = '#fff';
                          e.currentTarget.style.borderColor = '#d9d9d9';
                        }}
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确定要删除这个OCR模型集合吗？"
                        description="将删除包含的detection和recognition模型，删除后无法恢复。"
                        onConfirm={() => handleDeleteOcrCollection(record)}
                        okText="确定"
                        cancelText="取消"
                        icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                      >
                        <Button
                          type="link"
                          danger
                          size="small"
                          icon={<DeleteOutlined />}
                          style={{
                            padding: '4px 8px',
                            fontSize: '12px',
                            height: '28px',
                            color: '#ff4d4f',
                            border: '1px solid #d9d9d9',
                            borderRadius: '4px',
                            backgroundColor: '#fff',
                            transition: 'all 0.2s'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#fff2f0';
                            e.currentTarget.style.borderColor = '#ff4d4f';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '#fff';
                            e.currentTarget.style.borderColor = '#d9d9d9';
                          }}
                        >
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  );
                }

                // 普通模型的操作
                return (
                  <Space
                    size={8}
                    style={{
                      display: 'flex',
                      flexWrap: 'nowrap',
                      justifyContent: 'flex-start'
                    }}
                  >
                    <Button
                      type="link"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEditModel(record as VisionModel)}
                      style={{
                        padding: '4px 8px',
                        fontSize: '12px',
                        height: '28px',
                        color: '#1890ff',
                        border: '1px solid #d9d9d9',
                        borderRadius: '4px',
                        backgroundColor: '#fff',
                        transition: 'all 0.2s'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f0f8ff';
                        e.currentTarget.style.borderColor = '#1890ff';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#fff';
                        e.currentTarget.style.borderColor = '#d9d9d9';
                      }}
                    >
                      编辑
                    </Button>
                    <Popconfirm
                      title="确定要删除这个模型吗？"
                      description="删除后无法恢复，请谨慎操作。"
                      onConfirm={() => handleDeleteModels([(record as VisionModel).id])}
                      okText="确定"
                      cancelText="取消"
                      icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                    >
                      <Button
                        type="link"
                        danger
                        size="small"
                        icon={<DeleteOutlined />}
                        style={{
                          padding: '4px 8px',
                          fontSize: '12px',
                          height: '28px',
                          color: '#ff4d4f',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          backgroundColor: '#fff',
                          transition: 'all 0.2s'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#fff2f0';
                          e.currentTarget.style.borderColor = '#ff4d4f';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = '#fff';
                          e.currentTarget.style.borderColor = '#d9d9d9';
                        }}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
          dataSource={models}
          rowKey={(record) => {
            const isOcrCollection = 'collection_name' in record;
            return isOcrCollection ? `ocr_collection_${record.id}` : `model_${(record as VisionModel).id}`;
          }}
          loading={loading}
          pagination={{
            current: currentPage,
            total: models.length,
            pageSize: pageSize,
            showSizeChanger: true,
            pageSizeOptions: ['6', '10', '20', '50'],
            showQuickJumper: true,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
            },
            onShowSizeChange: (_, size) => {
              setCurrentPage(1); // 重置到第一页
              setPageSize(size);
            },
            showTotal: (total, range) => (
              <Text style={{ fontSize: '12px', color: '#595959' }}>
                第 {range[0]}-{range[1]} 条，共 {total} 条
              </Text>
            ),
            size: 'small',
            style: {
              marginTop: '8px',
              textAlign: 'right'
            }
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedKeys) => {
              // 支持选择OCR集合和普通模型
              setSelectedRowKeys(selectedKeys);
            },
            getCheckboxProps: () => {
              // 所有行都可以被选择
              return {
                disabled: false,
              };
            },
            columnWidth: 50
          }}
          scroll={{
            x: 900,
            y: 360
          }}
          size="middle"
          style={{
            width: '100%'
          }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-even' : 'table-row-odd'
          }
          components={{
            header: {
              cell: (props: any) => (
                <th
                  {...props}
                  style={{
                    ...props.style,
                    backgroundColor: '#fafafa',
                    borderBottom: '2px solid #e8e8e8',
                    padding: '12px 16px',
                    fontWeight: '600'
                  }}
                />
              )
            },
            body: {
              cell: (props: any) => (
                <td
                  {...props}
                  style={{
                    ...props.style,
                    padding: '12px 16px',
                    borderBottom: '1px solid #f0f0f0',
                    verticalAlign: 'middle'
                  }}
                />
              )
            }
          }}
        />
      </Card>

      {/* 上传模型模态框 */}
      <Modal
        title="上传模型"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          uploadForm.resetFields();
          setUploadFileList([]);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={handleUploadModel}
          initialValues={{
            is_system_model: true, // 默认选择系统模型
            version: '1.0.0' // 默认版本号
          }}
        >
          <Form.Item
            name="name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="请输入模型名称" />
          </Form.Item>

          <Form.Item
            name="model_type"
            label="模型类型"
            rules={[{ required: true, message: '请选择模型类型' }]}
          >
            <Select placeholder="请选择模型类型" onChange={() => setUploadFileList([])}>
              <Option value="barcode">条码检测模型 (.pt格式)</Option>
              <Option value="ocr">OCR识别模型 (.tar/.zip格式)</Option>
              <Option value="ai_restored">AI复原模型 (.onnx格式)</Option>
              <Option value="feature_matching">特征匹配模型 (.onnx格式)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="is_system_model"
            label="模型类别"
            rules={[{ required: true, message: '请选择模型类别' }]}
            tooltip="系统模型用户在使用页面无法上传，用户模型可以由用户上传"
          >
            <Select placeholder="请选择模型类别">
              <Option value={false}>用户模型</Option>
              <Option value={true}>系统模型</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.model_type !== currentValues.model_type
            }
          >
            {({ getFieldValue }) => {
              const modelType = getFieldValue('model_type');
              return modelType === 'ocr' ? (
                <>
                  <Form.Item
                    name="ocr_role"
                    label="OCR角色"
                    rules={[{ required: true, message: '请选择OCR角色' }]}
                  >
                    <Select placeholder="请选择OCR角色">
                      <Option value="detection">检测模型</Option>
                      <Option value="recognition">识别模型</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="ocr_collection_name"
                    label="OCR集合名称"
                    tooltip="用于将detection和recognition模型配对管理，相同集合名称的模型会被归为一组"
                  >
                    <Input placeholder="请输入OCR集合名称（可选）" />
                  </Form.Item>
                </>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            name="version"
            label="版本号"
            rules={[{ required: true, message: '请输入版本号' }]}
            tooltip="建议使用语义化版本号，如：1.0.0、2.1.3等"
          >
            <Input placeholder="请输入版本号，如：1.0.0" />
          </Form.Item>

          <Form.Item
            name="description"
            label="模型描述"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入模型描述（可选）"
            />
          </Form.Item>

          <Form.Item
            label="模型文件"
            required
          >
            <Upload
              {...{
                beforeUpload: (file: RcFile) => {
                  // 文件类型验证
                  const allowedTypes = {
                    barcode: ['.pt'],
                    ocr: ['.tar', '.tar.gz', '.zip'],
                    ai_restored: ['.onnx'],
                    feature_matching: ['.onnx']
                  };

                  const modelType = uploadForm.getFieldValue('model_type');
                  if (modelType && allowedTypes[modelType as keyof typeof allowedTypes]) {
                    const allowed = allowedTypes[modelType as keyof typeof allowedTypes];
                    const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();

                    if (!allowed.some(ext => fileExt.endsWith(ext))) {
                      message.error(`${modelType}模型只支持${allowed.join(', ')}格式的文件`);
                      return false;
                    }
                  }

                  // 文件大小限制 (500MB)
                  const isLt500M = file.size / 1024 / 1024 < 500;
                  if (!isLt500M) {
                    message.error('文件大小不能超过500MB');
                    return false;
                  }

                  setUploadFileList([{
                    uid: file.uid || file.name,
                    name: file.name,
                    status: 'done',
                    originFileObj: file
                  }]);

                  return false; // 阻止自动上传
                },
                fileList: uploadFileList,
                onRemove: () => {
                  setUploadFileList([]);
                },
              }}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
              支持的文件格式：条码检测(.pt)、OCR识别(.tar/.zip)、AI复原(.onnx)、特征匹配(.onnx)，最大500MB
            </div>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setUploadModalVisible(false);
                  uploadForm.resetFields();
                  setUploadFileList([]);
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={uploading}
                disabled={uploadFileList.length === 0}
              >
                上传模型
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑模型模态框 */}
      <Modal
        title="编辑模型信息"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setEditingModel(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateModel}
          initialValues={{
            is_system_model: false
          }}
        >
          <Form.Item
            name="name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="请输入模型名称" />
          </Form.Item>

          <Form.Item
            name="model_type"
            label="模型类型"
            rules={[{ required: true, message: '请选择模型类型' }]}
          >
            <Select placeholder="请选择模型类型">
              <Option value="barcode">条码检测模型</Option>
              <Option value="ocr">OCR识别模型</Option>
              <Option value="ai_restored">AI复原模型</Option>
              <Option value="feature_matching">特征匹配模型</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="is_system_model"
            label="模型类别"
            rules={[{ required: true, message: '请选择模型类别' }]}
            tooltip="系统模型将对所有用户可见，用户模型仅对当前用户可见"
          >
            <Select placeholder="请选择模型类别">
              <Option value={false}>用户模型</Option>
              <Option value={true}>系统模型</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.model_type !== currentValues.model_type
            }
          >
            {({ getFieldValue }) => {
              const modelType = getFieldValue('model_type');
              return modelType === 'ocr' ? (
                <>
                  <Form.Item
                    name="ocr_role"
                    label="OCR角色"
                    rules={[{ required: true, message: '请选择OCR角色' }]}
                  >
                    <Select placeholder="请选择OCR角色">
                      <Option value="detection">检测模型</Option>
                      <Option value="recognition">识别模型</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="ocr_collection_name"
                    label="OCR集合名称"
                    tooltip="用于将detection和recognition模型配对管理，相同集合名称的模型会被归为一组"
                  >
                    <Input placeholder="请输入OCR集合名称（可选）" />
                  </Form.Item>
                </>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            name="version"
            label="版本号"
            rules={[{ required: true, message: '请输入版本号' }]}
            tooltip="建议使用语义化版本号，如：1.0.0、2.1.3等"
          >
            <Input placeholder="请输入版本号，如：1.0.0" />
          </Form.Item>

          <Form.Item
            name="description"
            label="模型描述"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入模型描述（可选）"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setEditModalVisible(false);
                  editForm.resetFields();
                  setEditingModel(null);
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={editing}
              >
                更新模型
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* OCR集合编辑模态框 */}
      <Modal
        title="编辑OCR集合"
        open={editOcrModalVisible}
        onCancel={() => {
          setEditOcrModalVisible(false);
          editOcrForm.resetFields();
          setEditingOcrCollection(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editOcrForm}
          layout="vertical"
          onFinish={handleUpdateOcrCollection}
        >
          <Form.Item
            name="collection_name"
            label="集合名称"
            rules={[{ required: true, message: '请输入集合名称' }]}
          >
            <Input placeholder="请输入OCR集合名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="集合描述"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入集合描述（可选）"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setEditOcrModalVisible(false);
                  editOcrForm.resetFields();
                  setEditingOcrCollection(null);
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={editing}
              >
                更新集合
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminModelsPage;
