---
description: Explore the utility functions in FastSAM for adjusting bounding boxes and calculating IoU, benefiting computer vision projects.
keywords: FastSAM, bounding boxes, IoU, Ultralytics, image processing, computer vision
---

# Reference for `ultralytics/models/fastsam/utils.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/fastsam/utils.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.fastsam.utils.adjust_bboxes_to_image_border

<br><br>
