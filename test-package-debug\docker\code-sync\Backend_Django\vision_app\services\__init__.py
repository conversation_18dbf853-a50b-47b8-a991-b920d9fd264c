"""
服务层模块

此模块包含了从视图层抽取出来的业务逻辑，提高代码的可维护性和可测试性。

模块结构：
- base.py: 基础服务类
- file_service.py: 文件处理服务
- admin_service.py: 管理员功能服务
- prediction_service.py: AI推理服务
- image_service.py: 图像处理服务
- model_service.py: AI模型管理服务
"""

from .file_service import FileService
from .admin_service import AdminService
from .prediction_service import PredictionService
from .image_service import ImageService
from .model_service import ModelService
from .scanner_service import ScannerService

# Create a single, shared instance of the ScannerService
scanner_service_instance = ScannerService()

__all__ = [
    'FileService',
    'AdminService',
    'PredictionService',
    'ImageService',
    'ModelService',
    'ScannerService', # Keep for type hinting if needed
    'scanner_service_instance',
]
