import React, { useEffect, useRef } from 'react';
import { useFunctionPanel } from '../contexts/FunctionPanelContext';
import BarcodeDetectionPanel from './parameterPanels/BarcodeDetectionPanel';
import OcrDetectionPanel from './parameterPanels/OcrDetectionPanel';
import AiRestorePanel from './parameterPanels/AiRestorePanel';
import FeatureMatchingTraditionalPanel from './parameterPanels/FeatureMatchingTraditionalPanel';
import FeatureMatchingModelPanel from './parameterPanels/FeatureMatchingModelPanel'; // 导入模型匹配面板
import { useBarcodeDetectionParams } from '../contexts/BarcodeDetectionContext';
import { useOcrDetectionParams } from '../contexts/OcrDetectionContext';
import { useAiRestoreDetectionParams } from '../contexts/AiRestoreDetectionContext';
// import OcrDetectionPanel from './parameterPanels/OcrDetectionPanel';
// import AiRestorePanel from './parameterPanels/AiRestorePanel';

const ParameterPanel: React.FC = () => {
  // Get the selected function object from context
  const { selectedFunction } = useFunctionPanel();
  // 获取条码检测和OCR检测的设置函数
  const { setAutoInferenceEnabled: setBarcodeAutoInference } = useBarcodeDetectionParams();
  const { setAutoInferenceEnabled: setOcrAutoInference } = useOcrDetectionParams();
  const { setAutoInferenceEnabled: setAiRestoreAutoInference } = useAiRestoreDetectionParams();

  // Ref to store the previous function key
  const prevSelectedFunctionKeyRef = useRef<string | undefined | null>(null);

  // Log the value received from context on every render
  console.log('[ParameterPanel] Rendering with selectedFunction:', selectedFunction);

  useEffect(() => {
    const currentKey = selectedFunction?.key !== undefined ? String(selectedFunction.key) : undefined; // Convert to string or undefined
    
    if (prevSelectedFunctionKeyRef.current !== currentKey) {
      setBarcodeAutoInference(false);
      setOcrAutoInference(false);
      setAiRestoreAutoInference(false);
      console.log('[ParameterPanel] 功能面板已切换，已重置所有自动推理状态');
    }
    prevSelectedFunctionKeyRef.current = currentKey;
  }, [selectedFunction?.key, setBarcodeAutoInference, setOcrAutoInference, setAiRestoreAutoInference]);

  // TODO: This state needs to be updated by the FunctionTree component.
  // We will likely need to lift the state up or use another context.
  // console.log('Current selectedFunction from context:', selectedFunction);

  const renderPanel = () => {
    // Use selectedFunction.key for the switch
    switch (selectedFunction?.key) { 
      case 'barcode-detection':
        return <BarcodeDetectionPanel />;
      case 'ocr-detection':
        return <OcrDetectionPanel />;
      case 'ai-restore':
        return <AiRestorePanel />;
      case 'feature-matching-traditional':
        return <FeatureMatchingTraditionalPanel />;
      case 'feature-matching-model':
        return <FeatureMatchingModelPanel />;
      // case 'binarization':
          // return <BinarizationPanel />; // Need to create this if needed
      default:
        // Show default message if nothing is selected or key doesn't match
        return <div style={{ padding: '16px', textAlign: 'center', color: '#999' }}>请从左侧选择一个功能</div>;
    }
  };

  return (
    <div style={{
      width: '100%', 
      height: '100%',
      backgroundColor: '#ffffff',
      borderLeft: '1px solid #d9d9d9',
      overflowY: 'auto' // Add scroll if content overflows
    }}>
     {renderPanel()}
    </div>
  );
};

export default ParameterPanel;