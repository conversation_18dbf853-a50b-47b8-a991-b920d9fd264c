#ifndef _INPUT_IMAGE_H
#define _INPUT_IMAGE_H

//-----------------------------------------------------------------------------
//  Includes

#include <cmath>
#include <cstdint>

#include "Log.hpp"
#include "ModelLoader.hpp"
#include "AIEngineCommon.h"
#include "utils/Geometry.hpp"
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

// 输入图像类
class InputImage {
public:
    /**
     * @brief    
     *           清空输入张量
     *           
     * @param    prep:      前处理数据
     *           
     * @date     2024-10-31 Created by HuangJP
     */
    virtual void clear_input_tensor(PreprocessedData *prep) = 0;

    /**
     * @brief    
     *           缩放图片以填充至输入张量
     *           
     * @param    img_ptr:           指向原图存放数组的指针
     * @param    img_h,img_w:       原图高度和原图宽度
     * @param    start_y,start_x:   检测区域起始坐标
     * @param    det_h,det_w:       检测区域高度和宽度
     * @param    integer_factor:    是否使用整数缩放系数
     * @param    keep_ratio:        是否确保宽度和高度方向上缩放系数保持一致，避免图像变形
     * @param    prep:              前处理数据
     * @param    rsn:               右移位数
     *           
     * @retval   错误码
     *           
     * @date     2024-09-03 Created by HuangJP
     */
    virtual int resize_to_fill_input_tensor(void *img_ptr, int img_h, int img_w, int start_y, int start_x, int det_h, int det_w, bool integer_factor, bool keep_ratio, PreprocessedData &data, int8_t rsn) = 0;
    /**
     * @brief    
     *           填充输入张量，输入四边形区域坐标，将四边形区域旋转摆正
     *           
     * @param    img_ptr:           指向原图存储数组的指针
     * @param    quad_points：      四边形区域坐标
     * @param    upside_down:       是否上下颠倒图像
     * @param    rows:              原图高度
     * @param    cols:              原图宽度
     * @param    prep:              前处理数据
     * @param    rsn:               右移位数
     *           
     * @retval   错误码
     *           
     * @date     2024-08-06 Created by HuangJP
     */
    virtual int rotate_to_fill_input_tensor(void *img_ptr, QuadPoints quad_points, bool upside_down, int rows, int cols, PreprocessedData data, int8_t rsn) = 0;

    /**
     * @brief    
     *           自适应获取子位深度提取范围
     *           
     * @param    ref_rows,ref_cols:         指定参考像素的行数、列数（行数、列数不足原图高度、宽度时，间隔数行或数列取像素点；超过原图高度、宽度时，取与原图高度、宽度数量相同的像素点）
     * @param    img_h,img_w:               参考图像高度、宽度
     * @param    img_ptr:                   指向图片存储数组的指针
     * @param    start_y:                   检测区域起始点y轴坐标
     * @param    row_stride:                原图数组每行像素宽度
     * @param    start_x:                   检测区域起始点x轴坐标
     *           
     * @retval   子位深度提取范围，同时也是逻辑右移位数。
     *           
     * @date     2024-10-31 Created by HuangJP
     */
    virtual int8_t get_sub_bit_range_adaptive(int ref_rows, int ref_cols, int img_h, int img_w, void *img_ptr, int start_y, int row_stride, int start_x) = 0;

    /**
     * @brief    
     *           设置内存存储格式
     *           
     * @param    memory_format:             输入张量内存存储格式
     *           
     * @date     2024-09-04 Created by HuangJP
     */
    void set_memory_format(BaseModelLoader::memory_format_t memory_format)
    {
        this->_memory_format = memory_format;
    }

protected:
    BaseModelLoader::memory_format_t _memory_format; // 张量内存存储格式

    virtual ~InputImage() {}
    InputImage() {}
};

/**
 * @brief    
 *           输入灰度图片类
 *           
 * @param    enable_normalization:      是否对输入图片做归一化操作
 *           
 * @date     2024-08-02 Created by HuangJP
 */
template <typename pixel_t, typename tensor_t, bool enable_normalization>
class InputGrayImage : public InputImage {
public:
    /**
     * @brief    
     *           输入灰度图片类构造函数
     *           
     *           
     * @date     2024-08-02 Created by HuangJP
     */
    InputGrayImage() : InputImage() {}

    /**
     * @brief    
     *           输入灰度图片类析构函数
     *           
     * @date     2024-08-02 Created by HuangJP
     */
    ~InputGrayImage() {}

    /**
     * @brief    
     *           清空输入张量
     *           
     * @param    prep:      前处理数据
     *           
     * @date     2024-10-31 Created by HuangJP
     */
    void clear_input_tensor(PreprocessedData *prep) override
    {
        memset(prep->input_tensor_ptr, 0x00, sizeof(tensor_t) * prep->resize_n * prep->resize_h * prep->resize_w * prep->resize_c); // 清空输入张量
    }

    /**
     * @brief    
     *           缩放图片以填充至输入张量
     *           
     * @param    img_ptr:           指向原图存放数组的指针
     * @param    img_h,img_w:       原图高度和原图宽度
     * @param    start_y,start_x:   检测区域起始坐标
     * @param    det_h,det_w:       检测区域高度和宽度
     * @param    memory_format:     输入张量内存存储格式
     * @param    integer_factor:    是否使用整数缩放系数
     * @param    keep_ratio:        是否确保宽度和高度方向上缩放系数保持一致，避免图像变形
     * @param    prep:              前处理数据
     *           
     * @retval   错误码
     *           
     * @date     2024-09-03 Created by HuangJP
     */
    int resize_to_fill_input_tensor(void *img_ptr, int img_h, int img_w, int start_y, int start_x, int det_h, int det_w, bool integer_factor, bool keep_ratio, PreprocessedData &prep, int8_t rsn) override
    {
        switch (rsn)
        {
            case MACR_SUB_BIT_EXTRACTED_RANGE_00_07: return op0._resize_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, img_h, img_w, start_y, start_x, det_h, det_w, integer_factor, keep_ratio, prep);
            case MACR_SUB_BIT_EXTRACTED_RANGE_01_08: return op1._resize_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, img_h, img_w, start_y, start_x, det_h, det_w, integer_factor, keep_ratio, prep);;
            case MACR_SUB_BIT_EXTRACTED_RANGE_02_09: return op2._resize_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, img_h, img_w, start_y, start_x, det_h, det_w, integer_factor, keep_ratio, prep);;
            case MACR_SUB_BIT_EXTRACTED_RANGE_03_10: return op3._resize_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, img_h, img_w, start_y, start_x, det_h, det_w, integer_factor, keep_ratio, prep);;
            case MACR_SUB_BIT_EXTRACTED_RANGE_04_11: return op4._resize_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, img_h, img_w, start_y, start_x, det_h, det_w, integer_factor, keep_ratio, prep);;
            default: 
                LOGE("Unsupported sub bit depth extracted range.");
                return AIENGINE_INVALID_PARAM;
        }
    }

    /**
     * @brief    
     *           填充输入张量，输入四边形区域坐标，将四边形区域旋转摆正
     *           
     * @param    img_ptr:           指向原图存储数组的指针
     * @param    quad_points：      四边形区域坐标
     * @param    upside_down:       是否上下颠倒图像
     * @param    rows:              原图高度
     * @param    cols:              原图宽度
     * @param    data:              前处理数据
     * @param    prep:              前处理数据
     *           
     * @retval   错误码
     *           
     * @date     2024-08-06 Created by HuangJP
     */
    int rotate_to_fill_input_tensor(void *img_ptr, QuadPoints quad_points, bool upside_down, int rows, int cols, PreprocessedData data, int8_t rsn) override
    {
        switch (rsn)
        {
            case MACR_SUB_BIT_EXTRACTED_RANGE_00_07: return op0._rotate_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, quad_points, upside_down, rows, cols, data);
            case MACR_SUB_BIT_EXTRACTED_RANGE_01_08: return op1._rotate_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, quad_points, upside_down, rows, cols, data);
            case MACR_SUB_BIT_EXTRACTED_RANGE_02_09: return op2._rotate_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, quad_points, upside_down, rows, cols, data);
            case MACR_SUB_BIT_EXTRACTED_RANGE_03_10: return op3._rotate_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, quad_points, upside_down, rows, cols, data);
            case MACR_SUB_BIT_EXTRACTED_RANGE_04_11: return op4._rotate_to_fill_input_tensor(this->_memory_format, (pixel_t *)img_ptr, quad_points, upside_down, rows, cols, data);
            default: 
                LOGE("Unsupported sub bit depth extracted range.");
                return AIENGINE_INVALID_PARAM;
        }
    }

    /**
     * @brief    
     *           自适应获取子位深度提取范围
     *           
     * @param    ref_rows,ref_cols:         指定参考像素的行数、列数（行数、列数不足原图高度、宽度时，间隔数行或数列取像素点；超过原图高度、宽度时，取与原图高度、宽度数量相同的像素点）
     * @param    img_h,img_w:               参考图像高度、宽度
     * @param    pix_w:                     原图数组每行像素宽度
     * @param    img_ptr:                   指向图片存储数组的指针
     *           
     * @retval   子位深度提取范围，同时也是逻辑右移位数。
     *           
     * @date     2024-10-31 Created by HuangJP
     */
    int8_t get_sub_bit_range_adaptive(int ref_rows, int ref_cols, int img_h, int img_w, void *img_ptr, int start_y, int row_stride, int start_x) override
    {
        // 判断传入参数是否合法
        if (img_ptr == nullptr)
        {
            LOGE("A null pointer have been detected");
            return AIENGINE_INVALID_PARAM;
        }

        // 限制不能取超过图片高度和宽度的像素点
        ref_rows = std::min(ref_rows, img_h);
        ref_cols = std::min(ref_cols, img_w);

        // 计算在x轴和y轴上的步进间隔（向下取整）
        int step_y = img_h / ref_rows;
        int step_x = img_w / ref_cols;

        unsigned long max_gray = 0; // 最大灰度值，注意此处变量类型必须为无符号数！确保后续右移操作为逻辑右移

        pixel_t *src = (pixel_t *)img_ptr + start_y * row_stride + start_x;
        pixel_t *datum_mark = src + ((step_y / 2) * row_stride) + (step_x / 2); // 取中心区域的点做判断，这里计算中心区域左上角点坐标
        for (int i = 0; i < ref_rows; i++)
        {
            pixel_t *pImg = datum_mark + (i * step_y * row_stride);
            for (int j = 0; j < ref_cols; j++)
            {
                max_gray |= *pImg; // 通过或运算记录所有为1的位，用于接下来的子位深度提取范围计算
                pImg += step_x;
            }
        }

        // 计算子位深度提取范围
        int8_t sub_bit_range = 0;

        // 先右移八位，从bit0~bit7开始，逐个判断合适的子位深度提取范围
        max_gray = max_gray >> 8; 
        while (max_gray != 0x00)
        {
            max_gray = max_gray >> 1; // 原理：不断右移，直到所有位均为0时，累计右移的位数即为获取合适子位深度所需的右移位数（需确保此处右移为逻辑右移）
            sub_bit_range++;
        }

        return sub_bit_range;
    }

private:
template <int8_t rsn>
class Operator
{
public:
    /**
     * @brief    
     *           计算填充张量数值
     *           
     * @param    src:       指向存储像素的指针
     * @param    rsn:       像素右移位数（不做归一化时使用）
     *           
     * @retval   填充到张量的数值
     *           
     * @note     此函数为内联函数，内联确保代码嵌入到调用位置，使编译器可以最大程度优化代码
     *           
     * @date     2024-08-02 Created by HuangJP
     */
    inline tensor_t pixel_op(pixel_t *src)
    {
        // 判断是否执行归一化，enable_normalization是一个来自模板的形参，确保代码优化时只保留需要使用的分支
        if (enable_normalization)
        {
            return ((tensor_t)(*src) / (float)((0x0100 << rsn) - 1));
        }
        else
        {
            return (tensor_t)(((unsigned long)(*src) >> rsn) | (((unsigned long)(*src) >> (rsn + 8) ? (~0) : 0))); // unsigned long 确保逻辑右移
        }
    }

    /**
     * @brief    
     *           获取指定坐标点旋转后的数值
     *           
     * @param    s_x:           像素缩放后的x轴坐标
     * @param    s_y:           像素缩放后的y轴坐标
     * @param    cos_val:       旋转矩阵cos值
     * @param    sin_val:       旋转矩阵sin值
     * @param    centroid_x:    旋转中心x轴坐标
     * @param    centroid_y:    旋转中心y轴坐标
     * @param    rows:          原图高度
     * @param    cols:          原图宽度
     * @param    img_ptr:       指向原图存储数组的指针
     *           
     * @retval   指定坐标点旋转后的数值
     *           
     * @note     此函数为内联函数，内联确保代码嵌入到调用位置，使编译器可以最大程度优化代码
     *           
     * @date     2024-08-06 Created by HuangJP
     */
    inline pixel_t rotate_op(float s_x, float s_y, float cos_val, float sin_val, int centroid_x, int centroid_y, int rows, int cols, pixel_t *img_ptr)
    {
        // 旋转得到像素点在原图上的坐标
        float src_x = s_x * cos_val - s_y * sin_val + (float)centroid_x;
        float src_y = s_x * sin_val + s_y * cos_val + (float)centroid_y;
        // 双线性插值
        int x0 = (int)src_x;
        int y0 = (int)src_y;
        int x1 = x0 + 1;
        int y1 = y0 + 1;
        // 计算权重
        float dx = src_x - (float)x0;
        float dy = src_y - (float)y0;
        float w00 = (1.0f - dx) * (1.0f - dy);
        float w01 = dx * (1.0f - dy);
        float w10 = (1.0f - dx) * dy;
        float w11 = dx * dy;
        // 防止越界
        x0 = std::min(std::max(x0, 0), cols - 2);
        y0 = std::min(std::max(y0, 0), rows - 2);
        x1 = std::min(std::max(x1, 0), cols - 1);
        y1 = std::min(std::max(y1, 0), rows - 1);
        // 计算像素值
        pixel_t *src00 = img_ptr + x0 + y0 * cols; 
        pixel_t *src01 = src00 + (x1 - x0);
        pixel_t *src10 = src00 + (y1 - y0) * cols;
        pixel_t *src11 = src10 + (x1 - x0);
        pixel_t pix_val = ((float)*src00 * w00) + ((float)*src01 * w01) + ((float)*src10 * w10) + ((float)*src11 * w11);
        return pix_val;
    }

    /**
     * @brief    
     *           填充输入张量，输入四边形区域坐标，将四边形区域旋转摆正
     *           
     * @param    img_ptr:           指向原图存储数组的指针
     * @param    quad_points：      四边形区域坐标
     * @param    upside_down:       是否上下颠倒图像
     * @param    rows:              原图高度
     * @param    cols:              原图宽度
     * @param    data:              前处理数据
     * @param    prep:              前处理数据
     *           
     * @retval   错误码
     *           
     * @date     2024-08-06 Created by HuangJP
     */
    int _rotate_to_fill_input_tensor(BaseModelLoader::memory_format_t memory_format, pixel_t *img_ptr, QuadPoints quad_points, bool upside_down, int rows, int cols, PreprocessedData data)
    {
        //todo 支持upside_down参数
        // 设置旋转中心
        int centroid_x = (quad_points.x0 + quad_points.x1 + quad_points.x2 + quad_points.x3) / 4;
        int centroid_y = (quad_points.y0 + quad_points.y1 + quad_points.y2 + quad_points.y3) / 4;
        // 确定矩形高度和宽度
        float width1 = Geometry2D<int>::Calculate_Distance(quad_points.x0, quad_points.y0, quad_points.x1, quad_points.y1);
        float width2 = Geometry2D<int>::Calculate_Distance(quad_points.x2, quad_points.y2, quad_points.x3, quad_points.y3);
        float height1 = Geometry2D<int>::Calculate_Distance(quad_points.x3, quad_points.y3, quad_points.x0, quad_points.y0);
        float height2 = Geometry2D<int>::Calculate_Distance(quad_points.x1, quad_points.y1, quad_points.x2, quad_points.y2);
        float width = std::max(width1, width2);
        float height = std::max(height1, height2);
        // 高度或宽度不能为0
        if ((width <= 0.0f) || (height <= 0.0f))
        {
            LOGE("The width or height of the quadrangle cannot be less than 0.");
            return AIENGINE_INVALID_PARAM;
        }
        float slope = 1.0f;             // 包围框斜率
        float angle_radians = 0.0f;     // 旋转角度（单位: 弧度）
        float scale_w = 1.0f;           // 宽度方向缩放系数
        float scale_h_left = 1.0f;      // 高度方向缩放系数（左）
        float scale_h_right = 1.0f;     // 高度方向缩放系数（右）
        float bbox_width = 0;           // 包围框宽度
        int cpy_w = 0;                  // 宽度方向拷贝的像素数量
        int cpy_h = data.resize_h;      // 高度方向拷贝的像素数量
        // 计算包围框高度和宽度以及斜率
        bool is_landscape = width > height; // 是否是水平预测框
        // 水平方向
        if (is_landscape == true)
        {
            // 水平方向计算斜率
            float delta_y = ((float)(quad_points.y2 - quad_points.y3) + (float)(quad_points.y1 - quad_points.y0)) / 2.0f;
            float delta_x = ((float)(quad_points.x2 - quad_points.x3) + (float)(quad_points.x1 - quad_points.x0)) / 2.0f;
            slope = delta_y / delta_x;
            angle_radians = atan(slope); // 计算旋转角度
            // 计算左右两边的高度缩放系数
            scale_h_left = height1 / data.resize_h;
            scale_h_right = height2 / data.resize_h;
            // 赋值包围框宽度
            bbox_width = width;
        }
        // 竖直方向
        else
        {
            // 竖直方向计算斜率
            float delta_y = ((float)(quad_points.y1 - quad_points.y2) + (float)(quad_points.y0 - quad_points.y3)) / 2.0f;
            float delta_x = ((float)(quad_points.x1 - quad_points.x2) + (float)(quad_points.x0 - quad_points.x3)) / 2.0f;
            slope = delta_y / delta_x;
            slope = -1 / slope; // 求法线斜率
            angle_radians = atan(slope) + M_PI / 2; // 计算旋转角度
            // 计算左右两边的高度缩放系数
            scale_h_left = width1 / data.resize_h;
            scale_h_right = width2 / data.resize_h;
            // 赋值包围框宽度
            bbox_width = height;
        }
        // 获取拷贝宽度、计算宽度缩放系数
        scale_w = std::max(scale_h_left, scale_h_right);
        int estimate_width = bbox_width / scale_w;
        scale_w = (estimate_width <= data.resize_w) ? (scale_w) : (bbox_width / data.resize_w);
        cpy_w = std::min(data.resize_w, estimate_width);
        // 提前计算好每一个像素点高度方向的缩放系数
        std::vector<float> scale_h(cpy_w, 1.0f);
        for (int i = 0; i < cpy_w; i++)
        {
            float step = (float)i / (float)cpy_w;
            scale_h[i] = scale_h_left * (1 - step) + scale_h_right * step;
        }
        // 直方图
        unsigned long histogram[256] = {0x00};
        // NHWC内存存储格式
        if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
        {
            // 对三通道模型填充输入张量
            if (data.resize_c == 3)
            {
                is_landscape
                    ? _fill_input_tensor_rotate<true, 3>(angle_radians, centroid_x, centroid_y, scale_w, scale_h, cpy_w, cpy_h, data, rows, cols, img_ptr, histogram)
                    : _fill_input_tensor_rotate<false, 3>(angle_radians, centroid_x, centroid_y, scale_w, scale_h, cpy_w, cpy_h, data, rows, cols, img_ptr, histogram);
            }
            // 对单通道模型填充输入张量
            else if(data.resize_c == 1)
            {
                is_landscape
                    ? _fill_input_tensor_rotate<true, 1>(angle_radians, centroid_x, centroid_y, scale_w, scale_h, cpy_w, cpy_h, data, rows, cols, img_ptr, histogram)
                    : _fill_input_tensor_rotate<false, 1>(angle_radians, centroid_x, centroid_y, scale_w, scale_h, cpy_w, cpy_h, data, rows, cols, img_ptr, histogram);
            }
            else
            {
                LOGE("Unsupported model channel number");
                LOGD("Channel number: %d", data.resize_c);
                return AIENGINE_INVALID_PARAM;
            }
        }
        // NCHW内存存储格式
        else if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
        {
            // 限制模型的通道数在1-3通道
            if ((data.resize_c >= 1)
                && (data.resize_c <= 3))
            {
                // 处理单通道的数据
                is_landscape
                    ? _fill_input_tensor_rotate<true, 1>(angle_radians, centroid_x, centroid_y, scale_w, scale_h, cpy_w, cpy_h, data, rows, cols, img_ptr, histogram)
                    : _fill_input_tensor_rotate<false, 1>(angle_radians, centroid_x, centroid_y, scale_w, scale_h, cpy_w, cpy_h, data, rows, cols, img_ptr, histogram);
                // 拷贝相同的数据数据到每个通道
                size_t data_size = data.resize_h * data.resize_w;
                for (int i = 1; i < data.resize_c; i++)
                {
                    memcpy((tensor_t *)data.input_tensor_ptr + i * data_size, (tensor_t *)data.input_tensor_ptr, data_size);
                }
            }
            else
            {
                LOGE("Unsupported model channel number");
                LOGD("Channel number: %d", data.resize_c);
                return AIENGINE_INVALID_PARAM;
            }
        }
        // 其它不支持的内存存储格式
        else
        {
            LOGE("Unsupported input tensor memory format.");
            return AIENGINE_INVALID_PARAM;
        }
        // 亮度过滤器
        auto brightness_filter = [](unsigned long *hist, float num_pixels){
            // 检查传入参数是否合法
            if (hist == nullptr)
            {
                return false;
            }
            // 统计过亮像素占比
            float overbright_proportion = 0;
            for (int i = 200; i < 256; i++)
            {
                overbright_proportion += hist[i];
            }
            // 判断图像是否过亮
            overbright_proportion /= (float)num_pixels;
            if (overbright_proportion > 0.7f)
            {
                return false;
            }
            return true;
        };
        // 过滤亮度过高的检测图片
        if (brightness_filter(histogram, cpy_h * cpy_w) == false)
        {
            LOGE("The input image is overbright.");
            return AIENGINE_INPUT_DATA_ERROR;
        }
        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           缩放图片以填充至输入张量
     *           
     * @param    img_ptr:           指向原图存放数组的指针
     * @param    img_h,img_w:       原图高度和原图宽度
     * @param    start_y,start_x:   检测区域起始坐标
     * @param    det_h,det_w:       检测区域高度和宽度
     * @param    memory_format:     输入张量内存存储格式
     * @param    integer_factor:    是否使用整数缩放系数
     * @param    keep_ratio:        是否确保宽度和高度方向上缩放系数保持一致，避免图像变形
     * @param    prep:              前处理数据
     *           
     * @retval   错误码
     *           
     * @date     2024-09-03 Created by HuangJP
     */
    int _resize_to_fill_input_tensor(BaseModelLoader::memory_format_t memory_format, pixel_t *img_ptr, int img_h, int img_w, int start_y, int start_x, int det_h, int det_w, bool integer_factor, bool keep_ratio, PreprocessedData &prep)
    {
        // 检查输入参数是否合法
        if (img_ptr == nullptr)
        {
            LOGE("A null pointer have been detected");
            return AIENGINE_INVALID_PARAM;
        }
        // 检测图像范围不能超出原图范围
        if ((start_x < 0)
            || (start_y < 0)
            || (start_x + det_w > img_w)
            || (start_y + det_h > img_h))
        {
            LOGE("The detected area exceeds the original image size. Please ensure that the bounding box coordinates are within the boundaries of the input image.");
            return AIENGINE_ROI_COORD_ERROR;
        }
        // 第一步：计算高度和宽度方向上的缩放系数
        float scale_h = det_h <= prep.resize_h ? 1.0f
                            : (float)det_h / (float)prep.resize_h;
        float scale_w = det_w <= prep.resize_w ? 1.0f
                            : (float)det_w / (float)prep.resize_w;
        // 第二步：计算拷贝高度和宽度
        int cpy_h = std::min(det_h, prep.resize_h);
        int cpy_w = std::min(det_w, prep.resize_w);
        // 第三步：判断是否保持高度和宽度方向上的缩放系数一致，即不让缩放后的图像产生变形
        if (keep_ratio == true)
        {
            // 获取最大的缩放系数
            float scale_factor = std::max(scale_h, scale_w);
            scale_h = scale_factor;
            scale_w = scale_factor;
            // 重新计算拷贝高度和宽度
            cpy_h = det_h / scale_h;
            cpy_w = det_w / scale_w;
        }
        // 第四步：判断是否使用整数缩放系数
        if (integer_factor == true)
        {
            // 对缩放系数取整
            scale_h = ceil(scale_h);
            scale_w = ceil(scale_w);
            // 重新计算拷贝高度和宽度
            cpy_h = det_h / scale_h;
            cpy_w = det_w / scale_w;
        }
        // 保存缩放系数、起始检测坐标和检测区域大小，用于后处理还原结果
        prep.scale_h = scale_h;
        prep.scale_w = scale_w;
        prep.start_y = start_y;
        prep.start_x = start_x;
        prep.det_h = det_h;
        prep.det_w = det_w;
        // 判断宽度方向上的缩放系数是否为整数
        bool int_scale_w = (scale_w - floor(scale_w)) == 0.0f; // 判断是否有小数位
        int rs = AIENGINE_NO_ERROR;
        // 填充输入张量
        if (int_scale_w == true)
        {
            // 宽度方向缩放系数为整数倍，计算宽度方向的步进长度，高度方向按变步长缩放
            int x_step = (int)scale_w;
            // 填充输入张量
            rs = this->_fill_input_tensor_fix_step(memory_format, (pixel_t *)img_ptr + (start_x + start_y * img_w), img_w, scale_h, x_step, cpy_h, cpy_w, prep);
        }
        else
        {
            // 宽度方向缩放系数并非整数倍，两个方向均按变步长缩放
            rs = this->_fill_input_tensor_variable_step(memory_format, (pixel_t *)img_ptr + (start_x + start_y * img_w), img_w, scale_h, scale_w, cpy_h, cpy_w, prep);
        }
        return rs; // 返回执行结果
    }

    /**
     * @brief    
     *           旋转缩放前处理填充输入张量
     *           
     * @param    is_landscape:          是否是水平预测框
     * @param    num_channel:           输入张量通道数
     * @param    angle_radians:         旋转角度（单位: 弧度）
     * @param    centroid_x:            旋转中心x轴坐标值
     * @param    centroid_y:            旋转中心y轴坐标值
     * @param    scale_w:               宽度方向缩放系数
     * @param    scale_h:               每列像素高度方向上的缩放系数
     * @param    cpy_w:                 宽度方向拷贝的像素数量
     * @param    cpy_h:                 高度方向拷贝的像素数量
     * @param    data:                  前处理数据
     * @param    rows:                  原图高度
     * @param    cols:                  原图宽度
     * @param    img_ptr:               指向原图存储数组的指针
     * @param    rsn:                   图像像素右移位数(Right Shift Number)，用于提取指定子位深度(sub bit depth)
     * @param    histogram:             统计灰度直方图
     *           
     * @date     2024-08-14 Created by HuangJP
     */
    template<bool is_landscape, int num_channel>
    void _fill_input_tensor_rotate(float angle_radians, int centroid_x, int centroid_y, float scale_w, std::vector<float> &scale_h, int cpy_w, int cpy_h, PreprocessedData data, int rows, int cols, pixel_t *img_ptr, unsigned long *histogram)
    {
        int start_x = -cpy_w / 2; // 起始x坐标
        int start_y = -cpy_h / 2; // 起始y坐标
        float cos_val = cos(angle_radians); // 旋转矩阵cos值
        float sin_val = sin(angle_radians); // 旋转矩阵sin值
        // 填充水平预测框
        if (is_landscape == true)
        {
            // 按照列优先填充输入张量
            for (int i = 0; i < cpy_h; i++)
            {
                tensor_t *dst = (tensor_t *)data.input_tensor_ptr + i * data.resize_w * num_channel;
                for (int j = 0; j < cpy_w; j++)
                {
                    // 计算缩放后的坐标点
                    float s_x = (start_x + j) * scale_w;
                    float s_y = (start_y + i) * scale_h[j];
                    // 获取旋转后的像素值
                    pixel_t pix_val = rotate_op(s_x, s_y, cos_val, sin_val, centroid_x, centroid_y, rows, cols, img_ptr);
                    // 统计直方图
                    histogram[pix_val>>rsn]++;
                    // 填充数据
                    tensor_t tmp = pixel_op(&pix_val);
                    for (int k = 0; k < num_channel; k++)
                    {
                        *(dst+k) = tmp;
                    }
                    dst += num_channel;
                }
            }
        }
        // 填充竖直预测框
        else
        {
            // 按照行优先填充输入张量
            for (int j = 0; j < cpy_w; j++)
            {
                tensor_t *dst = (tensor_t *)data.input_tensor_ptr + j * num_channel;
                for (int i = 0; i < cpy_h; i++)
                {
                    // 计算缩放后的坐标点
                    float s_x = (start_x + j) * scale_w;
                    float s_y = (start_y + i) * scale_h[j];
                    // 获取旋转后的像素值
                    pixel_t pix_val = rotate_op(s_x, s_y, cos_val, sin_val, centroid_x, centroid_y, rows, cols, img_ptr);
                    // 统计直方图
                    uint8_t gray = ((unsigned long)pix_val >> rsn) | (((unsigned long)pix_val >> (rsn + 8) ? (~0) : 0));
                    histogram[gray]++;
                    // 填充数据
                    tensor_t tmp = pixel_op(&pix_val);
                    for (int k = 0; k < num_channel; k++)
                    {
                        *(dst+k) = tmp;
                    }
                    dst += data.resize_w * num_channel;
                }
            }
        }
    }

    /**
     * @brief    
     *           仅宽度方向固定步长填充输入张量，高度方向变步长填充输入张量
     *           
     * @param    src_pt:        指向原图指定位置的指针
     * @param    img_w:         原图宽度
     * @param    scale_y:       y轴方向缩放系数
     * @param    x_step:        x轴步进长度
     * @param    cpy_h:         拷贝高度
     * @param    cpy_w:         拷贝宽度
     * @param    prep:          前处理数据
     *           
     * @retval   错误码
     *           
     * @date     2024-09-03 Created by HuangJP
     */
    int _fill_input_tensor_fix_step(BaseModelLoader::memory_format_t memory_format, pixel_t *src_pt, int img_w, float scale_y, int x_step, int cpy_h, int cpy_w, PreprocessedData prep)
    {
        // 输入张量内存存储格式为NHWC
        if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
        {
            // 对三通道模型填充输入张量
            if (prep.resize_c == 3)
            {
                float height_idx = 0.0f; // 行高索引
                for (int i = 0; i < cpy_h; i++)
                {
                    tensor_t *dst = (tensor_t *)prep.input_tensor_ptr + i * prep.resize_w * 3; // 获取输入张量当前行首个元素的地址
                    pixel_t *src = src_pt + (int)(height_idx + 0.5f) * img_w; // 获取输入图片当前行首个像素的地址
                    height_idx += scale_y; // 更新行高索引
                    for (int j = 0; j < cpy_w; j++)
                    {
                        // 逐像素填充输入张量
                        tensor_t tmp = pixel_op(src);
                        *(dst+0) = tmp;
                        *(dst+1) = tmp;
                        *(dst+2) = tmp;
                        dst += 3;
                        src += x_step;
                    }
                }
            }
            // 对单通道模型填充输入张量
            else if (prep.resize_c == 1)
            {
                float height_idx = 0.0f; // 行高索引
                for (int i = 0; i < cpy_h; i++)
                {
                    tensor_t *dst = (tensor_t *)prep.input_tensor_ptr + i * prep.resize_w; // 获取输入张量当前行首个元素的地址
                    pixel_t *src = src_pt + (int)(height_idx + 0.5f) * img_w; // 获取输入图片当前行首个像素的地址
                    height_idx += scale_y; // 更新行高索引
                    for (int j = 0; j < cpy_w; j++)
                    {
                        // 逐像素填充输入张量
                        *dst = pixel_op(src);
                        dst++;
                        src += x_step;
                    }
                }
            }
            else
            {
                LOGE("Unsupported model channel number");
                LOGD("Channel number: %d", prep.resize_c);
                return AIENGINE_INVALID_PARAM;
            }
        }
        // 输入张量内存存储格式为NCHW
        else if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
        {
            // 限制模型的通道数在1-3通道
            if ((prep.resize_c >= 1)
                && (prep.resize_c <= 3))
            {
                float height_idx = 0.0f; // 行高索引
                for (int i = 0; i < cpy_h; i++)
                {
                    tensor_t *dst = (tensor_t *)prep.input_tensor_ptr + i * prep.resize_w; // 获取输入张量当前行首个元素的地址
                    pixel_t *src = src_pt + (int)(height_idx + 0.5f) * img_w; // 获取输入图片当前行首个像素的地址
                    height_idx += scale_y; // 更新行高索引
                    for (int j = 0; j < cpy_w; j++)
                    {
                        // 逐像素填充输入张量
                        *dst = pixel_op(src);
                        dst++;
                        src += x_step;
                    }
                }
                // 拷贝相同的数据数据到每个通道
                size_t data_size = prep.resize_h * prep.resize_w;
                for (int i = 1; i < prep.resize_c; i++)
                {
                    memcpy((tensor_t *)prep.input_tensor_ptr + i * data_size, (tensor_t *)prep.input_tensor_ptr, data_size);
                }
            }
            else
            {
                LOGE("Unsupported model channel number");
                LOGD("Channel number: %d", prep.resize_c);
                return AIENGINE_INVALID_PARAM;
            }
        }
        else
        {
            LOGE("Unsupported input tensor memory format.");
            return AIENGINE_INVALID_PARAM;
        }
        return AIENGINE_NO_ERROR;
    }
    /**
     * @brief    
     *           高度和宽度方向均变步长填充输入张量
     *           
     * @param    src_pt:        指向原图指定位置的指针
     * @param    img_w:         原图宽度
     * @param    scale_y:       y轴方向缩放系数
     * @param    scale_x:       x轴方向缩放系数
     * @param    cpy_h:         拷贝高度
     * @param    cpy_w:         拷贝宽度
     * @param    data:          前处理数据
     *           
     * @retval   错误码
     *           
     * @date     2024-09-03 Created by HuangJP
     */
    int _fill_input_tensor_variable_step(BaseModelLoader::memory_format_t memory_format, pixel_t *src_pt, int img_w, float scale_y, float scale_x, int cpy_h, int cpy_w, PreprocessedData prep)
    {
        // 计算宽度索引数组
        std::vector<int> width_idxes(cpy_w, 0);
        float width_idx = 0.0f;
        for (int i = 0; i < cpy_w; i++)
        {
            width_idxes[i] = (int)(width_idx + 0.5f);
            width_idx += scale_x;
        }
        // 输入张量内存存储格式为NHWC
        if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
        {
            // 对三通道模型填充输入张量
            if (prep.resize_c == 3)
            {
                float height_idx = 0.0f; // 行高索引
                for (int i = 0; i < cpy_h; i++)
                {
                    tensor_t *dst = (tensor_t *)prep.input_tensor_ptr + i * prep.resize_w * 3; // 获取输入张量当前行首个元素的地址
                    pixel_t *src = src_pt + (int)(height_idx + 0.5f) * img_w; // 获取输入图片当前行首个像素的地址
                    height_idx += scale_y; // 更新行高索引
                    for (int j = 0; j < cpy_w; j++)
                    {
                        // 逐像素填充输入张量
                        tensor_t tmp = pixel_op(&src[width_idxes[j]]);
                        *(dst+0) = tmp;
                        *(dst+1) = tmp;
                        *(dst+2) = tmp;
                        dst += 3;
                    }
                }
            }
            // 对单通道模型填充输入张量
            else if (prep.resize_c == 1)
            {
                float height_idx = 0.0f; // 行高索引
                for (int i = 0; i < cpy_h; i++)
                {
                    tensor_t *dst = (tensor_t *)prep.input_tensor_ptr + i * prep.resize_w; // 获取输入张量当前行首个元素的地址
                    pixel_t *src = src_pt + (int)(height_idx + 0.5f) * img_w; // 获取输入图片当前行首个像素的地址
                    height_idx += scale_y; // 更新行高索引
                    for (int j = 0; j < cpy_w; j++)
                    {
                        // 逐像素填充输入张量
                        *dst = pixel_op(&src[width_idxes[j]]);
                        dst++;
                    }
                }
            }
            else
            {
                LOGE("Unsupported model channel number");
                LOGD("Channel number: %d", prep.resize_c);
                return AIENGINE_INVALID_PARAM;
            }
        }
        // 输入张量内存存储格式为NCHW
        else if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
        {
            // 限制模型的通道数在1-3通道
            if ((prep.resize_c >= 1)
                && (prep.resize_c <= 3))
            {
                float height_idx = 0.0f; // 行高索引
                for (int i = 0; i < cpy_h; i++)
                {
                    tensor_t *dst = (tensor_t *)prep.input_tensor_ptr + i * prep.resize_w; // 获取输入张量当前行首个元素的地址
                    pixel_t *src = src_pt + (int)(height_idx + 0.5f) * img_w; // 获取输入图片当前行首个像素的地址
                    height_idx += scale_y; // 更新行高索引
                    for (int j = 0; j < cpy_w; j++)
                    {
                        // 逐像素填充输入张量
                        *dst = pixel_op(&src[width_idxes[j]]);
                        dst++;
                    }
                }
                // 拷贝相同的数据数据到每个通道
                size_t data_size = prep.resize_h * prep.resize_w;
                for (int i = 1; i < prep.resize_c; i++)
                {
                    memcpy((tensor_t *)prep.input_tensor_ptr + i * data_size, (tensor_t *)prep.input_tensor_ptr, data_size);
                }
            }
            else
            {
                LOGE("Unsupported model channel number");
                LOGD("Channel number: %d", prep.resize_c);
                return AIENGINE_INVALID_PARAM;
            }
        }
        else
        {
            LOGE("Unsupported input tensor memory format.");
            return AIENGINE_INVALID_PARAM;
        }
        return AIENGINE_NO_ERROR;
    }
}; /* class Operator */

    Operator<0> op0;
    Operator<1> op1;
    Operator<2> op2;
    Operator<3> op3;
    Operator<4> op4;
};

/**
 * @brief    
 *           获取输入图像工具实例
 *           
 * @param    bit_depth:                 图像位深度
 * @param    color_format:              图片颜色格式
 * @param    memory_format:             输入张量内存存储格式
 *           
 * @retval   
 *           
 * @date     2024-10-31 Created by HuangJP
 */
template <typename tensor_t, bool enable_normalization>
inline InputImage *_Get_Input_Image_Instance(bit_depth_t bit_depth, color_format_t color_format, BaseModelLoader::memory_format_t memory_format)
{
    InputImage *input_image = nullptr;

    switch (color_format)
    {
        case COLOR_FORMAT_GRAY:
        {
            switch (bit_depth)
            {
                case BIT_DEPTH_RAW8:
                {
                    static auto input_gray_image = InputGrayImage<uint8_t, tensor_t, enable_normalization>();
                    input_image = (InputImage *)&input_gray_image;
                    break;
                }
                case BIT_DEPTH_RAW10:
                case BIT_DEPTH_RAW12:
                {
                    static auto input_gray_image = InputGrayImage<uint16_t, tensor_t, enable_normalization>();
                    input_image = (InputImage *)&input_gray_image;
                    break;
                }
                default:
                    LOGE("Unsupported bit depth.");
                    break;
            }
            break;
        }
        default:
        {
            LOGE("Unsupported color format");
            break;
        }
    }

    // 设置内存存储格式
    if (input_image != nullptr)
    {
        input_image->set_memory_format(memory_format);
    }

    return input_image;
}

#endif
//-----------------------------------------------------------------------------
//  End of file