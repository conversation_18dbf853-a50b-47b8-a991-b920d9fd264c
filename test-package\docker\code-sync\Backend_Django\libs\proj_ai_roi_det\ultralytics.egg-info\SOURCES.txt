LICENSE
README.md
pyproject.toml
tests/test_cli.py
tests/test_cuda.py
tests/test_engine.py
tests/test_explorer.py
tests/test_exports.py
tests/test_integrations.py
tests/test_python.py
tests/test_solutions.py
ultralytics/__init__.py
ultralytics.egg-info/PKG-INFO
ultralytics.egg-info/SOURCES.txt
ultralytics.egg-info/dependency_links.txt
ultralytics.egg-info/entry_points.txt
ultralytics.egg-info/requires.txt
ultralytics.egg-info/top_level.txt
ultralytics/../tests/__init__.py
ultralytics/../tests/conftest.py
ultralytics/../tests/test_cli.py
ultralytics/../tests/test_cuda.py
ultralytics/../tests/test_engine.py
ultralytics/../tests/test_explorer.py
ultralytics/../tests/test_exports.py
ultralytics/../tests/test_integrations.py
ultralytics/../tests/test_python.py
ultralytics/../tests/test_solutions.py
ultralytics/assets/bus.jpg
ultralytics/assets/zidane.jpg
ultralytics/cfg/__init__.py
ultralytics/cfg/default.yaml
ultralytics/cfg/datasets/Argoverse.yaml
ultralytics/cfg/datasets/DOTAv1.5.yaml
ultralytics/cfg/datasets/DOTAv1.yaml
ultralytics/cfg/datasets/GlobalWheat2020.yaml
ultralytics/cfg/datasets/ImageNet.yaml
ultralytics/cfg/datasets/Objects365.yaml
ultralytics/cfg/datasets/SKU-110K.yaml
ultralytics/cfg/datasets/VOC.yaml
ultralytics/cfg/datasets/VisDrone.yaml
ultralytics/cfg/datasets/african-wildlife.yaml
ultralytics/cfg/datasets/brain-tumor.yaml
ultralytics/cfg/datasets/carparts-seg.yaml
ultralytics/cfg/datasets/coco-pose.yaml
ultralytics/cfg/datasets/coco.yaml
ultralytics/cfg/datasets/coco128-seg.yaml
ultralytics/cfg/datasets/coco128.yaml
ultralytics/cfg/datasets/coco8-pose.yaml
ultralytics/cfg/datasets/coco8-seg.yaml
ultralytics/cfg/datasets/coco8.yaml
ultralytics/cfg/datasets/crack-seg.yaml
ultralytics/cfg/datasets/dota8.yaml
ultralytics/cfg/datasets/hand-keypoints.yaml
ultralytics/cfg/datasets/lvis.yaml
ultralytics/cfg/datasets/open-images-v7.yaml
ultralytics/cfg/datasets/package-seg.yaml
ultralytics/cfg/datasets/signature.yaml
ultralytics/cfg/datasets/tiger-pose.yaml
ultralytics/cfg/datasets/xView.yaml
ultralytics/cfg/models/11/yolo11-cls.yaml
ultralytics/cfg/models/11/yolo11-obb.yaml
ultralytics/cfg/models/11/yolo11-pose.yaml
ultralytics/cfg/models/11/yolo11-seg.yaml
ultralytics/cfg/models/11/yolo11.yaml
ultralytics/cfg/models/rt-detr/rtdetr-l.yaml
ultralytics/cfg/models/rt-detr/rtdetr-resnet101.yaml
ultralytics/cfg/models/rt-detr/rtdetr-resnet50.yaml
ultralytics/cfg/models/rt-detr/rtdetr-x.yaml
ultralytics/cfg/models/v10/yolov10b.yaml
ultralytics/cfg/models/v10/yolov10l.yaml
ultralytics/cfg/models/v10/yolov10m.yaml
ultralytics/cfg/models/v10/yolov10n.yaml
ultralytics/cfg/models/v10/yolov10s.yaml
ultralytics/cfg/models/v10/yolov10x.yaml
ultralytics/cfg/models/v3/yolov3-spp.yaml
ultralytics/cfg/models/v3/yolov3-tiny.yaml
ultralytics/cfg/models/v3/yolov3.yaml
ultralytics/cfg/models/v5/yolov5-p6.yaml
ultralytics/cfg/models/v5/yolov5.yaml
ultralytics/cfg/models/v6/yolov6.yaml
ultralytics/cfg/models/v8/yolov8-cls-resnet101.yaml
ultralytics/cfg/models/v8/yolov8-cls-resnet50.yaml
ultralytics/cfg/models/v8/yolov8-cls.yaml
ultralytics/cfg/models/v8/yolov8-ghost-p2.yaml
ultralytics/cfg/models/v8/yolov8-ghost-p6.yaml
ultralytics/cfg/models/v8/yolov8-ghost.yaml
ultralytics/cfg/models/v8/yolov8-gray.yaml
ultralytics/cfg/models/v8/yolov8-obb.yaml
ultralytics/cfg/models/v8/yolov8-p2.yaml
ultralytics/cfg/models/v8/yolov8-p6.yaml
ultralytics/cfg/models/v8/yolov8-pose-p6.yaml
ultralytics/cfg/models/v8/yolov8-pose.yaml
ultralytics/cfg/models/v8/yolov8-rtdetr.yaml
ultralytics/cfg/models/v8/yolov8-seg-p6.yaml
ultralytics/cfg/models/v8/yolov8-seg.yaml
ultralytics/cfg/models/v8/yolov8-world.yaml
ultralytics/cfg/models/v8/yolov8-worldv2.yaml
ultralytics/cfg/models/v8/yolov8.yaml
ultralytics/cfg/models/v9/yolov9c-seg.yaml
ultralytics/cfg/models/v9/yolov9c.yaml
ultralytics/cfg/models/v9/yolov9e-seg.yaml
ultralytics/cfg/models/v9/yolov9e.yaml
ultralytics/cfg/models/v9/yolov9m.yaml
ultralytics/cfg/models/v9/yolov9s.yaml
ultralytics/cfg/models/v9/yolov9t.yaml
ultralytics/cfg/trackers/botsort.yaml
ultralytics/cfg/trackers/bytetrack.yaml
ultralytics/data/__init__.py
ultralytics/data/annotator.py
ultralytics/data/augment.py
ultralytics/data/base.py
ultralytics/data/build.py
ultralytics/data/converter.py
ultralytics/data/dataset.py
ultralytics/data/loaders.py
ultralytics/data/split_dota.py
ultralytics/data/utils.py
ultralytics/data/explorer/__init__.py
ultralytics/data/explorer/explorer.py
ultralytics/data/explorer/utils.py
ultralytics/data/explorer/gui/__init__.py
ultralytics/data/explorer/gui/dash.py
ultralytics/engine/__init__.py
ultralytics/engine/exporter.py
ultralytics/engine/model.py
ultralytics/engine/predictor.py
ultralytics/engine/results.py
ultralytics/engine/trainer.py
ultralytics/engine/tuner.py
ultralytics/engine/validator.py
ultralytics/hub/__init__.py
ultralytics/hub/auth.py
ultralytics/hub/session.py
ultralytics/hub/utils.py
ultralytics/hub/google/__init__.py
ultralytics/models/__init__.py
ultralytics/models/fastsam/__init__.py
ultralytics/models/fastsam/model.py
ultralytics/models/fastsam/predict.py
ultralytics/models/fastsam/utils.py
ultralytics/models/fastsam/val.py
ultralytics/models/nas/__init__.py
ultralytics/models/nas/model.py
ultralytics/models/nas/predict.py
ultralytics/models/nas/val.py
ultralytics/models/rtdetr/__init__.py
ultralytics/models/rtdetr/model.py
ultralytics/models/rtdetr/predict.py
ultralytics/models/rtdetr/train.py
ultralytics/models/rtdetr/val.py
ultralytics/models/sam/__init__.py
ultralytics/models/sam/amg.py
ultralytics/models/sam/build.py
ultralytics/models/sam/model.py
ultralytics/models/sam/predict.py
ultralytics/models/sam/modules/__init__.py
ultralytics/models/sam/modules/blocks.py
ultralytics/models/sam/modules/decoders.py
ultralytics/models/sam/modules/encoders.py
ultralytics/models/sam/modules/memory_attention.py
ultralytics/models/sam/modules/sam.py
ultralytics/models/sam/modules/tiny_encoder.py
ultralytics/models/sam/modules/transformer.py
ultralytics/models/sam/modules/utils.py
ultralytics/models/utils/__init__.py
ultralytics/models/utils/loss.py
ultralytics/models/utils/ops.py
ultralytics/models/yolo/__init__.py
ultralytics/models/yolo/model.py
ultralytics/models/yolo/classify/__init__.py
ultralytics/models/yolo/classify/predict.py
ultralytics/models/yolo/classify/train.py
ultralytics/models/yolo/classify/val.py
ultralytics/models/yolo/detect/__init__.py
ultralytics/models/yolo/detect/predict.py
ultralytics/models/yolo/detect/train.py
ultralytics/models/yolo/detect/val.py
ultralytics/models/yolo/obb/__init__.py
ultralytics/models/yolo/obb/predict.py
ultralytics/models/yolo/obb/train.py
ultralytics/models/yolo/obb/val.py
ultralytics/models/yolo/pose/__init__.py
ultralytics/models/yolo/pose/predict.py
ultralytics/models/yolo/pose/train.py
ultralytics/models/yolo/pose/val.py
ultralytics/models/yolo/segment/__init__.py
ultralytics/models/yolo/segment/predict.py
ultralytics/models/yolo/segment/train.py
ultralytics/models/yolo/segment/val.py
ultralytics/models/yolo/world/__init__.py
ultralytics/models/yolo/world/train.py
ultralytics/models/yolo/world/train_world.py
ultralytics/nn/__init__.py
ultralytics/nn/autobackend.py
ultralytics/nn/tasks.py
ultralytics/nn/modules/__init__.py
ultralytics/nn/modules/activation.py
ultralytics/nn/modules/block.py
ultralytics/nn/modules/conv copy.py
ultralytics/nn/modules/conv.py
ultralytics/nn/modules/head.py
ultralytics/nn/modules/transformer.py
ultralytics/nn/modules/utils.py
ultralytics/nn/modules/wtconv/__init__.py
ultralytics/nn/modules/wtconv/wtconv2d.py
ultralytics/nn/modules/wtconv/util/__init__.py
ultralytics/nn/modules/wtconv/util/wavelet.py
ultralytics/solutions/__init__.py
ultralytics/solutions/ai_gym.py
ultralytics/solutions/analytics.py
ultralytics/solutions/distance_calculation.py
ultralytics/solutions/heatmap.py
ultralytics/solutions/object_counter.py
ultralytics/solutions/parking_management.py
ultralytics/solutions/queue_management.py
ultralytics/solutions/speed_estimation.py
ultralytics/solutions/streamlit_inference.py
ultralytics/trackers/__init__.py
ultralytics/trackers/basetrack.py
ultralytics/trackers/bot_sort.py
ultralytics/trackers/byte_tracker.py
ultralytics/trackers/track.py
ultralytics/trackers/utils/__init__.py
ultralytics/trackers/utils/gmc.py
ultralytics/trackers/utils/kalman_filter.py
ultralytics/trackers/utils/matching.py
ultralytics/utils/__init__.py
ultralytics/utils/autobatch.py
ultralytics/utils/benchmarks.py
ultralytics/utils/checks.py
ultralytics/utils/dist.py
ultralytics/utils/distill_loss.py
ultralytics/utils/downloads.py
ultralytics/utils/errors.py
ultralytics/utils/files.py
ultralytics/utils/instance.py
ultralytics/utils/loss.py
ultralytics/utils/metrics.py
ultralytics/utils/ops.py
ultralytics/utils/patches.py
ultralytics/utils/plotting.py
ultralytics/utils/tal.py
ultralytics/utils/torch_utils.py
ultralytics/utils/triton.py
ultralytics/utils/tuner.py
ultralytics/utils/callbacks/__init__.py
ultralytics/utils/callbacks/base.py
ultralytics/utils/callbacks/clearml.py
ultralytics/utils/callbacks/comet.py
ultralytics/utils/callbacks/dvc.py
ultralytics/utils/callbacks/hub.py
ultralytics/utils/callbacks/mlflow.py
ultralytics/utils/callbacks/neptune.py
ultralytics/utils/callbacks/raytune.py
ultralytics/utils/callbacks/tensorboard.py
ultralytics/utils/callbacks/wb.py