import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
    TransformWrapper,
    TransformComponent,
} from 'react-zoom-pan-pinch';
import { useImageWorkspace } from '../contexts/ImageWorkspaceContext';
import { useBarcodeDetectionParams } from '../contexts/BarcodeDetectionContext'; // 导入条码检测上下文
import { useOcrDetectionParams } from '../contexts/OcrDetectionContext'; // 导入OCR检测上下文
import { useAiRestoreDetectionParams } from '../contexts/AiRestoreDetectionContext'; // <--- ADDED: Import AI Restore context
import { Button, message } from 'antd';
import { LeftOutlined, RightOutlined, PictureOutlined } from '@ant-design/icons';
import { useImageDropUpload } from './ImageDisplay/hooks/useImageDropUpload'; // Adjusted path
import { useRoiInteraction /* , GetCoordinatesRelativeToImageFn */ } from './ImageDisplay/hooks/useRoiInteraction'; // Import the new hook and types
import { useMouseCoordinates } from './ImageDisplay/hooks/useMouseCoordinates'; // Import the new hook
import { useImagePanZoom } from './ImageDisplay/hooks/useImagePanZoom'; // Import the new hook
import SvgOverlay from './ImageDisplay/SvgOverlay'; // Import the SvgOverlay component
import { useScanner } from '../contexts/ScannerContext'; // Import scanner context
import VideoStreamPlayer from './VideoStreamPlayer'; // Import the new video player component

// 声明全局window对象的扩展属性
declare global {
  interface Window {
    runBarcodeDetection?: () => Promise<void>;
    runOcrInference?: () => Promise<void>;
    clearDetectionResults?: () => Promise<void>;
    runAiImageRestore?: () => Promise<void>; // <--- ADDED: For AI Restore
  }
}

const ImageDisplay: React.FC = () => {
  const { inputSource } = useScanner(); // Get the input source from context
  // transformWrapperRef is now managed by useImagePanZoom
  const imageContainerRef = useRef<HTMLDivElement | null>(null); // Ref for the div containing image and SVG
  const imageRef = useRef<HTMLImageElement | null>(null); // Ref for the actual <img> element
  const svgOverlayRef = useRef<SVGSVGElement | null>(null); // Ref for the SVG overlay element

  // 获取条码检测和OCR检测的自动推理状态
  const { autoInferenceEnabled: barcodeAutoInference } = useBarcodeDetectionParams();
  const { autoInferenceEnabled: ocrAutoInference } = useOcrDetectionParams();
  const { autoInferenceEnabled: aiRestoreAutoInference } = useAiRestoreDetectionParams(); // <--- ADDED: Get AI Restore auto inference state

  // 添加智能检测进行中的状态标志
  const [isAutoInferenceRunning, setIsAutoInferenceRunning] = useState<boolean>(false);
  // 添加智能检测的消息提示关键字
  const autoInferenceMessageKey = 'autoInferenceMessage';

  const {
    isDraggingOver,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    imageList, // Consumed by showNavigationButtons
    currentImageIndex, // Consumed by navigation buttons
    handlePrevClick: originalHandlePrevClick, // 重命名为originalHandlePrevClick
    handleNextClick: originalHandleNextClick, // 重命名为originalHandleNextClick
    showNavigationButtons,
  } = useImageDropUpload();

  const {
    currentImageInfo,
    detectionResults,
    isSelectingRoi,
    selectedRoi, // Get selectedRoi for display
    setSelectedRoiCoordinates, // Get the new setter
    completeRoiSelection, // Get the new method to exit ROI mode
    setMouseImageCoordinates, // Get the setter for mouse coordinates
    setDetectionResults, // 从上下文中获取清除检测结果的方法
    isBatchProcessingActive, // <--- ADDED: Get batch processing state
  } = useImageWorkspace();

  // Pan and Zoom logic including transformWrapperRef and capture logic
  const {
    transformWrapperRef,
  } = useImagePanZoom({
    currentImageInfo,
    imageRef,
    svgOverlayRef,
  });

  const {
    getCoordinatesRelativeToImage,
    handleMouseLeaveForCoords,
    handleMouseMoveForCoords,
  } = useMouseCoordinates({
    currentImageInfo,
    setMouseImageCoordinates,
    transformWrapperRef, // Pass the ref from useImagePanZoom
    imageContainerRef,
  });

  const {
    drawingRoi,
    handleMouseDownForRoi,
    handleMouseMoveForRoi,
    handleMouseUpForRoi,
  } = useRoiInteraction({
    isSelectingRoi,
    currentImageWidth: currentImageInfo?.width,
    currentImageHeight: currentImageInfo?.height,
    setSelectedRoiCoordinates,
    completeRoiSelection,
    getCoordinatesRelativeToImage,
  });

  // 实现清除检测结果的函数，并将其暴露给全局
  const clearDetectionResults = useCallback(async () => {
    console.log('[ImageDisplay] 清除检测结果，不恢复原图显示');

    // 关键修复：图像切换时只清除检测结果，不恢复原图显示
    // 这确保用户切换图像时立即看到新图像，而不是先看到旧图像的原图

    // 清除检测结果
    if (setDetectionResults) {
      await setDetectionResults(null);
      // 等待检测结果清除完成
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 清除ROI选择
    if (setSelectedRoiCoordinates) {
      setSelectedRoiCoordinates(null);
    }

    return Promise.resolve();
  }, [setDetectionResults, setSelectedRoiCoordinates]);

  // 将clearDetectionResults方法暴露给全局
  useEffect(() => {
    window.clearDetectionResults = clearDetectionResults;

    return () => {
      delete window.clearDetectionResults;
    };
  }, [clearDetectionResults]);

  // 执行智能检测的函数
  const runAutoInference = useCallback(async () => {
    // 如果智能检测已经在运行中，不再执行
    if (isAutoInferenceRunning) return;

    setIsAutoInferenceRunning(true);
    message.loading({ content: '正在执行智能检测...', key: autoInferenceMessageKey, duration: 0 });

    try {
      // 确保检测前清除之前的检测结果
      await clearDetectionResults();

      // 等待UI更新完成
      await new Promise(resolve => setTimeout(resolve, 300));

      // 检查条码检测智能检测是否启用
      if (barcodeAutoInference && window.runBarcodeDetection) {
        await window.runBarcodeDetection();
        // 等待检测结果渲染
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 检查OCR检测智能检测是否启用
      if (ocrAutoInference && window.runOcrInference) {
        await window.runOcrInference();
        // 等待检测结果渲染
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // <--- ADDED: Check AI Restore auto inference --->
      if (aiRestoreAutoInference && window.runAiImageRestore) {
        await window.runAiImageRestore();
        // Wait for restore to complete (composite view might update)
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      message.success({ content: '智能检测完成', key: autoInferenceMessageKey, duration: 1.5 });
    } catch (error) {
      console.error('智能检测过程中出错:', error);
      message.error({ content: '智能检测失败', key: autoInferenceMessageKey, duration: 1.5 });
    } finally {
      setIsAutoInferenceRunning(false);
    }
  }, [barcodeAutoInference, ocrAutoInference, aiRestoreAutoInference, isAutoInferenceRunning, clearDetectionResults]);

  // 重写前一张图片处理函数，处理顺序：1.清除结果 2.切换图片 3.等待加载 4.执行检测
  const handlePrevClick = useCallback(async () => {
    // 如果智能检测正在进行中，则不允许切换图片
    if (isAutoInferenceRunning) {
      message.warning('请等待当前图片检测完成后再切换');
      return;
    }
    // <--- ADDED: Check for batch processing active --->
    if (isBatchProcessingActive) {
        message.warning('请等待当前批量处理完成后再切换');
        return;
    }

    try {
      // 1. 先清除当前的检测结果
      await clearDetectionResults();

      // 2. 切换到前一张图片
      originalHandlePrevClick();

      // 3. 等待图片加载完成
      await new Promise(resolve => setTimeout(resolve, 400));

      // 4. 如果启用了智能检测，执行检测
      if (barcodeAutoInference || ocrAutoInference || aiRestoreAutoInference) { // <--- MODIFIED: Include AI Restore
        await runAutoInference();
      }
    } catch (error) {
      console.error('切换图片过程中出错:', error);
      message.error('切换图片失败');
    }
  }, [originalHandlePrevClick, barcodeAutoInference, ocrAutoInference, aiRestoreAutoInference, runAutoInference, isAutoInferenceRunning, clearDetectionResults, isBatchProcessingActive]);

  // 重写下一张图片处理函数，处理顺序：1.清除结果 2.切换图片 3.等待加载 4.执行检测
  const handleNextClick = useCallback(async () => {
    // 如果智能检测正在进行中，则不允许切换图片
    if (isAutoInferenceRunning) {
      message.warning('请等待当前图片检测完成后再切换');
      return;
    }
    // <--- ADDED: Check for batch processing active --->
    if (isBatchProcessingActive) {
        message.warning('请等待当前批量处理完成后再切换');
        return;
    }

    try {
      // 1. 先清除当前的检测结果
      await clearDetectionResults();

      // 2. 切换到下一张图片
      originalHandleNextClick();

      // 3. 等待图片加载完成
      await new Promise(resolve => setTimeout(resolve, 400));

      // 4. 如果启用了智能检测，执行检测
      if (barcodeAutoInference || ocrAutoInference || aiRestoreAutoInference) { // <--- MODIFIED: Include AI Restore
        await runAutoInference();
      }
    } catch (error) {
      console.error('切换图片过程中出错:', error);
      message.error('切换图片失败');
    }
  }, [originalHandleNextClick, barcodeAutoInference, ocrAutoInference, aiRestoreAutoInference, runAutoInference, isAutoInferenceRunning, clearDetectionResults, isBatchProcessingActive]);

  if (inputSource === 'scanner') {
    return <VideoStreamPlayer />;
  }

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: isDraggingOver ? '#e6f7ff' : (currentImageInfo && currentImageInfo.url ? '#f0f2f5' : 'rgb(128, 128, 128)'),
        border: isDraggingOver ? '2px dashed #1890ff' : '2px dashed #d9d9d9',
        cursor: isSelectingRoi ? 'crosshair' : 'default',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        color: '#595959',
        fontSize: '14px',
        transition: 'background-color 0.2s ease, border-color 0.2s ease',
        overflow: 'hidden',
        position: 'relative'
      }}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {currentImageInfo && currentImageInfo.url ? (
        <TransformWrapper
          ref={transformWrapperRef}
          initialScale={1}
          minScale={0.1}
          maxScale={15}
          centerOnInit={true}
          disabled={isSelectingRoi}
        >
          {() => (
            <TransformComponent
              wrapperStyle={{
                width: '100%',
                height: '100%',
              }}
            >
              <div
                className="image-container"
                ref={imageContainerRef}
                onMouseDown={isSelectingRoi ? handleMouseDownForRoi : undefined}
                onMouseMove={(e) => {
                  // 始终更新鼠标坐标
                  handleMouseMoveForCoords(e);
                  // 如果在ROI选择模式下，同时处理ROI选择
                  if (isSelectingRoi) {
                    handleMouseMoveForRoi(e);
                  }
                }}
                onMouseUp={isSelectingRoi ? handleMouseUpForRoi : undefined}
                onMouseLeave={() => {
                  handleMouseLeaveForCoords();
                }}
                style={{ position: 'relative' }}
              >
                <img
                  ref={imageRef}
                  src={currentImageInfo.url}
                  alt="Current"
                  style={{
                    maxWidth: 'none',
                    maxHeight: 'none',
                  }}
                />
                {currentImageInfo && (
                  <SvgOverlay
                    ref={svgOverlayRef}
                    width={currentImageInfo?.width || 0}
                    height={currentImageInfo?.height || 0}
                    currentImageInfo={currentImageInfo}
                    detections={detectionResults}
                    drawingRoi={drawingRoi}
                    selectedRoi={selectedRoi}
                  />
                )}
              </div>
            </TransformComponent>
          )}
        </TransformWrapper>
      ) : (
        <div style={{ textAlign: 'center', padding: '20px', color: '#FFFFFF', fontFamily: 'SimHei, sans-serif', fontWeight: 'bold' }}>
          <p style={{ marginBottom: '20px', fontSize: '20px' }}> <PictureOutlined style={{ fontSize: '32px', marginRight: '8px', color: '#1890ff' }} /> 请选择加载图像的方式：  </p>

          <div style={{ textAlign: 'left', display: 'inline-block' }}>
            <p style={{ marginBottom: '10px', fontSize: '18px' }}>
              方式1：将图片或文件夹拖拽到此处
            </p>

            <p style={{ marginBottom: '10px', fontSize: '18px' }}>
              方式2：通过菜单 "文件" → "打开图像..." / "打开文件夹..."
            </p>

            <p style={{ marginBottom: '20px', fontSize: '18px' }}>
              方式3：通过菜单 "文件" → "示例图片库..." 获取预设图像
            </p>
          </div>

          <p style={{ fontSize: '16px' }}>
            支持的格式: JPG, PNG, BMP, WEBP, AVIF
          </p>
        </div>
      )}

      {showNavigationButtons && (
        <>
          <div
            onClick={currentImageIndex > 0 && !isAutoInferenceRunning && !isBatchProcessingActive ? handlePrevClick : undefined}
            style={{
              position: 'absolute',
              left: 0,
              top: 0,
              bottom: 0,
              width: '70px',
              zIndex: 10,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'linear-gradient(to right, rgba(24, 144, 255, 0.1), transparent)',
              cursor: currentImageIndex <= 0 || isAutoInferenceRunning || isBatchProcessingActive ? 'default' : 'pointer',
              opacity: currentImageIndex <= 0 || isAutoInferenceRunning || isBatchProcessingActive ? 0.3 : 1,
              transition: 'all 0.3s ease'
            }}
            className="image-nav-column left"
            aria-label="Previous image column"
          >
            <Button
              icon={<LeftOutlined style={{ fontSize: '18px', color: '#1890ff' }} />}
              onClick={(e) => {
                e.stopPropagation();
                if (currentImageIndex > 0 && !isAutoInferenceRunning && !isBatchProcessingActive) {
                  handlePrevClick();
                } else if (isAutoInferenceRunning) {
                  message.warning('请等待当前图片检测完成后再切换');
                } else if (isBatchProcessingActive) {
                    message.warning('请等待当前批量处理完成后再切换');
                }
              }}
              disabled={currentImageIndex <= 0 || isAutoInferenceRunning || isBatchProcessingActive}
              style={{
                height: '60px',
                width: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'rgba(255, 255, 255, 0.8)',
                border: '1px solid #e0e0e0',
                borderRadius: '0 4px 4px 0',
                boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease'
              }}
              aria-label="Previous image"
              className="image-nav-button"
            />
          </div>

          <div
            onClick={currentImageIndex < imageList.length - 1 && !isAutoInferenceRunning && !isBatchProcessingActive ? handleNextClick : undefined}
            style={{
              position: 'absolute',
              right: 0,
              top: 0,
              bottom: 0,
              width: '70px',
              zIndex: 10,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'linear-gradient(to left, rgba(24, 144, 255, 0.1), transparent)',
              cursor: currentImageIndex >= imageList.length - 1 || isAutoInferenceRunning || isBatchProcessingActive ? 'default' : 'pointer',
              opacity: currentImageIndex >= imageList.length - 1 || isAutoInferenceRunning || isBatchProcessingActive ? 0.3 : 1,
              transition: 'all 0.3s ease'
            }}
            className="image-nav-column right"
            aria-label="Next image column"
          >
            <Button
              icon={<RightOutlined style={{ fontSize: '18px', color: '#1890ff' }} />}
              onClick={(e) => {
                e.stopPropagation();
                if (currentImageIndex < imageList.length - 1 && !isAutoInferenceRunning && !isBatchProcessingActive) {
                  handleNextClick();
                } else if (isAutoInferenceRunning) {
                  message.warning('请等待当前图片检测完成后再切换');
                } else if (isBatchProcessingActive) {
                    message.warning('请等待当前批量处理完成后再切换');
                }
              }}
              disabled={currentImageIndex >= imageList.length - 1 || isAutoInferenceRunning || isBatchProcessingActive}
              style={{
                height: '60px',
                width: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'rgba(255, 255, 255, 0.8)',
                border: '1px solid #e0e0e0',
                borderRadius: '4px 0 0 4px',
                boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease'
              }}
              aria-label="Next image"
              className="image-nav-button"
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ImageDisplay;