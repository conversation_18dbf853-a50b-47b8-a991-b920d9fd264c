import React, { useState } from 'react'; // 引入 useState
import { Tree } from 'antd';
import type { TreeProps } from 'antd'; // 移除 DataNode，让 TS 推断或后续修正
// import { FolderOutlined } from '@ant-design/icons';
import { useFunctionPanel } from '../contexts/FunctionPanelContext';

// Import icons
import BaseOperateIcon from '../assets/base_operate.png';
import AiIcon from '../assets/ai.png';
import BarcodeIcon from '../assets/barcode.png';
import OcrIcon from '../assets/ocr.png';
import AiRestoreIcon from '../assets/ai-restored.png';
import FeatureMatchingIcon from '../assets/feature_matching.png';
import FeatureMatchingIcon_traditional from '../assets/feature_matching_traditional.png';
import FeatureMatchingIcon_model from '../assets/feature_matching_model.png';

// Helper to create icon node
const createIcon = (src: string) => <img src={src} alt="" style={{ width: 18, height: 18, marginRight: 5, verticalAlign: 'middle' }} />;

interface TreeNode {
  key: React.Key;
  title: string;
  icon?: React.ReactNode;
  isLeaf?: boolean;
  children?: TreeNode[];
}

const FunctionTree: React.FC = () => {
  const { setSelectedFunction } = useFunctionPanel();

  // Define tree data with icons - let TypeScript infer the type
  const treeData: TreeNode[] = [ // 应用 TreeNode 类型
    {
      title: '图像基础操作',
      key: 'image-basic',
      icon: createIcon(BaseOperateIcon),
      children: [
      ],
    },
    {
      title: 'AI 视觉功能',
      key: 'ai-vision',
      icon: createIcon(AiIcon),
      children: [
        {
          title: '条码目标检测',
          key: 'barcode-detection',
          icon: createIcon(BarcodeIcon),
          isLeaf: true,
        },
        {
          title: 'OCR检测',
          key: 'ocr-detection',
          icon: createIcon(OcrIcon),
          isLeaf: true,
        },
        {
          title: 'AI复原',
          key: 'ai-restore',
          icon: createIcon(AiRestoreIcon),
          isLeaf: true,
        },
        {
          title: '特征点匹配',
          key: 'feature-matching',
          icon: createIcon(FeatureMatchingIcon), // 复用AI图标
          children: [
            {
              title: '传统算法',
              key: 'feature-matching-traditional',
              icon: createIcon(FeatureMatchingIcon_traditional),
              isLeaf: true,
            },
            {
              title: '模型算法',
              key: 'feature-matching-model',
              icon: createIcon(FeatureMatchingIcon_model),
              isLeaf: true,
            },
          ],
        },
      ],
    },
  ];

// Helper function to get all non-leaf keys for default expansion
const getDefaultExpandedKeys = (nodes: TreeNode[]): React.Key[] => { // 使用 TreeNode[] 类型
  const keys: React.Key[] = [];
  const recurse = (currentNodes: TreeNode[] | undefined) => { // 使用 TreeNode[] 类型
    if (!currentNodes) return;
    currentNodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        keys.push(node.key);
        recurse(node.children); // children is already TreeNode[] or undefined
      }
    });
  };
  recurse(nodes);
  return keys;
};
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(getDefaultExpandedKeys(treeData));

  const onExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys);
  };

  const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
    // Log what is selected and if it's considered a leaf
    console.log(
        'FunctionTree onSelect:',
        'Selected Keys:', selectedKeys,
        'Node Data:', info.node, // Log the whole node data for richer info
        'Is Leaf:', info.node.isLeaf
    );

    // Update the context with the selected function object (or null if deselected/non-leaf)
    if (info.node.isLeaf && selectedKeys.length > 0) {
      const selectedNode = info.node as TreeNode; // Cast to our TreeNode type
      console.log('[FunctionTree] Updating context with function:', { key: selectedNode.key, name: selectedNode.title });
      setSelectedFunction({ key: selectedNode.key, name: selectedNode.title });
    } else if (selectedKeys.length === 0) {
      console.log('[FunctionTree] Deselection detected, clearing selected function.');
      setSelectedFunction(null);
    } else if (!info.node.isLeaf) {
      // If a category (non-leaf) node is clicked, we could either clear the selection
      // or keep the previous one. For now, let's clear it, as per typical panel behavior.
      // This also handles the case where a category is clicked after a function was selected.
      console.log('[FunctionTree] Non-leaf node clicked, clearing selected function.');
      setSelectedFunction(null);
      // Original logic for expanding/collapsing categories:
const nodeKey = info.node.key;
      if (expandedKeys.includes(nodeKey)) {
        setExpandedKeys(expandedKeys.filter(key => key !== nodeKey));
      } else {
        setExpandedKeys([...expandedKeys, nodeKey]);
      }
      // Optionally, you might still want to select the category node
      // or clear the function panel if a category is clicked.
      // For now, we only toggle expansion.
      // if (selectedKeys.length > 0) {
      //   setSelectedFunctionKey(selectedKeys[0]); // Or null if you want to clear
      // }
    }
  };

  return (
    <div 
      className="function-tree-container"
      style={{ 
        height: '100%',
        backgroundColor: '#ffffff',
        borderRight: '1px solid #d9d9d9',
        padding: '8px'
      }}
    >
      <Tree
        showIcon={true}
        // defaultExpandAll // Removed, will be controlled by expandedKeys
        expandedKeys={expandedKeys}
        onExpand={onExpand}
        onSelect={onSelect}
        treeData={treeData}
        className="function-tree"
        style={{ fontSize: '15px' }}
      />
    </div>
  );
};

export default FunctionTree;