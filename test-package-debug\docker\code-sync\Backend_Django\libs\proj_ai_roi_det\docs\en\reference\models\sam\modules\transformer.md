---
description: Explore the TwoWayTransformer module in Ultralytics, designed for simultaneous attention to image and query points. Ideal for object detection and segmentation tasks.
keywords: Ultralytics, TwoWayTransformer, module, deep learning, transformer, object detection, image segmentation, attention mechanism, neural networks
---

# Reference for `ultralytics/models/sam/modules/transformer.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/transformer.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/transformer.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/transformer.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.modules.transformer.TwoWayTransformer

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.transformer.TwoWayAttentionBlock

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.transformer.Attention

<br><br>
