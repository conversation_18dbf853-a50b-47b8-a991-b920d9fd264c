#ifndef _POSTPROCESS_H
#define _POSTPROCESS_H

//-----------------------------------------------------------------------------
//  Includes

#include <cfloat>

#include <cmath>
#include <map>
#include <memory>
#include <stack>
#include <vector>
#include <algorithm>

#include "autoconf.h"
#include "Log.hpp"
#include "ModelLoader.hpp"
#include "AIEngineCommon.h"
#include "ModelLoader.hpp"
#include "utils/Geometry.hpp"

//-----------------------------------------------------------------------------
//  Definitions

// 自适应子位深度提取范围功能参考区域尺寸
#ifndef MACR_ADAP_SBER_REF_SIZE
#define MACR_ADAP_SBER_REF_SIZE                 (100)
#endif

// 输出检测结果时，置信度阈值，低于阈值的检测结果将不会输出
#ifndef MACR_DET_FILTER_SCORE_THRESHOLD
#define MACR_DET_FILTER_SCORE_THRESHOLD         (0.1f)
#endif

// 输出检测结果时，交并比阈值，两个检测边框的交并比高于此值时，消除置信度较低的边框
#ifndef MACR_DET_NMS_IOU_THRESHOLD
#define MACR_DET_NMS_IOU_THRESHOLD              (0.5f)
#endif

// OCR_Det模型坐标框缩放系数
#ifndef MACR_OCR_DET_BOX_DILATE_SCALE_FACTOR
#define MACR_OCR_DET_BOX_DILATE_SCALE_FACTOR    (1.082f)
#endif

// OCR_Rec模型字符置信度阈值
#ifndef MACR_OCR_REC_CHARACTER_SCORE_THRESHOLD
#define MACR_OCR_REC_CHARACTER_SCORE_THRESHOLD  (0.2f)
#endif
//-----------------------------------------------------------------------------
//  Declarations

class BasePostprocess {
public:
    /**
     * @brief    
     *           输出目标检测任务结果
     *           
     * @param    prep_data:         指向前处理数据的指针
     * @param    indexes:           输出张量索引列表
     * @param    det_results:       指向存储目标检测结果vector的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    template <typename tensor_t>
    int Output_Detection(const PreprocessedData *prep_data, const std::vector<void *> indexes, std::vector<DetectionBBoxInfo> *det_results)
    {
        // 检查传入参数是否合法
        if ((prep_data == nullptr)
            || (det_results == nullptr))
        {
            LOGE("A null pointer have been encountered.");
            return AIENGINE_INPUT_DATA_ERROR;
        }

        // 定义变量
        int rs = AIENGINE_NO_ERROR; // 执行结果

        // 判断后处理类型
        if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_DETECTION_YOLOV7_CUSTOM)
        {
            float fy = (float)prep_data->resize_h * prep_data->scale_h;
            float fx = (float)prep_data->resize_w * prep_data->scale_w;
            int bias_y = prep_data->start_y;
            int bias_x = prep_data->start_x;

            // 获取输出张量
            output_tensor_t *bbox_tensor = this->Get_Output_Tensor(indexes[0]);
            output_tensor_t *class_tensor = this->Get_Output_Tensor(indexes[1]);
            output_tensor_t *score_tensor = this->Get_Output_Tensor(indexes[2]);
            output_tensor_t *detect_count_tensor = this->Get_Output_Tensor(indexes[3]);
            if ((bbox_tensor == nullptr)
                || (class_tensor) == nullptr
                || (score_tensor) == nullptr
                || (detect_count_tensor) == nullptr)
            {
                LOGE("Failed to get output tensor.");
                return AIENGINE_GOT_NULLPTR;
            }

            // 获取结果数量
            int detect_count = *(tensor_t *)detect_count_tensor->data;
            if (detect_count < 0)
            {
                LOGE("The count of detection results must be greater than 0.");
                return AIENGINE_INVALID_PARAM;
            }
            det_results->reserve(detect_count); // 给检测结果预留足够的存储空间

            // 获取输出结果
            tensor_t *bbox = (tensor_t *)bbox_tensor->data;
            tensor_t *class_id = (tensor_t *)class_tensor->data;
            tensor_t *score = (tensor_t *)score_tensor->data;
            for (int i = 0; i < detect_count; i++)
            {
                // 获取预测框信息
                DetectionBBoxInfo detection;

                // 获取坐标信息
                detection.ymin = bbox[0] * fy + bias_y;
                detection.xmin = bbox[1] * fx + bias_x;
                detection.ymax = bbox[2] * fy + bias_y;
                detection.xmax = bbox[3] * fx + bias_x;
                bbox += 4;

                // 获取类别信息
                detection.classID = *class_id;
                class_id++;

                // 获取置信度
                detection.score = *score;
                score++;

                // 返回结果
                det_results->emplace_back(detection);
            }
        }
        else if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_DETECTION_YOLOV7)
        {
            // 获取输出张量
            output_tensor_t *o_tensor = this->Get_Output_Tensor(indexes[0]);
            if (o_tensor == nullptr)
            {
                LOGE("Failed to get output tensor.");
                return AIENGINE_GOT_NULLPTR;
            }

            // YOLOv7模型后处理
            int num_results = o_tensor->shape[1]; // 获取输出结果个数
            int width = o_tensor->shape[2]; // 获取输出张量宽度

            // 计算缩放系数和坐标偏移地址
            float fy = prep_data->scale_h;
            float fx = prep_data->scale_w;
            int bias_y = prep_data->start_y;
            int bias_x = prep_data->start_x;

            rs = Detection<tensor_t>::YOLOv7((tensor_t *)o_tensor->data, num_results, width, MACR_DET_FILTER_SCORE_THRESHOLD, MACR_DET_NMS_IOU_THRESHOLD, fy, fx, bias_y, bias_x, det_results);
        }
        else if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_DETECTION_YOLOV8)
        {
            // 获取输出张量
            output_tensor_t *o_tensor = this->Get_Output_Tensor(indexes[0]);
            if (o_tensor == nullptr)
            {
                LOGE("Failed to get output tensor.");
                return AIENGINE_GOT_NULLPTR;
            }

            int height = o_tensor->shape[1]; // 输出张量高度
            int num_results = o_tensor->shape[2]; // 结果个数

            // 计算缩放系数和坐标偏移地址
            float fy = prep_data->scale_h;
            float fx = prep_data->scale_w;
            int bias_y = prep_data->start_y;
            int bias_x = prep_data->start_x;

            // 执行后处理
            rs = Detection<tensor_t>::YOLOv8((tensor_t *)o_tensor->data, height, num_results, MACR_DET_FILTER_SCORE_THRESHOLD, MACR_DET_NMS_IOU_THRESHOLD, fy, fx, bias_y, bias_x, det_results);
        }
        else if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_DETECTION_YOLOV8_TRANSPOSE)
        {
            // 获取输出张量
            output_tensor_t *o_tensor = this->Get_Output_Tensor(indexes[0]);
            if (o_tensor == nullptr)
            {
                LOGE("Failed to get output tensor.");
                return AIENGINE_GOT_NULLPTR;
            }

            int num_results = o_tensor->shape[1]; // 字符串长度
            int width = o_tensor->shape[2]; // 字符类别数

            // 计算缩放系数和坐标偏移地址
            float fy = prep_data->scale_h;
            float fx = prep_data->scale_w;
            int bias_y = prep_data->start_y;
            int bias_x = prep_data->start_x;

            // 执行后处理
            rs = Detection<tensor_t>::YOLOv8_Transpose((tensor_t *)o_tensor->data, num_results, width, MACR_DET_FILTER_SCORE_THRESHOLD, MACR_DET_NMS_IOU_THRESHOLD, fy, fx, bias_y, bias_x, det_results);
        }
        else if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_DETECTION_YOLOV10)
        {
            // 获取输出张量
            output_tensor_t *o_tensor = this->Get_Output_Tensor(indexes[0]);
            if (o_tensor == nullptr)
            {
                LOGE("Failed to get output tensor.");
                return AIENGINE_GOT_NULLPTR;
            }

            int num_results = o_tensor->shape[1]; // 字符串长度
            int width = o_tensor->shape[2]; // 字符类别数

            // 计算缩放系数和坐标偏移地址
            float fy = prep_data->scale_h;
            float fx = prep_data->scale_w;
            int bias_y = prep_data->start_y;
            int bias_x = prep_data->start_x;

            // 执行后处理
            rs = Detection<tensor_t>::YOLOv10((tensor_t *)o_tensor->data, num_results, width, MACR_DET_FILTER_SCORE_THRESHOLD, fy, fx, bias_y, bias_x, det_results);
        }
        else
        {
            LOGE("Unsupported postprocess method. Please check whether the `postprocess_method` parameter in the config file is correct.");
            return AIENGINE_INVALID_MODEL_DATA;
        }

        // 返回结果
        return rs;
    }

    /**
     * @brief    
     *           输出语义分割任务结果
     *           
     * @param    prep_data:         指向前处理数据的指针
     * @param    indexes:           输出张量索引列表
     * @param    contours:          轮廓坐标
     * @param    seg_mask:          指向掩码图像存储地址的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-10-16 Created by HuangJP
     */
    template <typename tensor_t>
    int Output_Semantic_Segmentation(const PreprocessedData *prep_data, const std::vector<void *> indexes, struct ImageInfo *seg_mask, std::vector<std::vector<Point>> *contours, std::vector<int> *classes)
    {
        // 检查传入参数是否合法
        if (prep_data == nullptr)
        {
            LOGE("A null pointer have been encountered.");
            return AIENGINE_INPUT_DATA_ERROR;
        }

        // 计算缩放系数和坐标偏移地址
        float fy = prep_data->scale_h;
        float fx = prep_data->scale_w;
        int bias_y = prep_data->start_y;
        int bias_x = prep_data->start_x;
        int seg_height = prep_data->resize_h * fy;
        int seg_width = prep_data->resize_w * fx;

        // 定义变量
        int rs = AIENGINE_NO_ERROR; // 执行结果

        // 检查分割掩码图像是否符合预期
        uint8_t *seg_mask_data = nullptr;
        if (seg_mask != nullptr)
        {
            // 要求掩码图像的高跟宽与检测区域高跟宽保持一致
            if ((seg_mask->img_height != seg_height)
                || (seg_mask->img_width != seg_width))
            {
                LOGE("The height or width of the segmented mask image does not match to the input image.");
                return AIENGINE_INPUT_DATA_ERROR;
            }

            seg_mask_data = (uint8_t *)seg_mask->img_data_pt;
        }

        // 判断后处理类型
        if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_SEMANTIC_SEGMENTATION_YOLOV8)
        {
            // 获取输出张量
            output_tensor_t *o_tensor = this->Get_Output_Tensor(indexes[0]);
            int height = o_tensor->shape[1]; // 输出张量高度
            int num_results = o_tensor->shape[2]; // 结果个数

            // 执行后处理
            output_tensor_t *proto_tensor = this->Get_Output_Tensor(indexes[1]);
            if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
            {
                int num_proto_masks = proto_tensor->shape[1]; // 原型掩码图像数量
                int proto_height = proto_tensor->shape[2]; // 原型掩码高度
                int proto_width = proto_tensor->shape[3]; // 原型掩码宽度

                rs = SemanticSegment<tensor_t, BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW>::YOLOv8((tensor_t *)o_tensor->data, height, num_results, MACR_DET_FILTER_SCORE_THRESHOLD, MACR_DET_NMS_IOU_THRESHOLD, (tensor_t *)proto_tensor->data, num_proto_masks, proto_height, proto_width, 0.5, fy, fx, bias_y, bias_x, prep_data->resize_h, prep_data->resize_w, seg_height, seg_width, seg_mask_data, contours, classes);
            }
            else if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
            {
                int proto_height = proto_tensor->shape[1]; // 原型掩码高度
                int proto_width = proto_tensor->shape[2]; // 原型掩码宽度
                int num_proto_masks = proto_tensor->shape[3]; // 原型掩码图像数量

                rs = SemanticSegment<tensor_t, BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC>::YOLOv8((tensor_t *)o_tensor->data, height, num_results, MACR_DET_FILTER_SCORE_THRESHOLD, MACR_DET_NMS_IOU_THRESHOLD, (tensor_t *)proto_tensor->data, num_proto_masks, proto_height, proto_width, 0.5, fy, fx, bias_y, bias_x, prep_data->resize_h, prep_data->resize_w, seg_height, seg_width, seg_mask_data, contours, classes);
            }
            else
            {
                LOGE("Unsupported memory format.");
                return AIENGINE_INVALID_PARAM;
            }
        }

        return rs;
    }

    /**
     * @brief    
     *           输出字符检测任务结果
     *           
     * @param    data:              指向前处理数据的指针
     * @param    indexes:           输出张量索引列表
     * @param    det_results:       指向存储字符检测结果vector的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-08-08 Created by HuangJP
     */
    template <typename tensor_t>
    int Output_OCR_Detection(const PreprocessedData *prep_data, const std::vector<void *> indexes, std::vector<QuadPoints> *ocr_det_results)
    {
        // 检查传入参数是否合法
        if ((prep_data == nullptr)
            || (ocr_det_results == nullptr))
        {
            LOGE("A null pointer have been encountered.");
            return AIENGINE_INPUT_DATA_ERROR;
        }

        // 获取输出张量
        output_tensor_t *o_tensor = this->Get_Output_Tensor(indexes[0]);
        if (o_tensor == nullptr)
        {
            LOGE("Failed to get output tensor.");
            return AIENGINE_GOT_NULLPTR;
        }

        int o_height = o_tensor->shape[1]; // 输出张量高度
        int o_width = o_tensor->shape[2]; // 输出张量宽度
        if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
        {
            o_height = o_tensor->shape[2]; // 原型掩码高度
            o_width = o_tensor->shape[3]; // 原型掩码宽度
        }

        // 计算缩放系数
        float fy = prep_data->scale_h;
        float fx = prep_data->scale_w;
        int bias_y = prep_data->start_y;
        int bias_x = prep_data->start_x;

        if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_OCR_DETECTION_POCRV3)
        {
            float box_scale_factor = BaseModelLoader::Get_Parameter<float>(this->_model_data, "box_scale_factor", MACR_OCR_DET_BOX_DILATE_SCALE_FACTOR);
            float box_scale_factor_short = BaseModelLoader::Get_Parameter<float>(this->_model_data, "box_scale_factor_short", 1.0f);
            bool use_diagonal_center = BaseModelLoader::Get_Parameter<bool>(this->_model_data, "use_diagonal_center", false);

            OCR_Detection<tensor_t>::PaddleOCRv3((tensor_t *)o_tensor->data, o_height, o_width, 0.01, 0.01, fy, fx, bias_y, bias_x, box_scale_factor, ocr_det_results, use_diagonal_center, box_scale_factor_short);
        }
        else
        {
            LOGE("Unsupported postprocess method. Please check whether the `postprocess_method` parameter in the config file is correct.");
            return AIENGINE_INVALID_MODEL_DATA;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           输出字符识别任务结果
     *           
     * @param    prep_data:         指向前处理数据的指针
     * @param    indexes:           输出张量索引列表
     * @param    det_results:       指向存储字符识别结果vector的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-08-14 Created by HuangJP
     */
    template <typename tensor_t>
    int Output_OCR_Recognize(const PreprocessedData *prep_data, const std::vector<void *> indexes, std::vector<OCRCharacter> *ocr_rec_results)
    {
        // 检查传入参数是否合法
        if ((prep_data == nullptr)
            || (ocr_rec_results == nullptr))
        {
            LOGE("A null pointer have been encountered.");
            return AIENGINE_INPUT_DATA_ERROR;
        }

        // 获取字典
        auto dict = this->_model_data->ocr_rec_dict; // 指向字典内容的指针
        if (dict == nullptr)
        {
            // 字典不能为空
            LOGE("The parameter `ocr_rec_dict` was a null pointer, which is not allowed in the `Output_OCR_Recognize` function. Please check whether the config file specifies the `ocr_rec_dict` parameter.");
            return AIENGINE_INVALID_MODEL_DATA;
        }

        // 获取输出张量
        output_tensor_t *o_tensor = this->Get_Output_Tensor(indexes[0]);
        if (o_tensor == nullptr)
        {
            LOGE("Failed to get output tensor.");
            return AIENGINE_GOT_NULLPTR;
        }

        int length = o_tensor->shape[1]; // 字符串长度
        int char_classes = o_tensor->shape[2]; // 字符类别数

        // 执行后处理
        if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_OCR_RECOGNIZE_POCRV4_CTC_LABEL_DECODE)
        {
            // PaddleOCRv4字符识别CTCLabelDecode后处理
            OCR_Recognize<tensor_t>::PaddleOCRv4_CTC_Label_Decode((tensor_t *)o_tensor->data, length, char_classes, MACR_OCR_REC_CHARACTER_SCORE_THRESHOLD, dict, ocr_rec_results);
        }
        else if (this->_model_data->postprocess_method == POSTPROCESS_METHOD_OCR_RECOGNIZE_POCRV4_CUSTOM)
        {
            // PaddleOCRv4模型字符识别后处理（自定义CTC标签解码）
            OCR_Recognize<tensor_t>::PaddleOCRv4_CTC_Label_Decode_Custom((tensor_t *)o_tensor->data, length, char_classes, MACR_OCR_REC_CHARACTER_SCORE_THRESHOLD, dict, ocr_rec_results);
        }
        else
        {
            LOGE("Unsupported postprocess method. Please check whether the `postprocess_method` parameter in the config file is correct.");
            return AIENGINE_INVALID_MODEL_DATA;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           计算两个矩形边框的交并比
     *           
     * @param    bbox1,bbox2:       需要计算交并比的两个矩形边框
     *           
     * @retval   百分比表示的交并比，数值为0.0~1.0
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static float IoU(struct DetectionBBoxInfo bbox1, struct DetectionBBoxInfo bbox2)
    {
        // 步骤一：获取交集部分坐标
        float ix_min = std::max(bbox1.xmin, bbox2.xmin);
        float iy_min = std::max(bbox1.ymin, bbox2.ymin);
        float ix_max = std::min(bbox1.xmax, bbox2.xmax);
        float iy_max = std::min(bbox1.ymax, bbox2.ymax);

        // 步骤二：计算交集部分宽和高
        float iw = std::max(ix_max - ix_min, 0.0f);
        float ih = std::max(iy_max - iy_min, 0.0f);
        if ((iw <= 0.0f) || (ih <= 0.0f))
        {
            return 0.0;
        }

        // 步骤三：计算交集部分面积
        float inters = iw * ih;

        // 步骤四：计算两个框的面积
        float s1 = (bbox1.xmax - bbox1.xmin) * (bbox1.ymax - bbox1.ymin);
        float s2 = (bbox2.xmax - bbox2.xmin) * (bbox2.ymax - bbox2.ymin);

        // 步骤五：计算并集面积
        float uni = s1 + s2 - inters;

        // 步骤六：计算和返回交并比
        return inters / uni;
    }

protected:
    typedef struct {
        void *data; // 指向输出张量的指针
        std::vector<int> shape; // 输出张量形状
    }output_tensor_t;

    const BaseModelLoader::ModelData *_model_data;
    // 输出张量表，键：张量索引，值：输出张量

    /**
     * @brief    
     *           获取输出张量
     *           
     * @param    index:     输出张量索引
     *           
     * @retval   指向输出张量组的指针，获取失败时返回空指针
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    virtual output_tensor_t *Get_Output_Tensor(void *index) = 0;

    /**
     * @brief    
     *           两个预测框的得分比较函数
     *           
     * @param    a, b:      需要比较的预测框
     *           
     * @retval   a的得分是否比b高
     *           
     * @date     2024-08-08 Created by HuangJP
     */
    static inline bool _bbox_score_cmp(struct DetectionBBoxInfo a, struct DetectionBBoxInfo b)
    {
        // 返回置信度高的结果
        return a.score > b.score;
    }

    /**
     * @brief    
     *           非极大值抑制算法，Non-Maximum Supression
     *           
     * @param    iou_threshold:     交并比阈值，当两个边框的交并比大于阈值时，消除置信度较低的边框
     * @param    bboxes:            暂时保留的检测结果，NMS执行过程中会逐个移除该形参中的元素，直至元素全部移除完毕
     * @param    detections:        NMS算法执行完毕后，最终保留下来的检测结果
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    template<typename bbox_t>
    static void NMS(float iou_threshold, std::vector<bbox_t> *bboxes, std::vector<bbox_t> *detections)
    {
        // 按照得分从高到低排序结果
        std::sort(bboxes->begin(), bboxes->end(), _bbox_score_cmp);

        // 循环直至所有重复框都被消除
        while (!bboxes->empty())
        {
            auto base = bboxes->begin(); // 选取剩余预测框中置信度最高的边框作为基准框
            std::vector<int> delete_list({0}); // 定义消除列表，同时把基准框作为消除对象
            detections->emplace_back(*base); // 将基准框添加到最终保留的检测结果中

            // 遍历剩余的预测框
            int remain = bboxes->size();
            for (int i = 1; i < remain; i++)
            {
                // 计算当前边框与基准框的交并比，当交并比大于阈值时，将当前边框添加到消除列表中（因为当前边框的置信度比基准框低）
                if (IoU(*base, (*bboxes)[i]) > iou_threshold)
                {
                    delete_list.emplace_back(i);
                }
            }

            // 从后往前移除重复的预测框
            for (auto idx = delete_list.rbegin(); idx != delete_list.rend(); idx++)
            {
                bboxes->erase(bboxes->begin()+(*idx));
            }
        }
    }

    /**
     * @brief    
     *           目标检测任务类
     *           
     * @param    tensor_t:      输出张量数据类型
     *           
     * @date     2024-08-07 Created by HuangJP
     */
    template <typename tensor_t>
    class Detection {
    public:
        /**
         * @brief    
         *           YOLOv7模型后处理
         *           
         * @param    output_tensor:         指向输出张量的指针
         * @param    num_results:           输出结果个数
         * @param    width:                 输出张量宽度
         * @param    score_threshold:       置信度阈值
         * @param    iou_threshold:         NMS交并比阈值
         * @param    fy,fx:                 检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:         检测区域左上角点相对原点的偏移地址
         * @param    det_results:           存储输出结果的vector指针
         *           
         * @retval   错误码
         *           
         * @date     2024-07-31 Created by HuangJP
         */
        static int YOLOv7(tensor_t *output_tensor, int num_results, int width, float score_threshold, float iou_threshold, float fy, float fx, int bias_y, int bias_x, std::vector<DetectionBBoxInfo> *det_results)
        {
            // 判断传入参数是否合法
            if ((output_tensor == nullptr)
                || (det_results == nullptr))
            {
                LOGE("A null pointer has been encountered.");
                return AIENGINE_INVALID_PARAM;
            }

            #undef CLASS_PROB_IDX
            #define CLASS_PROB_IDX 5 // 类别概率在输出张量中的起始索引
            int num_classes = width - CLASS_PROB_IDX; // 计算类别数（结果中，前四个数是xywh坐标，第五个数是置信度，剩下的数是每个类别的概率）

            // 判断类别数是否正确
            if (num_classes <= 0)
            {
                LOGE("The number of classes must be greater than 0. Please check whether the width parameter was set correctly.");
                LOGD("Number of classes: %d, width: %d.", num_classes, width);
                return AIENGINE_INVALID_PARAM;
            }

            std::vector<struct DetectionBBoxInfo> keeps[num_classes]; // 每个类别暂时保留的检测结果

            // 获取检测结果，同时过滤置信度低的结果
            for (int i = 0; i < num_results; i++)
            {
                tensor_t *vector = &output_tensor[i * width]; // 获取指向检测结果的指针

                float confidence = (float)vector[CLASS_PROB_IDX-1]; // 获取置信度
                if (confidence < score_threshold) // 过滤置信度低的结果
                {
                    continue;
                }

                // 获取最大类别概率和类别ID
                float class_score = 0.0f; // 类别概率
                int class_id = 0; // 类别ID
                for (int j = CLASS_PROB_IDX; j < width; j++)
                {
                    float score = (float)vector[j]; // 获取类别概率
                    if (class_score < score)
                    {
                        class_id = j - CLASS_PROB_IDX; // 保存类别ID
                        class_score = score; // 更新类别概率
                    }
                }

                // 判断得分是否高于阈值
                float max_score = class_score * confidence;  // 计算类别概率和置信度的乘积，作为检测结果得分
                if (max_score > score_threshold)
                {
                    keeps[class_id].emplace_back(
                        (struct DetectionBBoxInfo){
                            .xmin = (vector[0] - (vector[2] / 2)) * fx + bias_x, // 左上角点x坐标 = (中心点x轴坐标 - 宽度的一半) * 缩放系数 + x轴偏移坐标
                            .ymin = (vector[1] - (vector[3] / 2)) * fy + bias_y, // 左上角点y坐标 = (中心点y轴坐标 - 高度的一半) * 缩放系数 + y轴偏移坐标
                            .xmax = (vector[0] + (vector[2] / 2)) * fx + bias_x, // 左上角点x坐标 = (中心点x轴坐标 + 宽度的一半) * 缩放系数 + x轴偏移坐标
                            .ymax = (vector[1] + (vector[3] / 2)) * fy + bias_y, // 左上角点y坐标 = (中心点y轴坐标 + 高度的一半) * 缩放系数 + y轴偏移坐标
                            .score = max_score,
                            .classID = class_id,
                        }
                    );
                }
            }

            // 消除类别相同，交并比高于阈值的边框
            for (int i = 0; i < num_classes; i++)
            {
                NMS<struct DetectionBBoxInfo>(iou_threshold, &keeps[i], det_results); // 注意：此处会修改keeps的内容
            }

            return AIENGINE_NO_ERROR;
        }

        /**
         * @brief    
         *           YOLOv8模型后处理
         *           
         * @param    output_tensor:         指向输出张量的指针
         * @param    height:                输出张量高度
         * @param    num_results:           输出结果个数
         * @param    score_threshold:       置信度阈值
         * @param    iou_threshold:         NMS交并比阈值
         * @param    fy,fx:                 检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:         检测区域左上角点相对原点的偏移地址
         * @param    det_results:           存储输出结果的vector指针
         *           
         * @retval   错误码
         *           
         * @date     2024-08-30 Created by HuangJP
         */
        static int YOLOv8(tensor_t *output_tensor, int height, int num_results, float score_threshold, float iou_threshold, float fy, float fx, int bias_y, int bias_x, std::vector<DetectionBBoxInfo> *det_results)
        {
            // 判断传入参数是否合法
            if ((output_tensor == nullptr)
                || (det_results == nullptr))
            {
                LOGE("A null pointer has been encountered.");
                return AIENGINE_INVALID_PARAM;
            }

            #undef CLASS_PROB_IDX
            #define CLASS_PROB_IDX 4 // 类别概率在输出张量中的起始索引

            int num_classes = height - CLASS_PROB_IDX; // 类别数

            // 判断类别数是否正确
            if (num_classes <= 0)
            {
                LOGE("The number of classes must be greater than 0. Please check whether the width parameter was set correctly.");
                LOGD("Number of classes: %d, height: %d.", num_classes, height);
                return AIENGINE_INVALID_PARAM;
            }

            std::vector<struct DetectionBBoxInfo> keeps[num_classes]; // 每个类别暂时保留的检测结果

            // 获取坐标
            tensor_t *coord_pt = output_tensor; // 行指针
            std::vector<CoordinateYOLO> coordinates(num_results);
            for (int i = 0; i < num_results; i++) coordinates[i].center_x = *coord_pt, coord_pt++;
            for (int i = 0; i < num_results; i++) coordinates[i].center_y = *coord_pt, coord_pt++;
            for (int i = 0; i < num_results; i++) coordinates[i].width = *coord_pt, coord_pt++;
            for (int i = 0; i < num_results; i++) coordinates[i].height = *coord_pt, coord_pt++;

            // 获取置信度和分类
            std::vector<float> scores(num_results, 0.0f); // 置信度
            std::vector<int> class_ids(num_results); // 分类
            for (int i = CLASS_PROB_IDX; i < height; i++)
            {
                tensor_t *vector = &output_tensor[i * num_results]; // 行指针
                for (int j = 0; j < num_results; j++)
                {
                    // 判断当前类别置信度是否更高
                    if (scores[j] < vector[j])
                    {
                        scores[j] = vector[j];
                        class_ids[j] = i - CLASS_PROB_IDX;
                    }
                }
            }

            // 获取置信度高于阈值的结果
            for (int j = 0; j < num_results; j++)
            {
                float score = scores[j];
                int class_id = class_ids[j];
                CoordinateYOLO coord = coordinates[j];

                if (score > score_threshold)
                {
                    keeps[class_id].emplace_back(
                        (struct DetectionBBoxInfo){
                            .xmin = (coord.center_x - (coord.width / 2))  * fx + bias_x,    // 左上角点x坐标 = (中心点x轴坐标 - 宽度的一半) * 缩放系数 + x轴偏移
                            .ymin = (coord.center_y - (coord.height / 2)) * fy + bias_y,    // 左上角点y坐标 = (中心点y轴坐标 - 高度的一半) * 缩放系数 + y轴偏移
                            .xmax = (coord.center_x + (coord.width / 2))  * fx + bias_x,    // 左上角点x坐标 = (中心点x轴坐标 + 宽度的一半) * 缩放系数 + x轴偏移
                            .ymax = (coord.center_y + (coord.height / 2)) * fy + bias_y,    // 左上角点y坐标 = (中心点y轴坐标 + 高度的一半) * 缩放系数 + y轴偏移
                            .score = score,
                            .classID = class_id,
                        }
                    );
                }
            }

            // 消除类别相同，交并比高于阈值的边框
            for (int i = 0; i < num_classes; i++)
            {
                NMS<struct DetectionBBoxInfo>(iou_threshold, &keeps[i], det_results); // 注意：此处会修改keeps的内容
            }

            return AIENGINE_NO_ERROR;
        }

        /**
         * @brief    
         *           YOLOv8模型后处理（Transpose）
         *           
         * @param    output_tensor:         指向输出张量的指针
         * @param    num_results:           输出结果个数
         * @param    width:                 输出张量宽度
         * @param    score_threshold:       置信度阈值
         * @param    iou_threshold:         NMS交并比阈值
         * @param    fy,fx:                 检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:         检测区域左上角点相对原点的偏移地址
         * @param    det_results:           存储输出结果的vector指针
         *           
         * @retval   错误码
         *           
         * @date     2024-09-19 Created by HuangJP
         */
        static int YOLOv8_Transpose(tensor_t *output_tensor, int num_results, int width, float score_threshold, float iou_threshold, float fy, float fx, int bias_y, int bias_x, std::vector<DetectionBBoxInfo> *det_results)
        {
            // 判断传入参数是否合法
            if ((output_tensor == nullptr)
                || (det_results == nullptr))
            {
                LOGE("A null pointer has been encountered.");
                return AIENGINE_INVALID_PARAM;
            }

            #undef CLASS_PROB_IDX
            #define CLASS_PROB_IDX 4 // 类别概率在输出张量中的起始索引

            int num_classes = width - CLASS_PROB_IDX; // 类别数

            // 判断类别数是否正确
            if (num_classes <= 0)
            {
                LOGE("The number of classes must be greater than 0. Please check whether the width parameter was set correctly.");
                LOGD("Number of classes: %d, width: %d.", num_classes, width);
                return AIENGINE_INVALID_PARAM;
            }

            std::vector<struct DetectionBBoxInfo> keeps[num_classes]; // 保存每个类别的检测结果

            // 获取检测结果，同时过滤置信度低的结果
            for (int i = 0; i < num_results; i++)
            {
                tensor_t *vector = &output_tensor[i * width]; // 获取指向检测结果的指针

                // 获取最大类别概率和类别ID
                float class_score = 0.0f; // 类别概率
                int class_id = 0; // 类别ID
                for (int j = CLASS_PROB_IDX; j < width; j++)
                {
                    float score = (float)vector[j]; // 获取类别概率
                    if (class_score < score)
                    {
                        class_id = j - CLASS_PROB_IDX; // 保存类别ID
                        class_score = score; // 更新类别概率
                    }
                }

                // 判断置信度是否高于阈值
                if (class_score > score_threshold)
                {
                    keeps[class_id].emplace_back(
                        (struct DetectionBBoxInfo){
                            .xmin = (vector[0] - (vector[2] / 2)) * fx + bias_x, // 左上角点x坐标 = (中心点x轴坐标 - 宽度的一半) * 缩放系数 + x轴偏移坐标
                            .ymin = (vector[1] - (vector[3] / 2)) * fy + bias_y, // 左上角点y坐标 = (中心点y轴坐标 - 高度的一半) * 缩放系数 + y轴偏移坐标
                            .xmax = (vector[0] + (vector[2] / 2)) * fx + bias_x, // 左上角点x坐标 = (中心点x轴坐标 + 宽度的一半) * 缩放系数 + x轴偏移坐标
                            .ymax = (vector[1] + (vector[3] / 2)) * fy + bias_y, // 左上角点y坐标 = (中心点y轴坐标 + 高度的一半) * 缩放系数 + y轴偏移坐标
                            .score = class_score,
                            .classID = class_id,
                        }
                    );
                }
            }

            // 消除类别相同，交并比高于阈值的边框
            for (int i = 0; i < num_classes; i++)
            {
                NMS<struct DetectionBBoxInfo>(iou_threshold, &keeps[i], det_results); // 注意：此处会修改keeps的内容
            }

            return AIENGINE_NO_ERROR;
        }

        /**
         * @brief    
         *           YOLOv10模型后处理
         *           
         * @param    output_tensor:         指向输出张量的指针
         * @param    num_results:           输出结果个数
         * @param    width:                 输出张量宽度
         * @param    score_threshold:       置信度阈值
         * @param    fy,fx:                 检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:         检测区域左上角点相对原点的偏移地址
         * @param    det_results:           存储输出结果的vector指针
         *           
         * @retval   错误码
         *           
         * @date     2024-09-02 Created by HuangJP
         */
        static int YOLOv10(tensor_t *output_tensor, int num_results, int width, float score_threshold, float fy, float fx, int bias_y, int bias_x, std::vector<DetectionBBoxInfo> *det_results)
        {
            // 判断传入参数是否合法
            if ((output_tensor == nullptr)
                || (det_results == nullptr))
            {
                LOGE("A null pointer has been encountered.");
                return AIENGINE_INVALID_PARAM;
            }

            // 判断输出张量形状是否正确
            if (width != 6)
            {
                LOGE("The width of the output tensor in YOLOv10 must be 6. Please verify your model configuration to ensure they comply with this requirement.");
                return AIENGINE_INVALID_PARAM;
            }

            // 解析输出结果
            for (int i = 0; i < num_results; i++)
            {
                tensor_t *vector = output_tensor + i * 6;

                // 保存置信度大于阈值的结果
                float score = vector[4];
                if (score > score_threshold)
                {
                    det_results->push_back((DetectionBBoxInfo){
                        .xmin = vector[0] * fx + bias_x,
                        .ymin = vector[1] * fy + bias_y,
                        .xmax = vector[2] * fx + bias_x,
                        .ymax = vector[3] * fy + bias_y,
                        .score = score,
                        .classID = (int)ceil(vector[5]),
                    });
                }
            }

            return AIENGINE_NO_ERROR;
        }
    };

    /**
     * @brief    
     *           语义分割类
     *           
     * @param    tensor_t:      输出张量数据类型
     *           
     * @date     2024-10-16 Created by HuangJP
     */
    template <typename tensor_t, BaseModelLoader::memory_format_t memory_format>
    class SemanticSegment {
    public:
        /**
         * @brief    
         *           YOLOv8模型后处理（语义分割任务）
         *           
         * @param    output_tensor:         指向输出张量的指针
         * @param    height:                输出张量高度
         * @param    num_results:           输出结果个数
         * @param    score_threshold:       置信度阈值
         * @param    iou_threshold:         NMS交并比阈值
         * @param    proto_masks:           指向原型掩码的指针
         * @param    num_proto_masks:       原型掩码数量
         * @param    proto_height:          原型掩码高度
         * @param    proto_width:           原型掩码宽度
         * @param    mask_threshold:        掩码图像阈值
         * @param    fy,fx:                 检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:         检测区域左上角点相对原点的偏移地址
         * @param    resize_h,resize_w:     输入张量高度和宽度
         * @param    seg_height:            分割图像高度
         * @param    seg_width:             分割图像宽度
         * @param    seg_mask:              指向存储分割掩码的指针
         * @param    contours:              轮廓坐标
         * @param    classes:               类别数组
         *           
         * @retval   错误码
         *           
         * @date     2024-08-30 Created by HuangJP
         */
        static int YOLOv8(tensor_t *output_tensor, int height, int num_results, float score_threshold, float iou_threshold, tensor_t *proto_masks, int num_proto_masks, int proto_height, int proto_width, float mask_threshold, float fy, float fx, int bias_y, int bias_x, int resize_h, int resize_w, int seg_height, int seg_width, uint8_t *seg_mask = nullptr, std::vector<std::vector<Point>> *contours = nullptr, std::vector<int> *classes = nullptr)
        {
            // 判断传入参数是否合法
            if ((output_tensor == nullptr)
                || (proto_masks == nullptr))
            {
                LOGE("A null pointer has been encountered.");
                return AIENGINE_INVALID_PARAM;
            }

            #undef CLASS_PROB_IDX
            #define CLASS_PROB_IDX 4 // 类别概率在输出张量中的起始索引

            int num_classes = height - CLASS_PROB_IDX - num_proto_masks; // 类别数

            // 判断类别数是否正确
            if (num_classes <= 0)
            {
                LOGE("The number of classes must be greater than 0. Please check whether the width parameter was set correctly.");
                LOGD("Number of classes: %d, height: %d.", num_classes, height);
                return AIENGINE_INVALID_PARAM;
            }

            std::vector<BBoxSeg> keeps[num_classes]; // 每个类别暂时保留的检测结果

            // 获取坐标
            tensor_t *coord_pt = output_tensor; // 行指针
            std::vector<CoordinateYOLO> coordinates(num_results);
            for (int i = 0; i < num_results; i++) coordinates[i].center_x = *coord_pt, coord_pt++;
            for (int i = 0; i < num_results; i++) coordinates[i].center_y = *coord_pt, coord_pt++;
            for (int i = 0; i < num_results; i++) coordinates[i].width = *coord_pt, coord_pt++;
            for (int i = 0; i < num_results; i++) coordinates[i].height = *coord_pt, coord_pt++;

            // 获取置信度和分类
            std::vector<float> scores(num_results, 0.0f); // 置信度
            std::vector<int> class_ids(num_results); // 分类
            for (int i = CLASS_PROB_IDX; i < height - num_proto_masks; i++)
            {
                tensor_t *vector = &output_tensor[i * num_results]; // 行指针
                for (int j = 0; j < num_results; j++)
                {
                    // 判断当前类别置信度是否更高
                    if (scores[j] < vector[j])
                    {
                        scores[j] = vector[j];
                        class_ids[j] = i - CLASS_PROB_IDX;
                    }
                }
            }

            // 获取置信度高于阈值的结果
            for (int j = 0; j < num_results; j++)
            {
                float score = scores[j];
                int class_id = class_ids[j];
                CoordinateYOLO coord = coordinates[j];

                if (score > score_threshold)
                {
                    // 添加预测结果
                    BBoxSeg bbox{};
                    bbox.xmin = (coord.center_x - (coord.width / 2));
                    bbox.ymin = (coord.center_y - (coord.height / 2));
                    bbox.xmax = (coord.center_x + (coord.width / 2));
                    bbox.ymax = (coord.center_y + (coord.height / 2));
                    bbox.score = score;
                    bbox.classID = class_id;
                    bbox.idx = j;
                    keeps[class_id].emplace_back(bbox);
                }
            }

            // 消除类别相同，交并比高于阈值的边框
            std::vector<BBoxSeg> det_results;
            for (int i = 0; i < num_classes; i++)
            {
                NMS<BBoxSeg>(iou_threshold, &keeps[i], &det_results); // 注意：此处会修改keeps的内容
            }

            // 获取掩码图像系数
            int proto_idx = 0;
            for (int i = height - num_proto_masks; i < height; i++)
            {
                tensor_t *vector = &output_tensor[i * num_results]; // 行指针
                for (auto &bbox: det_results)
                {
                    // 添加系数
                    int j = bbox.idx;
                    bbox.coefficient.emplace_back(vector[j]);
                }
                proto_idx++;
            }

            // 计算掩码图像的缩放系数
            float proto_fy = (float)resize_h / (float)proto_height; // 原型掩码图像在高度方向上的缩放系数
            float proto_fx = (float)resize_w / (float)proto_width; // 原型掩码图像在宽度方向上的缩放系数

            // 计算分割图像的宽高以及缩放系数
            float mask_fy = fy * proto_fy;
            float mask_fx = fx * proto_fx;

            // 计算像素阈值
            float pixel_threshold = Inverse_Sigmoid(mask_threshold);
            if (std::isnan(pixel_threshold))
            {
                return AIENGINE_INVALID_PARAM;
            }

            // 生成掩码图像
            int proto_size = proto_height * proto_width; // 原型掩码尺寸
            std::unique_ptr<float[]> mask(new float[proto_size]); // 存储掩码图像的指针
            std::unique_ptr<uint8_t[]> final_mask(new uint8_t[proto_size]{}); // 最终输出的掩码图像
            auto generate_mask_nhwc = [&mask, proto_size, proto_masks, num_proto_masks](BBoxSeg bbox)
            {
                // 清空掩码图像
                memset(mask.get(), 0x00, proto_size * sizeof(float));

                // 生成分割掩码
                tensor_t *proto_mask = proto_masks;
                for (int i = 0; i < proto_size; i++)
                {
                    for (int j = 0; j < num_proto_masks; j++)
                    {
                        mask[i] += *proto_mask * bbox.coefficient[j];
                        proto_mask++;
                    }
                }
            };
            auto generate_mask_nchw = [&mask, proto_size, proto_masks, num_proto_masks](BBoxSeg bbox)
            {
                // 清空掩码图像
                memset(mask.get(), 0x00, proto_size * sizeof(float));

                // 生成分割掩码
                tensor_t *proto_mask = proto_masks;
                for (int i = 0; i < num_proto_masks; i++)
                {
                    float coe = bbox.coefficient[i];
                    for (int j = 0; j < proto_size; j++)
                    {
                        mask[i] += *proto_mask * coe;
                        proto_mask++;
                    }
                }
            };

            // 添加类别信息
            if (classes != nullptr)
            {
                // 倒序遍历所有目标检测结果（需要将置信度高的掩码覆盖在置信度低的掩码上）
                for (auto it = det_results.rbegin(); it != det_results.rend(); it++)
                {
                    BBoxSeg bbox = *it;
                    int cls = bbox.classID + 1; // 类别ID（0为背景，目标为原始分类+1）
                    classes->emplace_back(cls);
                }
            }

            // 倒序遍历所有目标检测结果（需要将置信度高的掩码覆盖在置信度低的掩码上）
            for (auto it = det_results.rbegin(); it != det_results.rend(); it++)
            {
                // 生成分割掩码
                BBoxSeg bbox = *it;

                if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
                {
                    generate_mask_nhwc(bbox);
                }
                else if (memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
                {
                    generate_mask_nchw(bbox);
                }
                else
                {
                    LOGE("Unsupported memory format.");
                    return AIENGINE_INVALID_PARAM;
                }

                // 转换外轮廓
                if (contours != nullptr)
                {
                    GeomPolygon contour;

                    // 计算分割图像区域
                    int start_x = std::max(0, (int)(bbox.xmin / proto_fx));
                    int start_y = std::max(0, (int)(bbox.ymin / proto_fy));
                    int end_x = std::min(seg_width, (int)(bbox.xmax / proto_fx + 1));
                    int end_y = std::min(seg_height, (int)(bbox.ymax / proto_fy + 1));

                    // 从左到右搜索左边界
                    GeomPoint left_top{(int)end_x, (int)end_y}; // 左边界顶点
                    GeomPoint left_bottom{(int)end_x, (int)start_y}; // 左边界底点
                    for (int i = start_y; i < end_y; i++)
                    {
                        float *src = mask.get() + i * proto_width;
                        for (int j = start_x; j < end_x; j++)
                        {
                            if (src[j] > pixel_threshold)
                            {
                                // 添加边界点
                                GeomPoint point{(int)j, (int)i};
                                contour.emplace_back(point);
                                break;
                            }
                        }
                    }

                    // 从右到左搜索右边界
                    for (int i = left_top.y; i < left_bottom.y+1; i++)
                    {
                        float *src = mask.get() + i * proto_width;
                        for (int j = end_x-1; j >= start_x; j--)
                        {
                            if (src[j] > pixel_threshold)
                            {
                                // 添加边界点
                                GeomPoint point{(int)j, (int)i};
                                contour.emplace_back(point);
                                break;
                            }
                        }
                    }

                    // 从上到下搜索上边界
                    std::vector<bool> top_found(proto_width, false);
                    for (int i = start_y; i < end_y; i++)
                    {
                        float *src = mask.get() + i * proto_width;
                        for (int j = start_x; j < end_x; j++)
                        {
                            // 已找到上边界点的情况下跳过
                            if (top_found[j] == true)
                            {
                                continue;
                            }

                            if (src[j] > pixel_threshold)
                            {
                                // 添加边界点
                                GeomPoint point{(int)j, (int)i};
                                contour.emplace_back(point);
                                top_found[j] = true;
                            }
                        }
                    }

                    // 从下到上搜索下边界
                    int new_start_x = start_x;
                    int new_end_x = end_x;
                    for (int i = 0; i < proto_width; i++) {if (top_found[i] == true) {new_start_x = i; break;}};
                    for (int i = proto_width - 1; i >= 0; i--) {if (top_found[i] == true) {new_end_x = i; break;}};
                    std::vector<bool> bottom_found(proto_width, false);
                    for (int i = end_y-1; i >= start_y; i--)
                    {
                        float *src = mask.get() + i * proto_width;
                        for (int j = new_start_x; j < new_end_x; j++)
                        {
                            // 已找到下边界点的情况下跳过
                            if (bottom_found[j] == true)
                            {
                                continue;
                            }

                            if (src[j] > pixel_threshold)
                            {
                                // 添加边界点
                                GeomPoint point{(int)j, (int)i};
                                contour.emplace_back(point);
                                bottom_found[j] = true;
                            }
                        }
                    }

                    // 转换坐标点
                    for (auto &point: contour) {point.x = point.x * mask_fx + bias_x; point.y = point.y * mask_fy + bias_y;};
                    
                    // 形成凸包（逆时针排序）
                    contour = Geom::Build_Convex_Hull(contour);

                    // 转换输出数据
                    std::vector<Point> output;
                    for (auto it = contour.rbegin(); it != contour.rend(); it++)
                    {
                        auto point = *it;
                        output.emplace_back(Point{(int)point.x,(int)(point.y)});
                    };

                    // 添加结果
                    contours->emplace_back(output);
                }

                // 转换分割掩码
                if (seg_mask != nullptr)
                {
                    int cls = bbox.classID + 1; // 类别ID（0为背景，目标为原始分类+1）

                    // 计算分割图像区域
                    int start_x = std::max(0, (int)(bbox.xmin * fx));
                    int start_y = std::max(0, (int)(bbox.ymin * fy));
                    int end_x = std::min(seg_width, (int)(bbox.xmax * fx + 1));
                    int end_y = std::min(seg_height, (int)(bbox.ymax * fy + 1));

                    // 计算宽度方向索引
                    int width = end_x - start_x;
                    std::vector<float> w_idxes(width);
                    for (int i = 0; i < width; i++)
                    {
                        w_idxes[i] = (start_x + i) / mask_fx;
                    }

                    // 转换分割图像
                    for (int i = start_y; i < end_y; i++)
                    {
                        uint8_t *dst = seg_mask + i * seg_width;
                        float src_y = i / mask_fy; // 行索引
                        for (int j = start_x; j < end_x; j++)
                        {
                            float src_x = w_idxes[j - start_x]; // 列索引

                            // 双线性插值
                            int x0 = (int)src_x;
                            int y0 = (int)src_y;
                            int x1 = x0 + 1;
                            int y1 = y0 + 1;

                            // 计算权重
                            float dx = src_x - (float)x0;
                            float dy = src_y - (float)y0;

                            float w00 = (1.0f - dx) * (1.0f - dy);
                            float w01 = dx * (1.0f - dy);
                            float w10 = (1.0f - dx) * dy;
                            float w11 = dx * dy;

                            // 防止越界
                            x0 = std::min(std::max(x0, 0), proto_width - 2);
                            y0 = std::min(std::max(y0, 0), proto_height - 2);
                            x1 = std::min(std::max(x1, 0), proto_width - 1);
                            y1 = std::min(std::max(y1, 0), proto_height - 1);

                            // 计算像素值
                            float *src00 = mask.get() + x0 + y0 * proto_width; 
                            float *src01 = src00 + (x1 - x0);
                            float *src10 = src00 + (y1 - y0) * proto_width;
                            float *src11 = src10 + (x1 - x0);
                            float mask_val = ((float)*src00 * w00) + ((float)*src01 * w01) + ((float)*src10 * w10) + ((float)*src11 * w11);

                            if (mask_val > pixel_threshold)
                            {
                                dst[j] = cls;
                            }
                        }
                    }
                }
            }

            return AIENGINE_NO_ERROR;
        }

    private:
        typedef Geometry2D<int> Geom; // 导入二维几何运算模块
        typedef Geom::Point GeomPoint; // 使用几何运算模块的Point
        typedef Geom::Polygon GeomPolygon; // 使用几何运算模块的Polygon

        /**
         * @brief    
         *           sigmoid函数
         *           
         * @param    x:     输入值
         *           
         * @retval   sigmoid函数返回值
         *           
         * @date     2024-10-22 Created by HuangJP
         */
        inline static double Sigmoid(double x)
        {
            return (1 / (1 + exp(-x)));
        }

        /**
         * @brief    
         *           逆解sigmoid函数
         *           
         * @param    y:     给定输出值
         *           
         * @retval   y值对应到sigmoid函数的x轴坐标
         *           
         * @date     2024-10-25 Created by HuangJP
         */
        inline static double Inverse_Sigmoid(double y)
        {
            if (y <= 0.0 || y >= 1.0)
            {
                return NAN;
            }
            return log(y / (1.0f - y));
        }

        /**
         * @brief    
         *           目标检测+分割输出数据结构
         *           
         * @date     2024-10-22 Created by HuangJP
         */
        struct BBoxSeg: DetectionBBoxInfo
        {
        public:
            int idx;                            // 数据索引
            std::vector<float> coefficient;     // 掩码图像系数
        };
    };

    /**
     * @brief    
     *           字符检测任务类
     *           
     * @param    tensor_t:      输出张量数据类型
     *           
     * @date     2024-08-07 Created by HuangJP
     */
    template <typename tensor_t>
    class OCR_Detection {
    public:
        /**
         * @brief    
         *           PaddleOCRv3字符检测模型后处理
         *           
         * @param    output_tensor:             指向输出张量的指针
         * @param    o_height:                  输出张量高度
         * @param    o_width:                   输出张量宽度
         * @param    pixel_threshold:           像素阈值，输出概率图中置信度大于阈值的像素点才被认为是字符像素
         * @param    box_threshold:             预测框阈值，预测框中所有像素的平均置信度大于阈值时才会返回相应结果
         * @param    fy,fx:                     检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:             检测区域左上角点相对原点的偏移地址
         * @param    box_scale_factor:          预测框缩放系数
         * @param    ocr_det_results:           存储输出结果的vector指针
         * @param    use_diagonal_center:       使用较长的对角线的中心作为旋转中心
         * @param    box_scale_factor_short:    预测框短边缩放系数，用于调节短边的扩充长度
         *           
         * @retval   错误码
         *           
         * @date     2024-08-07 Created by HuangJP
         */
        static int PaddleOCRv3(tensor_t *output_tensor, int o_height, int o_width, float pixel_threshold, float box_threshold, float fy, float fx, int bias_y, int bias_x, float box_scale_factor, std::vector<QuadPoints> *ocr_det_results, bool use_diagonal_center = false, float box_scale_factor_short = 1.0f)
        {
            // 判断传入参数是否有效
            if (ocr_det_results == nullptr)
            {
                LOGE("A null pointer have been encountered.");
                return AIENGINE_INVALID_PARAM;
            }

            // 解析输出结果，获取所有可能的字符边框
            for (int i = 0; i < o_height; i++)
            {
                tensor_t *src = output_tensor + o_width * i; // 获取指向每行首个像素的指针
                for (int j = 0; j < o_width; j++)
                {
                    // 遍历概率图像，找到置信度高于阈值的像素点
                    if (src[j] <= pixel_threshold)
                    {
                        continue;
                    }

                    // 找到置信度高于阈值的像素点，通过深度优先搜索获取预测框
                    QuadPoints quads;
                    float box_score = _dfs_get_predict_box<false>(output_tensor, o_height, o_width, pixel_threshold, box_threshold, j, i, fy, fx, bias_y, bias_x, quads);
                    if (box_score < box_threshold)
                    {
                        // 预测框置信度低于阈值，不添加到结果中
                        continue;
                    }

                    // 向外扩充预测框
                    bool dilate_rs = _dilate_predict_box(quads, box_scale_factor, use_diagonal_center, box_scale_factor_short);
                    if (dilate_rs == false)
                    {
                        // 扩充预测框失败，不添加到结果中
                        continue;
                    }

                    ocr_det_results->emplace_back(quads); // 添加结果
                }
            }

            return AIENGINE_NO_ERROR;
        }
    private:
        typedef Geometry2D<int> Geom; // 导入二维几何运算模块
        typedef Geom::Point GeomPoint; // 使用几何运算模块的Point

        /**
         * @brief    
         *           两个点的高度比较函数
         *           
         * @param    a, b:      需要比较的坐标点
         *           
         * @retval   第一个点是否高于第二个点，如果高度相同，则判断第二个点是否在第一个点左边
         *           
         * @date     2024-08-08 Created by HuangJP
         */
        static inline bool _point_cmp(GeomPoint a, GeomPoint b)
        {
            if (a.y == b.y)
            {
                // 两个点高度相同时，判断第二个点是否在第一个点左边
                return b.x < a.x;
            }

            // 判断第一个点是否高于第二个点
            return a.y < b.y;
        }

        /**
         * @brief    
         *           向外扩大四边形预测框
         *           
         * @param    quads:                 四边形预测框四个顶点坐标
         * @param    scale_factor:          预测框缩放系数
         * @param    use_diagonal_center:   使用较长的对角线的中心作为旋转中心
         * @param    scale_factor_short:    预测框短边缩放系数，用于调节短边的扩充长度
         *           
         * @retval   true:              外扩成功
         * @retval   false:             外扩失败
         *           
         * @date     2024-08-09 Created by HuangJP
         */
        static bool _dilate_predict_box(QuadPoints &quads, float scale_factor, bool use_diagonal_center = true, float scale_factor_short = 1.0f)
        {
            // 计算质心
            int centroid_x = (quads.x0 + quads.x1 + quads.x2 + quads.x3) / 4;
            int centroid_y = (quads.y0 + quads.y1 + quads.y2 + quads.y3) / 4;

            // 判断是否使用对角线的中心作为质心
            if (use_diagonal_center == true)
            {
                // 计算对角线长度
                float diagonal_len1 = Geom::Calculate_Distance(quads.x0, quads.y0, quads.x2, quads.y2);
                float diagonal_len2 = Geom::Calculate_Distance(quads.x1, quads.y1, quads.x3, quads.y3);
                centroid_x = diagonal_len1 > diagonal_len2
                                ? (quads.x0 + quads.x2) / 2
                                : (quads.x1 + quads.x3) / 2;
                centroid_y = diagonal_len1 > diagonal_len2
                                ? (quads.y0 + quads.y2) / 2
                                : (quads.y1 + quads.y3) / 2;
            }

            // 确定矩形高度和宽度
            float width1 = Geom::Calculate_Distance(quads.x0, quads.y0, quads.x1, quads.y1);
            float width2 = Geom::Calculate_Distance(quads.x2, quads.y2, quads.x3, quads.y3);
            float height1 = Geom::Calculate_Distance(quads.x3, quads.y3, quads.x0, quads.y0);
            float height2 = Geom::Calculate_Distance(quads.x1, quads.y1, quads.x2, quads.y2);

            // 获取最大的宽度和高度
            float width = std::max(width1, width2);
            float height = std::max(height1, height2);

            // 高度和宽度不能为零
            if ((width <= 0.0f) || (height <= 0.0f))
            {
                return false;
            }

            // 计算斜率
            float slope = 0.0f;
            if (width > height)
            {
                // 水平方向计算斜率
                float delta_y = ((float)(quads.y2 - quads.y3) + (float)(quads.y1 - quads.y0)) / 2.0f;
                float delta_x = ((float)(quads.x2 - quads.x3) + (float)(quads.x1 - quads.x0)) / 2.0f;
                slope = delta_y / delta_x;

                // 计算点到直线的距离，作为新的高度
                GeomPoint base_vec = Geom::Get_Vector((GeomPoint){.x=quads.x3, .y=quads.y3}, (GeomPoint){.x=quads.x2, .y=quads.y2});
                height1 = abs(Geom::Cross_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x3, .y=quads.y3}, (GeomPoint){.x=quads.x0, .y=quads.y0})));
                height1 /= width2;
                height2 = abs(Geom::Cross_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x3, .y=quads.y3}, (GeomPoint){.x=quads.x1, .y=quads.y1})));
                height2 /= width2;

                // 高度不能为0
                if ((height1 <= 0.0f) || (height2 <= 0.0f))
                {
                    return false;
                }

                // 计算对角边在直线上的投影，作为新的宽度
                float new_width1 = fabs(Geom::Dot_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x3, .y=quads.y3}, (GeomPoint){.x=quads.x1, .y=quads.y1})) / width2);
                float new_width2 = fabs(Geom::Dot_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x2, .y=quads.y2}, (GeomPoint){.x=quads.x0, .y=quads.y0})) / width2);
                width = std::max(new_width1, new_width2);

                // 宽度不能为0
                if (width <= 0.0f)
                {
                    return false;
                }

                // 向外扩像素点
                width1 = width * scale_factor / 2; // 按照系数缩放
                width2 = width1;

                // 高度方向向外扩像素点
                int error = width / 2 * (scale_factor - 1); // 计算外扩的像素数
                height1 = (height1 + error * scale_factor_short) / 2;
                height2 = (height2 + error * scale_factor_short) / 2;
            }
            else
            {
                // 竖直方向计算斜率
                float delta_y = ((float)(quads.y1 - quads.y2) + (float)(quads.y0 - quads.y3)) / 2.0f;
                float delta_x = ((float)(quads.x1 - quads.x2) + (float)(quads.x0 - quads.x3)) / 2.0f;
                slope = delta_y / delta_x;
                slope = -1 / slope; // 因为是竖直方向，所以要取法线斜率用作旋转

                // 计算点到直线的距离，作为新的宽度
                GeomPoint base_vec = Geom::Get_Vector((GeomPoint){.x=quads.x2, .y=quads.y2}, (GeomPoint){.x=quads.x1, .y=quads.y1});
                width1 = abs(Geom::Cross_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x2, .y=quads.y2}, (GeomPoint){.x=quads.x0, .y=quads.y0})));
                width1 /= height2;
                width2 = abs(Geom::Cross_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x2, .y=quads.y2}, (GeomPoint){.x=quads.x3, .y=quads.y3})));
                width2 /= height2;

                // 宽度不能为0
                if ((width1 <= 0.0f) || (width2 <= 0.0f))
                {
                    return false;
                }

                // 计算对角边在直线上的投影，作为新的高度
                float new_height1 = abs(Geom::Dot_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x1, .y=quads.y1}, (GeomPoint){.x=quads.x3, .y=quads.y3})) / height2);
                float new_height2 = abs(Geom::Dot_Product(base_vec, Geom::Get_Vector((GeomPoint){.x=quads.x2, .y=quads.y2}, (GeomPoint){.x=quads.x0, .y=quads.y0})) / height2);
                height = std::max(new_height1, new_height2);

                // 宽度不能为0
                if (height <= 0.0f)
                {
                    return false;
                }

                // 向外扩像素点
                height1 = height * scale_factor / 2; // 按照系数缩放
                height2 = height1;

                // 宽度方向像外扩像素点
                int error = height / 2 * (scale_factor - 1); // 计算像外扩的像素点数
                width1 = (width1 + error * scale_factor_short) / 2;
                width2 = (width2 + error * scale_factor_short) / 2;
            }

            // 将斜率转换为旋转角度
            float angle_radians = atan(slope);

            // 计算旋转方程的cos值和sin值
            float cos_val = cos(angle_radians);
            float sin_val = sin(angle_radians);

            // 旋转函数
            auto rotate = [](int &new_x, int &new_y, float x, float y, int c_x, int c_y, float cos_val, float sin_val){
                new_x = (int)(x * cos_val - y * sin_val) + c_x;
                new_y = (int)(x * sin_val + y * cos_val) + c_y;
            };

            // 旋转得到扩充后的坐标点
            rotate(quads.x0, quads.y0, -width1, -height1, centroid_x, centroid_y, cos_val, sin_val);
            rotate(quads.x1, quads.y1, width2, -height2, centroid_x, centroid_y, cos_val, sin_val);
            rotate(quads.x2, quads.y2, width2, height2, centroid_x, centroid_y, cos_val, sin_val);
            rotate(quads.x3, quads.y3, -width1, height1, centroid_x, centroid_y, cos_val, sin_val);

            return true;
        }

        /**
         * @brief    
         *           在边界中找到极值点
         *           
         * @param    border:        边界坐标点数组
         * @param    top:           上极值点
         * @param    bottom:        下极值点
         *           
         * @date     2024-08-09 Created by HuangJP
         */
        static void _find_extreme_point(std::vector<GeomPoint> border, GeomPoint &top, GeomPoint &bottom, bool is_left)
        {
            // 赋初值
            top = border.back();
            bottom = border.front();

            // 找到y轴最大和最小的点
            int xmin = INT32_MAX, xmax = INT32_MIN;
            int ymin = INT32_MAX, ymax = INT32_MIN;
            for (auto point: border)
            {
                top = (top.y > point.y) ? point : top;
                bottom = (bottom.y < point.y) ? point : bottom;
                xmin = std::min(point.x, xmin);
                xmax = std::max(point.x, xmax);
                ymin = std::min(point.y, ymin);
                ymax = std::max(point.y, ymax);
            }

            // 两点间的高度和宽度判断判断是横向框还是竖向框
            int height = abs(bottom.y - top.y);
            int width = abs(bottom.x - top.x);

            // 横向框
            std::vector<GeomPoint> filter_border;
            if (height < width)
            {
                // 左边界
                if (is_left == true)
                {
                    for (auto point: border)
                    {
                        if (point.x > xmin+3)
                        {
                            continue;
                        }

                        filter_border.emplace_back(point);
                    }
                }
                // 右边界
                else
                {
                    for (auto point: border)
                    {
                        if (point.x < xmax-3)
                        {
                            continue;
                        }

                        filter_border.emplace_back(point);
                    }
                }

                // 在过滤边界中找到y轴最大和最小的点
                top = filter_border.back();
                bottom = filter_border.front();
                for (auto point: filter_border)
                {
                    top = top.y > point.y ? point : top;
                    bottom = bottom.y < point.y ? point : bottom;
                }
            }
            // 竖向框
            else
            {
                auto cmp = is_left ? [](int a, int b){return a > b;} : [](int a, int b){return a < b;};
                for (auto point: border)
                {
                    // 在过滤边界中找到x轴最大和最小的点
                    if (point.y <= ymin+3)
                    {
                        top = cmp(top.x, point.x) ? point : top;
                    }

                    if (point.y >= ymax-3)
                    {
                        bottom = cmp(bottom.x, point.x) ? point : bottom;
                    }
                }
            }
        }

        /**
         * @brief    
         *           从边界中找出四边形
         *           
         * @param    left_border:           左边界坐标数组
         * @param    right_border:          右边界坐标数组
         * @param    fy,fx:                 检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:         检测区域左上角点相对原点的偏移地址
         *           
         * @retval   四边形四个顶点坐标
         *           
         * @date     2024-08-07 Created by HuangJP
         */
        static QuadPoints _find_quadrangle_from_border(std::vector<GeomPoint> left_border, std::vector<GeomPoint> right_border, float fy, float fx, int bias_y, int bias_x)
        {
            // 从上至下，从右往左排序边界点
            std::sort(left_border.begin(), left_border.end(), _point_cmp);
            std::sort(right_border.begin(), right_border.end(), _point_cmp);

            // 判断是横向框还是竖向框
            int width = abs(left_border.front().x - left_border.back().x);
            int height = abs(left_border.front().y - left_border.back().y);
            bool is_landscape = height < width;

            // 获取极左点和极右点
            GeomPoint left_point = left_border.front();
            GeomPoint right_point = right_border.back();
            if (is_landscape == true)
            {
                // 找最左边的边界点
                for (auto point: left_border)
                {
                    if (left_point.x > point.x)
                    {
                        left_point = point;
                    }
                }
                // 找到最右边的边界点
                for (auto point: right_border)
                {
                    if (right_point.x <= point.x)
                    {
                        right_point = point;
                    }
                }
            }

            // 获取极值点
            struct ExtremePoint {
                GeomPoint point;
                int area;
            };
            // 在右边界找极值点
            GeomPoint base_vec = Geom::Get_Vector(left_point, right_point); // 基准向量
            ExtremePoint right_max = (ExtremePoint){.point = right_border[0], .area = INT32_MIN};
            ExtremePoint right_min = (ExtremePoint){.point = right_border[0], .area = INT32_MAX};
            for (auto point: right_border)
            {
                GeomPoint vec = Geom::Get_Vector(left_point, point); // 左边点指向右边界的向量
                int area = Geom::Cross_Product(base_vec, vec); // 计算两个向量围成的面积
                right_max = right_max.area < area ? (ExtremePoint){.point = point, .area = area} : right_max; // 获取正数面积最大的点
                right_min = right_min.area > area ? (ExtremePoint){.point = point, .area = area} : right_min; // 获取负数面积最大的点
            }
            // 在左边界找极值点
            base_vec = Geom::Get_Vector(right_point, left_point); // 基准向量
            ExtremePoint left_max = (ExtremePoint){.point = left_border[0], .area = INT32_MIN};
            ExtremePoint left_min = (ExtremePoint){.point = left_border[0], .area = INT32_MAX};
            for (auto point: left_border)
            {
                GeomPoint vec = Geom::Get_Vector(right_point, point); // 左边点指向右边界的向量
                int area = Geom::Cross_Product(base_vec, vec); // 计算两个向量围成的面积
                left_max = left_max.area < area ? (ExtremePoint){.point = point, .area = area} : left_max; // 获取正数面积最大的点
                left_min = left_min.area > area ? (ExtremePoint){.point = point, .area = area} : left_min; // 获取负数面积最大的点
            }

            // 从4种组合中获取能围成最大面积的极值点
            ExtremePoint point_group[][2] = {
                {left_max, right_max},
                {left_max, right_min},
                {left_min, right_max},
                {left_min, right_min},
            };
            int max_area = INT32_MIN;
            int max_area_idx = 0;
            for (int i = 0; i < (int)(sizeof(point_group)/sizeof(point_group[0])); i++)
            {
                int area = abs(point_group[i][1].area + point_group[i][0].area);
                if (max_area < area)
                {
                    max_area = area;
                    max_area_idx = i;
                }
            }

            // 按照左上、右上、右下、左下的方向填充结果
            GeomPoint left_point_second = point_group[max_area_idx][0].point;
            GeomPoint right_point_second = point_group[max_area_idx][1].point;
            Geom::Polygon convex = {
                left_point,
                right_point,
                right_point_second,
                left_point_second,
            };
            convex = Geom::Sort_Convex_Polygon_With_Erase(convex);
            QuadPoints quads = (QuadPoints){
                .x0 = (int)((float)convex[0].x * fx + bias_x), .y0 = (int)((float)convex[0].y * fy + bias_y),
                .x1 = (int)((float)convex[1].x * fx + bias_x), .y1 = (int)((float)convex[1].y * fy + bias_y),
                .x2 = (int)((float)convex[2].x * fx + bias_x), .y2 = (int)((float)convex[2].y * fy + bias_y),
                .x3 = (int)((float)convex[3].x * fx + bias_x), .y3 = (int)((float)convex[3].y * fy + bias_y),
            };

            return quads;
        }

        /**
         * @brief    
         *           通过深度优先搜索获取预测框
         *           
         * @param    use_area:              使用面积方法获取预测框
         * @param    output_tensor:         指向输出张量的指针
         * @param    o_height:              输出张量高度
         * @param    o_width:               输出张量宽度
         * @param    pixel_threshold:       像素阈值，输出概率图中置信度大于阈值的像素点才被认为是字符像素
         * @param    box_threshold:         预测框阈值，预测框中所有像素的平均置信度大于阈值时才会返回相应结果
         * @param    fy,fx:                 检测边框坐标在y轴和x轴上的缩放系数
         * @param    bias_y,bias_x:         检测区域左上角点相对原点的偏移地址
         * @param    quads:                 预测框坐标
         *           
         * @retval   预测框中所有像素的平均置信度
         *           
         * @date     2024-08-07 Created by HuangJP
         */
        template <bool use_area>
        static float _dfs_get_predict_box(tensor_t *output_tensor, int o_height, int o_width, float pixel_threshold, float box_threshold, int start_x, int start_y, float fy, float fx, int bias_y, int bias_x, QuadPoints &quads)
        {
            // 方向数组（左、下、右、上）
            const int dx[] = {-1, 0, 1, 0};
            const int dy[] = {0, 1, 0, -1};
            const int count_dir = std::min(sizeof(dx)/sizeof(dx[0]), sizeof(dy)/sizeof(dy[0]));

            // 像素个数计数
            int count_pixel = 0;
            double box_score = 0.0f;

            // 左右边界数组
            std::vector<GeomPoint> left_border;
            std::vector<GeomPoint> right_border;

            // 初始化栈
            std::stack<GeomPoint> stk;
            stk.push((GeomPoint){.x = start_x, .y = start_y}); // 将第一个点压入栈内

            // 在栈非空时继续搜索
            while (!stk.empty())
            {
                // 获取指向当前像素点的指针
                GeomPoint cur = stk.top(); stk.pop();
                tensor_t *ptr = output_tensor + cur.y * o_width + cur.x;

                // 判断当前点是否在图像边界内，且置信度是否高于阈值
                if ((cur.x < 0)
                    || (cur.x >= o_width)
                    || (cur.y < 0)
                    || (cur.y >= o_height)
                    || (*ptr <= pixel_threshold))
                {
                    continue;
                }

                // 将与当前点相邻的点加入到栈内
                for (int i = 0; i < count_dir; i++)
                {
                    stk.push((GeomPoint){.x=cur.x+dx[i], .y=cur.y+dy[i]});
                }

                // 标记当前点已遍历
                count_pixel++; // 记录像素点个数
                box_score += *ptr; // 累加置信度
                *ptr = -1.0f;

                // 判断当前点是否为左边界
                if ((cur.x - 1) < 0)
                {
                    // 当前点在图像最左边，判断为左边界
                    left_border.emplace_back(cur);
                }
                else
                {
                    tensor_t score = *(ptr - 1);
                    if ((score >= 0.0f)
                        && (score <= pixel_threshold))
                    {
                        // 当前点左边点的置信度大于0（不在预测框内）且小于像素阈值，判断为左边界
                        left_border.emplace_back(cur);
                    }
                }

                // 判断当前点是否为右边界
                if ((cur.x + 1) >= o_width)
                {
                    // 当前点在图像最右边，判断为右边界
                    right_border.emplace_back(cur);
                }
                else
                {
                    tensor_t score = *(ptr + 1);
                    if ((score >= 0.0f)
                        && (score <= pixel_threshold))
                    {
                        // 当前点右边点的置信度大于0（不在预测框内）且小于像素阈值，判断为右边界
                        right_border.emplace_back(cur);
                    }
                }
            }

            // 计算平均置信度，判断是否要过滤结果
            box_score /= (double)count_pixel;
            if (box_score < box_threshold)
            {
                // 平均置信度低于阈值，过滤结果，不再找四边形顶点
                return box_score;
            }

            if (use_area)
            {
                // 从边界提取四边形顶点
                quads = _find_quadrangle_from_border(left_border, right_border, fy, fx, bias_y, bias_x);
            }
            else
            {
                // 在左边界中找到极值点
                GeomPoint p0, p1, p2, p3;
                _find_extreme_point(left_border, p0, p3, true);
                _find_extreme_point(right_border, p1, p2, false);

                // 将坐标点添加到polygon中
                quads = (QuadPoints){
                    .x0 = (int)((float)p0.x * fx + bias_x), .y0 = (int)((float)p0.y * fy + bias_y),
                    .x1 = (int)((float)p1.x * fx + bias_x), .y1 = (int)((float)p1.y * fy + bias_y),
                    .x2 = (int)((float)p2.x * fx + bias_x), .y2 = (int)((float)p2.y * fy + bias_y),
                    .x3 = (int)((float)p3.x * fx + bias_x), .y3 = (int)((float)p3.y * fy + bias_y),
                };
            }

            // 返回预测框置信度
            return box_score;
        }
    };

    template <typename tensor_t>
    class OCR_Recognize {
    public:
        /**
         * @brief    
         *           PaddleOCRv4模型字符识别后处理（CTC标签解码）
         *           
         * @param    output_tensor:         指向输出张量的指针
         * @param    length:                字符串长度（输出结果个数）
         * @param    char_classes:          字符类别数
         * @param    score_threshold:       单个字符的置信度阈值
         * @param    dict:                  指向字符字典的指针
         * @param    ocr_rec_results:       存储输出结果的vector指针
         *           
         * @retval   错误码
         *           
         * @date     2024-08-13 Created by HuangJP
         */
        static int PaddleOCRv4_CTC_Label_Decode(tensor_t *output_tensor, int length, int char_classes, float score_threshold, std::vector<const char *> *dict, std::vector<OCRCharacter> *ocr_rec_results)
        {
            // 判断传入参数是否有效
            if ((dict == nullptr)
                || (ocr_rec_results == nullptr))
            {
                LOGE("A null pointer have been encountered.");
                return AIENGINE_INVALID_PARAM;
            }

            // 定义变量
            std::vector<int> text_index(length); // 每个字符在字典中的索引
            std::vector<float> text_prob(length); // 每个字符的置信度

            // 遍历全部字符结果
            for (int i = 0; i < length; i++)
            {
                int index = 0; // 字符在字典中的索引
                float max_score = 0.0f; // 最大类别概率
                tensor_t *row_ptr = output_tensor + i * char_classes; // 行指针

                // 遍历所有字符的类别概率，找到类别概率最高的结果
                for (int j = 0; j < char_classes; j++)
                {
                    float score = row_ptr[j];
                    if (score > max_score)
                    {
                        max_score = score;
                        index = j - 1;
                    }
                }

                // 判断字符置信度是否超过阈值
                if (max_score < score_threshold)
                {
                    // 丢弃置信度低于阈值的结果
                    text_index[i] = -1;
                    text_prob[i] = 0.0f;
                    continue;
                }

                // 将字符在字典中的索引和置信度添加到列表中
                text_index[i] = index;
                text_prob[i] = max_score;
            }

            // 利用不等性去重
            int dict_len = dict->size(); // 字典长度
            // 默认第一个字符有效
            int idx = text_index[0];
            if (_is_character_index_valid(idx, dict_len))
            {
                // 字符有效时，将字符添加到结果中
                unicode_t character = 0x00;
                memcpy(&character, (*dict)[idx], std::min(strlen((*dict)[idx]), sizeof(unicode_t)));
                ocr_rec_results->emplace_back(OCRCharacter{character, text_prob[0]});
            }
            // 循环判断剩余字符的重复性
            for (int i = 1; i < length; i++)
            {
                // 判断字符是否重复
                if (text_index[i] == text_index[i-1])
                {
                    // 重复字符不添加到结果中
                    continue;
                }

                // 判断字符是否有效
                idx = text_index[i];
                if (_is_character_index_valid(idx, dict_len))
                {
                    // 字符有效时，将字符添加到结果中
                    unicode_t character = 0x00;
                    memcpy(&character, (*dict)[idx], std::min(strlen((*dict)[idx]), sizeof(unicode_t)));
                    ocr_rec_results->emplace_back(OCRCharacter{character, text_prob[i]});
                }
            }

            // 返回结果
            return AIENGINE_NO_ERROR;
        }

        /**
         * @brief    
         *           PaddleOCRv4模型字符识别后处理（自定义CTC标签解码）
         *           
         * @param    output_tensor:         指向输出张量的指针
         * @param    length:                字符串长度（输出结果个数）
         * @param    char_classes:          字符类别数
         * @param    score_threshold:       单个字符的置信度阈值
         * @param    dict:                  指向字符字典的指针
         * @param    ocr_rec_results:       存储输出结果的vector指针
         *           
         * @retval   错误码
         *           
         * @date     2024-08-13 Created by HuangJP
         */
        static int PaddleOCRv4_CTC_Label_Decode_Custom(tensor_t *output_tensor, int length, int char_classes, float score_threshold, std::vector<const char *> *dict, std::vector<OCRCharacter> *ocr_rec_results)
        {
            // 变量定义
            int last_char_pos = 0; // 上一个字符所在位置
            float last_char_score = 0.0f; // 上一个字符的置信度
            int dict_len = dict->size(); // 字典长度

            // 遍历全部字符结果
            for (int i = 0; i < length; i++)
            {
                int index = 0; // 字符在字典中的索引
                float max_score = 0.0f; // 最大类别概率
                tensor_t *row_ptr = output_tensor + i * char_classes; // 行指针

                // 遍历所有字符的类别概率，找到类别概率最高的结果
                for (int j = 0; j < char_classes; j++)
                {
                    float score = row_ptr[j];
                    if (score > max_score)
                    {
                        max_score = score;
                        index = j - 1;
                    }
                }

                // 跳过空字符和置信度低于阈值的字符
                if ((_is_character_index_valid(index, dict_len) == false)
                    || (max_score < score_threshold))
                {
                    continue;
                }

                // 字符去重
                if (last_char_pos != 0)
                {
                    // 确认字符是否重复
                    if ((i - last_char_pos) <= 1)
                    {
                        // 重复的字符中选择置信度最高的字符
                        if (last_char_score < max_score)
                        {
                            last_char_score = max_score; // 更新置信度
                            (*ocr_rec_results)[ocr_rec_results->size()-1] = (OCRCharacter){.character = (*dict)[index][0], .score = max_score}; // 替换重复字符
                        }
                        last_char_pos = i; // 记录重复字符的位置
                        continue;
                    }
                }

                // 将字符添加到返回结果中
                last_char_pos = i;
                last_char_score = max_score;
                ocr_rec_results->emplace_back((OCRCharacter){.character = (*dict)[index][0], .score = max_score});
            }

            return AIENGINE_NO_ERROR;
        }
    private:
        /**
         * @brief    
         *           判断字符是否有效
         *           
         * @param    idx:           字符在字典中的索引
         * @param    dict_len:      字典长度
         *           
         * @retval   true: 当字符索引大于0且小于字典长度时，字符索引有效
         * @retval   false: 字符索引无效
         *           
         * @date     2024-08-13 Created by HuangJP
         */
        static inline bool _is_character_index_valid(int idx, int dict_len)
        {
            return ((idx >= 0) && (idx < dict_len));
        }
    };

    /**
     * @brief    
     *           BasePostprocess类析构函数
     *           
     * @date     2024-08-06 Created by HuangJP
     */
    virtual ~BasePostprocess()
    {

    }

    /**
     * @brief    
     *           BasePostprocess类构造函数
     *           
     * @param    model_data:    模型数据
     *           
     * @date     2024-08-06 Created by HuangJP
     */
    BasePostprocess(const BaseModelLoader::ModelData *model_data):
        _model_data(model_data)
    {

    }
};

#endif
//-----------------------------------------------------------------------------
//  End of file