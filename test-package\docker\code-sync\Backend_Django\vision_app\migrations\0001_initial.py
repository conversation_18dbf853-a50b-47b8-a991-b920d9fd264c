# Generated by Django 5.2.1 on 2025-05-08 10:52

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AIModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("model_file", models.FileField(upload_to="ai_models/")),
                ("version", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "model_type",
                    models.CharField(
                        choices=[
                            ("barcode", "Barcode Detection"),
                            ("ocr", "OCR Model"),
                            ("restoration", "Image Restoration"),
                        ],
                        default="barcode",
                        max_length=20,
                    ),
                ),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.<PERSON>olean<PERSON>ield(default=True)),
            ],
        ),
        migrations.CreateModel(
            name="UploadedImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image", models.ImageField(upload_to="uploaded_images/")),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
