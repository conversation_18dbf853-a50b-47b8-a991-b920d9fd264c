# AI Vision App 环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# =============================================================================
# 应用基础配置
# =============================================================================

# 应用名称和版本
APP_NAME=ai-vision-app
VERSION=latest

# Docker仓库配置
REGISTRY=localhost:5000

# =============================================================================
# Django 后端配置
# =============================================================================

# Django 密钥（生产环境必须修改）
DJANGO_SECRET_KEY=your-very-secret-key-change-this-in-production

# 调试模式（生产环境设置为False）
DJANGO_DEBUG=False

# =============================================================================
# 网络配置
# =============================================================================

# 端口配置
FRONTEND_DEV_PORT=5173          # 前端开发服务器端口
FRONTEND_PROD_PORT=8080         # 前端生产环境端口
BACKEND_PORT=8000               # Django后端端口

# 主机配置
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com  # 允许的主机（逗号分隔）
LAN_IPS=*************           # 局域网IP地址（逗号分隔）

# CORS配置
# CORS_ORIGINS=http://localhost:5173,http://localhost:8080  # 自定义CORS源（可选）
CORS_ALLOW_ALL_ORIGINS=False    # 是否允许所有源（仅开发环境）

# Docker环境主机配置
BACKEND_DOCKER_HOST=backend     # Docker环境后端主机名
FRONTEND_DOCKER_HOST=localhost  # Docker环境前端主机名

# 数据库配置
DATABASE_URL=sqlite:///app/db.sqlite3

# =============================================================================
# CORS 配置
# =============================================================================

# 允许的源（用逗号分隔）
CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1,https://your-domain.com

# =============================================================================
# 文件上传配置
# =============================================================================

# 最大上传文件大小（字节）
MAX_UPLOAD_SIZE=104857600  # 100MB

# 媒体文件URL前缀
MEDIA_URL=/media/

# 静态文件URL前缀
STATIC_URL=/static/

# =============================================================================
# AI 模型配置
# =============================================================================

# 系统模型根目录
SYSTEM_MODELS_ROOT=/app/models/system_models

# 自定义模型根目录
CUSTOM_MODELS_ROOT=/app/models/custom_models

# 默认推理设备（cpu 或 cuda）
DEFAULT_DEVICE=cpu

# =============================================================================
# 扫码枪设备配置
# =============================================================================

# 是否启用扫码枪功能
SCANNER_ENABLED=true

# 扫码枪默认IP地址
SCANNER_DEFAULT_IP=************

# 扫码枪默认端口
SCANNER_DEFAULT_PORT=8080

# 扫码枪连接超时时间（秒）
SCANNER_TIMEOUT=30

# 扫码枪图像质量设置
SCANNER_IMAGE_QUALITY=high

# 扫码枪帧率设置（fps）
SCANNER_FPS=30

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=/app/logs/django.log

# 日志文件最大大小（字节）
LOG_MAX_SIZE=15728640  # 15MB

# 日志文件备份数量
LOG_BACKUP_COUNT=10

# =============================================================================
# 缓存配置
# =============================================================================

# 缓存后端
CACHE_BACKEND=django.core.cache.backends.locmem.LocMemCache

# 缓存超时时间（秒）
CACHE_TIMEOUT=300

# 缓存最大条目数
CACHE_MAX_ENTRIES=1000

# =============================================================================
# 会话配置
# =============================================================================

# 会话超时时间（秒）
SESSION_COOKIE_AGE=86400  # 24小时

# 会话保存频率
SESSION_SAVE_EVERY_REQUEST=True

# =============================================================================
# 安全配置
# =============================================================================

# 是否启用HTTPS重定向
SECURE_SSL_REDIRECT=False

# HSTS 设置
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# Cookie 安全设置
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# =============================================================================
# 邮件配置（可选）
# =============================================================================

# 邮件后端
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# SMTP 配置
EMAIL_HOST=localhost
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# =============================================================================
# 时区和国际化
# =============================================================================

# 时区
TIME_ZONE=Asia/Shanghai

# 语言代码
LANGUAGE_CODE=zh-hans

# 是否启用国际化
USE_I18N=True
USE_L10N=True
USE_TZ=True

# =============================================================================
# 性能配置
# =============================================================================

# 数据库连接超时（秒）
DATABASE_TIMEOUT=20

# 请求超时时间（秒）
REQUEST_TIMEOUT=60

# 工作进程数（Gunicorn）
WORKERS=2

# 每个工作进程的线程数
THREADS=4

# =============================================================================
# 监控和健康检查
# =============================================================================

# 健康检查URL
HEALTH_CHECK_URL=/health

# 监控数据收集间隔（秒）
MONITORING_INTERVAL=60

# =============================================================================
# 开发环境特定配置
# =============================================================================

# 是否启用Django调试工具栏
ENABLE_DEBUG_TOOLBAR=False

# 是否启用SQL查询日志
LOG_SQL_QUERIES=False

# 是否启用性能分析
ENABLE_PROFILING=False
