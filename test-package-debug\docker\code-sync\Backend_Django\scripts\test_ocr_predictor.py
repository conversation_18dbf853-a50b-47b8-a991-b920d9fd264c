import os
import django
import sys
import cv2

# Add the project root to the Python path
# This allows us to import modules from the Django project
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_project.settings')
try:
    django.setup()
except Exception as e:
    print(f"Error setting up Django: {e}")
    print("Please ensure you are running this script from within the Backend_Django directory or that your DJANGO_SETTINGS_MODULE is correctly set.")
    sys.exit(1)

from django.conf import settings
from vision_app.ocr_paddle_predictor import PaddleOCRSystemPredictor

def run_ocr_test():
    """
    Runs a test of the PaddleOCRSystemPredictor.
    """
    print("Starting OCR predictor test...")

    # --- Configuration: Update these paths if necessary ---
    # Construct model paths relative to settings.BASE_DIR (Backend_Django)
    # Note: Ensure these model paths are correct and the inference models exist there.
    # Example: 'models/system_models/ocr/car_liencese/AI_OCR_Det_CHNLP_NCHW_1x3x320x320/inference'
    #          'models/system_models/ocr/car_liencese/AI_OCR_Rec_CHNLP_NCHW_1x3x64x320/inference'

    det_model_relative_path = os.path.join('models', 'system_models', 'ocr', 'car_liencese', 'AI_OCR_Det_CHNLP_NCHW_1x3x320x320', 'inference')
    rec_model_relative_path = os.path.join('models', 'system_models', 'ocr', 'car_liencese', 'AI_OCR_Rec_CHNLP_NCHW_1x3x64x320', 'inference')
    
    det_model_dir = os.path.join(settings.BASE_DIR, det_model_relative_path)
    rec_model_dir = os.path.join(settings.BASE_DIR, rec_model_relative_path)

    # Update this to the actual path of your test image
    test_image_path = "D:/DeskTop/MFC_IMGPROCESS_AIENGINE/Picture/car_license/4983349.bmp" # Example, please change

    print(f"Django settings.BASE_DIR: {settings.BASE_DIR}")
    print(f"Attempting to use Detection Model Dir: {det_model_dir}")
    print(f"Attempting to use Recognition Model Dir: {rec_model_dir}")
    print(f"Attempting to use Test Image: {test_image_path}")

    # --- Sanity checks for paths ---
    if not os.path.exists(det_model_dir):
        print(f"ERROR: Detection model directory not found: {det_model_dir}")
        return
    if not os.path.exists(rec_model_dir):
        print(f"ERROR: Recognition model directory not found: {rec_model_dir}")
        return
    if not os.path.exists(test_image_path):
        print(f"ERROR: Test image not found: {test_image_path}")
        print("Please update the 'test_image_path' variable in this script to a valid image file.")
        return

    # --- Initialize Predictor ---
    try:
        print("\nInitializing PaddleOCRSystemPredictor...")
        # Explicitly pass parameters based on run_infer.ps1 and common defaults
        ocr_predictor = PaddleOCRSystemPredictor(
            det_model_dir=det_model_dir,
            rec_model_dir=rec_model_dir,
            use_angle_cls=False,  # Assuming no angle classification for license plates
            det_algorithm='DB',   # Explicitly set detection algorithm
            rec_algorithm='CRNN', # Explicitly set recognition algorithm (can be 'SVTR' etc.)
            lang='ch',
            use_gpu=False,
            # Parameters from run_infer.ps1
            rec_image_shape="3,64,320",
            det_limit_type="max",
            det_limit_side_len=320,
            det_db_thresh=0.01,
            det_db_box_thresh=0.01,
            use_onnx=False, # Explicitly set use_onnx to False
            use_npu=False,  # Explicitly set use_npu to False (no NPU on Win10 CPU setup)
            use_mlu=False,  # Explicitly set use_mlu to False (no MLU)
            use_xpu=False,  # Explicitly set use_xpu to False (no XPU)
            return_word_box=False, # Explicitly set return_word_box to False
            # You might need to adjust/add other params based on PaddleOCRSystemPredictor's __init__
            # e.g., cls_model_dir, enable_mkldnn, cpu_threads, etc. if they are critical
        )
        print("PaddleOCRSystemPredictor initialized successfully.")
    except Exception as e:
        print(f"ERROR: Failed to initialize PaddleOCRSystemPredictor: {e}")
        import traceback
        traceback.print_exc()
        return

    # --- Perform Prediction ---
    try:
        print(f"\nLoading image: {test_image_path}...")
        img = cv2.imread(test_image_path)
        if img is None:
            print(f"ERROR: Failed to load image from {test_image_path}. cv2.imread returned None.")
            return
        
        print("Image loaded successfully. Performing OCR prediction...")
        dt_boxes, rec_res, time_dict = ocr_predictor.predict(img)
        
        print("\n--- OCR Prediction Results ---")
        print(f"Detection time: {time_dict.get('det', 'N/A'):.4f}s")
        print(f"Recognition time: {time_dict.get('rec', 'N/A'):.4f}s")
        print(f"Total time (excluding NMS and get_rotate_crop_image): {time_dict.get('all', {}).get('total_excluding_nms_crop', 'N/A'):.4f}s")
        
        if not dt_boxes and not rec_res:
            print("No text detected or recognized.")
        else:
            print("\nDetected Text Regions (dt_boxes):")
            for i, box in enumerate(dt_boxes):
                print(f"  Box {i+1}: {box.tolist()}") # .tolist() for cleaner printing of numpy arrays

            print("\nRecognized Text and Confidence (rec_res):")
            # rec_res format: list of tuples, e.g., [('text1', 0.99), ('text2', 0.95)]
            for i, (text, confidence) in enumerate(rec_res):
                print(f"  Result {i+1}: Text='{text}', Confidence={confidence:.4f}")
                
        print("\n--- End of OCR Prediction Results ---")

    except Exception as e:
        print(f"ERROR: An error occurred during OCR prediction: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_ocr_test()
    print("\nTest script finished.") 