import React from 'react';
import { Outlet } from 'react-router-dom'; // Outlet 用于渲染子路由

// 这个布局组件可以包含共享的导航栏、侧边栏等 (如果需要的话)
// 目前我们先让它简单地渲染子路由
const DashboardLayout: React.FC = () => {
  return (
    <div className="dashboard-layout">
      {/* 这里可以放共享的 Header, Sider 等 */}
      <main>
        <Outlet /> {/* 子页面会渲染在这里 */}
      </main>
      {/* 这里可以放共享的 Footer */}
    </div>
  );
};

export default DashboardLayout; 