from rest_framework import serializers
from .models import AIModel

class AIModelSerializer(serializers.ModelSerializer):
    file_path = serializers.CharField(source='model_file', read_only=True)

    class Meta:
        model = AIModel
        fields = [
            'id',
            'name',
            'version',
            'model_type',
            'ocr_role',
            'file_path', # 使用 model_file 作为源
            'uploaded_at',
            'is_system_model',
            'description',
            'ocr_collection_name'  # 新增OCR集合名称字段
        ]

class AIModelUploadSerializer(serializers.Serializer):
    model_file = serializers.FileField()
    name = serializers.CharField(max_length=255)
    model_type = serializers.CharField(max_length=50)
    version = serializers.CharField(max_length=50, required=True)  # 版本号改为必填
    description = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    ocr_role = serializers.CharField(max_length=20, required=False, allow_blank=True, allow_null=True)
    is_system_model = serializers.BooleanField(required=False, default=False)  # 新增：是否为系统模型
    ocr_collection_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)  # 新增：OCR集合名称

    def validate_model_file(self, value):
        """验证模型文件格式"""
        filename = value.name.lower()

        # 支持的文件格式
        allowed_extensions = {
            'barcode': ['.pt'],
            'ocr': ['.tar', '.tar.gz', '.zip'],
            'ai_restored': ['.onnx']
        }

        # 如果还没有model_type信息，暂时跳过验证（在validate方法中再次验证）
        return value

    def validate_model_type(self, value):
        """验证模型类型"""
        allowed_types = ['barcode', 'ocr', 'ai_restored', 'feature_matching']
        if value not in allowed_types:
            raise serializers.ValidationError(f"模型类型必须是以下之一: {', '.join(allowed_types)}")
        return value

    def validate_ocr_role(self, value):
        """验证OCR角色"""
        if value:
            allowed_roles = ['detection', 'recognition']
            if value not in allowed_roles:
                raise serializers.ValidationError(f"OCR角色必须是以下之一: {', '.join(allowed_roles)}")
        return value

    def validate(self, data):
        """综合验证"""
        model_type = data.get('model_type')
        model_file = data.get('model_file')
        ocr_role = data.get('ocr_role')

        # 验证文件格式与模型类型的匹配
        if model_file and model_type:
            filename = model_file.name.lower()

            allowed_extensions = {
                'barcode': ['.pt'],
                'ocr': ['.tar', '.tar.gz', '.zip'],
                'ai_restored': ['.onnx'],
                'feature_matching': ['.onnx']
            }

            allowed_exts = allowed_extensions.get(model_type, [])
            if not any(filename.endswith(ext) for ext in allowed_exts):
                raise serializers.ValidationError(
                    f"{model_type}模型只支持以下格式: {', '.join(allowed_exts)}"
                )

        # OCR模型必须指定角色
        if model_type == 'ocr' and not ocr_role:
            raise serializers.ValidationError("OCR模型必须指定角色 (detection 或 recognition)")

        # 非OCR模型不应该有OCR角色
        if model_type != 'ocr' and ocr_role:
            raise serializers.ValidationError("只有OCR模型才能指定角色")

        # 注意：我们移除了重复模型检查，将其移到视图中处理
        # 这样可以避免序列化器验证失败但模型已创建的问题

        return data


class AIModelUpdateSerializer(serializers.Serializer):
    """
    AI模型更新序列化器
    用于模型信息编辑功能
    """
    name = serializers.CharField(max_length=255)
    model_type = serializers.CharField(max_length=50)
    version = serializers.CharField(max_length=50, required=True)
    description = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    ocr_role = serializers.CharField(max_length=20, required=False, allow_blank=True, allow_null=True)
    is_system_model = serializers.BooleanField(required=False, default=False)
    ocr_collection_name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)  # 新增：OCR集合名称

    def validate_model_type(self, value):
        """验证模型类型"""
        allowed_types = ['barcode', 'ocr', 'ai_restored', 'feature_matching']
        if value not in allowed_types:
            raise serializers.ValidationError(f"模型类型必须是以下之一: {', '.join(allowed_types)}")
        return value

    def validate_ocr_role(self, value):
        """验证OCR角色"""
        if value:
            allowed_roles = ['detection', 'recognition']
            if value not in allowed_roles:
                raise serializers.ValidationError(f"OCR角色必须是以下之一: {', '.join(allowed_roles)}")
        return value

    def validate(self, data):
        """综合验证"""
        model_type = data.get('model_type')
        ocr_role = data.get('ocr_role')

        # OCR模型必须指定角色
        if model_type == 'ocr' and not ocr_role:
            raise serializers.ValidationError("OCR模型必须指定角色 (detection 或 recognition)")

        # 非OCR模型不应该有OCR角色
        if model_type != 'ocr' and ocr_role:
            raise serializers.ValidationError("只有OCR模型才能指定角色")

        return data