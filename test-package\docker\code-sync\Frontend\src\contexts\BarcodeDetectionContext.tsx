import React, { createContext, useState, useContext, ReactNode, useMemo } from 'react';
import { VisionModel } from '../services/api';

// --- Types ---
export type PreprocessingMethod = 'full_scale' | 'roi'; // 预处理方法示例
export type ModelSelectionType = 'system' | 'custom'; // 模型选择类型

export interface BarcodeDetectionState {
  preprocessingMethod: PreprocessingMethod;
  confidenceThreshold: number; // 0.0 to 1.0
  batchProcessingInterval: number; // 批处理间隔时间(毫秒)
  autoInferenceEnabled: boolean; // 切换图片时是否自动执行检测
  isInferring: boolean; // 单图推理状态
  isBatchProcessing: boolean; // 批量处理状态

  // 模型管理相关状态
  selectedModelName: string; // 当前选择的模型名称
  modelSelectionType: ModelSelectionType; // 模型选择类型：系统模型或自定义模型
  systemBarcodeModels: VisionModel[]; // 系统条码模型列表
  customBarcodeModels: VisionModel[]; // 自定义条码模型列表
  modelsLoading: boolean; // 模型加载状态
  modelsError: string | null; // 模型加载错误信息
}

interface BarcodeDetectionContextType extends BarcodeDetectionState {
  setPreprocessingMethod: (method: PreprocessingMethod) => void;
  setConfidenceThreshold: (threshold: number) => void;
  setBatchProcessingInterval: (interval: number) => void;
  setAutoInferenceEnabled: (enabled: boolean) => void; // 设置切换时智能检测状态
  setIsInferring: (inferring: boolean) => void; // 设置单图推理状态
  setIsBatchProcessing: (processing: boolean) => void; // 设置批量处理状态

  // 模型管理相关方法
  setSelectedModelName: (modelName: string) => void; // 设置选择的模型名称
  setModelSelectionType: (type: ModelSelectionType) => void; // 设置模型选择类型
  setSystemBarcodeModels: (models: VisionModel[]) => void; // 设置系统模型列表
  setCustomBarcodeModels: (models: VisionModel[]) => void; // 设置自定义模型列表
  setModelsLoading: (loading: boolean) => void; // 设置模型加载状态
  setModelsError: (error: string | null) => void; // 设置模型错误信息
  fetchBarcodeModels: (trigger: 'initial' | 'manual') => Promise<void>; // 获取模型列表
}

// --- Context Creation ---
const BarcodeDetectionContext = createContext<BarcodeDetectionContextType | undefined>(undefined);

// --- Provider Component ---
interface BarcodeDetectionProviderProps {
  children: ReactNode;
}

export const BarcodeDetectionProvider: React.FC<BarcodeDetectionProviderProps> = ({ children }) => {
  const [preprocessingMethod, setPreprocessingMethod] = useState<PreprocessingMethod>('full_scale');
  const [confidenceThreshold, setConfidenceThreshold] = useState<number>(0.45); // 默认置信度
  const [batchProcessingInterval, setBatchProcessingInterval] = useState<number>(1000); // 默认间隔1000毫秒（1秒）
  const [autoInferenceEnabled, setAutoInferenceEnabled] = useState<boolean>(false); // 默认不启用切换时智能检测
  const [isInferring, setIsInferring] = useState<boolean>(false); // 默认不在推理中
  const [isBatchProcessing, setIsBatchProcessing] = useState<boolean>(false); // 默认不在批量处理中

  // 模型管理相关状态
  const [selectedModelName, setSelectedModelName] = useState<string>(''); // 当前选择的模型名称
  const [modelSelectionType, setModelSelectionType] = useState<ModelSelectionType>('system'); // 默认选择系统模型
  const [systemBarcodeModels, setSystemBarcodeModels] = useState<VisionModel[]>([]); // 系统条码模型列表
  const [customBarcodeModels, setCustomBarcodeModels] = useState<VisionModel[]>([]); // 自定义条码模型列表
  const [modelsLoading, setModelsLoading] = useState<boolean>(false); // 模型加载状态
  const [modelsError, setModelsError] = useState<string | null>(null); // 模型加载错误信息

  // 获取条码模型列表的函数
  const fetchBarcodeModels = React.useCallback(async (trigger: 'initial' | 'manual') => {
    setModelsLoading(true);
    setModelsError(null);

    // 根据触发类型记录日志
    console.log(`[BarcodeDetectionContext] 开始获取条码模型列表 (触发方式: ${trigger})`);

    try {
      // 动态导入API函数以避免循环依赖
      const { getGroupedVisionModels } = await import('../services/api');

      // 获取系统模型
      const systemModelsResponse = await getGroupedVisionModels('system');
      const systemBarcodeList = systemModelsResponse['barcode'] || [];
      setSystemBarcodeModels(systemBarcodeList);

      // 获取用户模型
      const customModelsResponse = await getGroupedVisionModels('custom');
      const customBarcodeList = customModelsResponse['barcode'] || [];
      setCustomBarcodeModels(customBarcodeList);

      // 设置默认选择的模型
      if (modelSelectionType === 'system' && systemBarcodeList.length > 0) {
        if (!selectedModelName || !systemBarcodeList.some(m => m.name === selectedModelName)) {
          setSelectedModelName(systemBarcodeList[0].name);
        }
      } else if (modelSelectionType === 'custom' && customBarcodeList.length > 0) {
        if (!selectedModelName || !customBarcodeList.some(m => m.name === selectedModelName)) {
          setSelectedModelName(customBarcodeList[0].name);
        }
      }

      console.log(`[BarcodeDetectionContext] 模型获取成功 (${trigger}): 系统模型 ${systemBarcodeList.length} 个, 自定义模型 ${customBarcodeList.length} 个`);

    } catch (error) {
      console.error(`[BarcodeDetectionContext] 获取条码模型失败 (${trigger}):`, error);
      setModelsError('获取模型列表失败，请稍后重试');
      setSystemBarcodeModels([]);
      setCustomBarcodeModels([]);
    } finally {
      setModelsLoading(false);
    }
  }, [modelSelectionType, selectedModelName]);

  const contextValue = useMemo(() => ({
    preprocessingMethod,
    setPreprocessingMethod,
    confidenceThreshold,
    setConfidenceThreshold,
    batchProcessingInterval,
    setBatchProcessingInterval,
    autoInferenceEnabled,
    setAutoInferenceEnabled,
    isInferring,
    setIsInferring,
    isBatchProcessing,
    setIsBatchProcessing,

    // 模型管理相关
    selectedModelName,
    setSelectedModelName,
    modelSelectionType,
    setModelSelectionType,
    systemBarcodeModels,
    setSystemBarcodeModels,
    customBarcodeModels,
    setCustomBarcodeModels,
    modelsLoading,
    setModelsLoading,
    modelsError,
    setModelsError,
    fetchBarcodeModels,
  }), [
    preprocessingMethod,
    confidenceThreshold,
    batchProcessingInterval,
    autoInferenceEnabled,
    isInferring,
    isBatchProcessing,
    selectedModelName,
    modelSelectionType,
    systemBarcodeModels,
    customBarcodeModels,
    modelsLoading,
    modelsError,
    fetchBarcodeModels,
  ]);

  return (
    <BarcodeDetectionContext.Provider value={contextValue}>
      {children}
    </BarcodeDetectionContext.Provider>
  );
};

// --- Hook for consuming context ---
export const useBarcodeDetectionParams = (): BarcodeDetectionContextType => {
  const context = useContext(BarcodeDetectionContext);
  if (context === undefined) {
    throw new Error('useBarcodeDetectionParams must be used within a BarcodeDetectionProvider');
  }
  return context;
};