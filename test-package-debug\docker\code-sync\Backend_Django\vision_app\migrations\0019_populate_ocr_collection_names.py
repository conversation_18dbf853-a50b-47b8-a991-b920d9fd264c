# Generated by Django 5.2.1 on 2025-06-05 08:00

from django.db import migrations
from django.utils import timezone

def populate_ocr_collection_names(apps, schema_editor):
    """
    为现有的OCR模型设置collection_name，将detection和recognition模型配对
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    # 获取所有OCR模型
    ocr_models = AIModel.objects.using(db_alias).filter(model_type='ocr')
    
    # 按name分组，为每个组设置相同的collection_name
    ocr_groups = {}
    for model in ocr_models:
        if model.name not in ocr_groups:
            ocr_groups[model.name] = []
        ocr_groups[model.name].append(model)
    
    # 为每个组设置collection_name
    for task_name, models in ocr_groups.items():
        # 使用task_name作为collection_name的基础
        collection_name = task_name
        
        # 如果有detection模型且有描述，优先使用detection模型的描述作为collection_name
        detection_model = None
        for model in models:
            if model.ocr_role == 'detection':
                detection_model = model
                break
        
        if detection_model and detection_model.description:
            collection_name = detection_model.description
        
        # 更新所有相关模型的collection_name
        for model in models:
            model.ocr_collection_name = collection_name
            model.save(using=db_alias)
            print(f"Updated OCR model {model.name} ({model.ocr_role}) with collection_name: {collection_name}")

def reverse_populate_ocr_collection_names(apps, schema_editor):
    """
    回滚操作：清除所有OCR模型的collection_name
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    # 清除所有OCR模型的collection_name
    AIModel.objects.using(db_alias).filter(model_type='ocr').update(ocr_collection_name=None)

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0018_add_ocr_collection_name'),
    ]

    operations = [
        migrations.RunPython(
            populate_ocr_collection_names,
            reverse_populate_ocr_collection_names,
        ),
    ]
