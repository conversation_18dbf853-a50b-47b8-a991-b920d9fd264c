# 前端环境变量配置示例
# 复制此文件为 .env.local 并根据实际情况修改

# =============================================================================
# API配置
# =============================================================================

# API基础URL配置
VITE_API_BASE_URL=/api

# =============================================================================
# 网络配置
# =============================================================================

# 后端配置
VITE_BACKEND_HOST=localhost
VITE_BACKEND_PORT=9000
VITE_BACKEND_URL=http://localhost:9000

# 前端配置
VITE_FRONTEND_HOST=localhost
VITE_FRONTEND_PORT=5173

# 环境配置
VITE_ENVIRONMENT=development
VITE_USE_HTTPS=false

# =============================================================================
# 使用说明
# =============================================================================

# 1. 本地开发 (默认)
#    - 前端: http://localhost:5173
#    - 后端: http://localhost:9000
#    - 使用 .env.development

# 2. 局域网访问
#    - 设置 VITE_BACKEND_HOST 和 VITE_FRONTEND_HOST 为局域网IP
#    - 例如: VITE_BACKEND_HOST=*************

# 3. Docker热重载
#    - 使用 .env.hotreload
#    - 前端通过nginx代理访问后端

# 4. 生产部署
#    - 使用 .env.production
#    - 前端静态文件部署

# 5. 自定义端口
#    - 修改 VITE_BACKEND_PORT 和 VITE_FRONTEND_PORT
#    - 确保与后端配置一致

# =============================================================================
# 环境变量优先级
# =============================================================================

# 1. 命令行环境变量 (最高)
# 2. .env.local 文件
# 3. .env.[mode] 文件 (如 .env.development)
# 4. .env 文件
# 5. 默认值 (最低)
