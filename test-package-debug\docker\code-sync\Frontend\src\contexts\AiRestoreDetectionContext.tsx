import React, { createContext, useState, useContext, ReactNode, useMemo } from 'react';
import { VisionModel } from '../services/api'; // Correctly import VisionModel

// --- Types ---

// AiRestoreModel is now VisionModel imported from api.ts
// No local AiRestoreModel interface needed if VisionModel is sufficient.
// VisionModel typically has: id, name, model_type, version, description, file_path, is_system_model, uploaded_at
// For the context, 'name' from VisionModel will likely be used as 'selectedModelValue'.

export interface AiRestoreResult {
  restoredImageBase64: string;
  // Add other relevant properties from the API response if any
}

// Added for consistency, even if not immediately used in the panel
export type AiRestoreDisplayMode = 'restored_only' | 'composite';

export interface AiRestoreDetectionState {
  selectedModelValue: string; // Stores the 'name' of the selected VisionModel
  aiRestoreProcessingResults: AiRestoreResult | null;
  selectedDisplayMode: AiRestoreDisplayMode;
  batchProcessingInterval: number; // Added for future consistency
  availableAiRestoreModels: VisionModel[]; // Use VisionModel[]
  aiRestoreModelsLoading: boolean;
  aiRestoreModelsError: string | null;
  autoInferenceEnabled: boolean; // <--- KEPT: State for auto inference on switch
  isInferring: boolean; // 单图推理状态
  isBatchProcessing: boolean; // 批量处理状态
}

interface AiRestoreDetectionContextType extends AiRestoreDetectionState {
  setSelectedModelValue: (value: string) => void;
  setAiRestoreProcessingResults: (results: AiRestoreResult | null) => void;
  setSelectedDisplayMode: (mode: AiRestoreDisplayMode) => void;
  setBatchProcessingInterval: (interval: number) => void; // Added
  setAvailableAiRestoreModels: (models: VisionModel[]) => void; // Use VisionModel[]
  setAiRestoreModelsLoading: (loading: boolean) => void;
  setAiRestoreModelsError: (error: string | null) => void;
  setAutoInferenceEnabled: (enabled: boolean) => void; // <--- KEPT: Setter for auto inference
  setIsInferring: (inferring: boolean) => void; // 设置单图推理状态
  setIsBatchProcessing: (processing: boolean) => void; // 设置批量处理状态
}

// --- Context Creation ---
const AiRestoreDetectionContext = createContext<AiRestoreDetectionContextType | undefined>(undefined);

// --- Provider Component ---
interface AiRestoreDetectionProviderProps {
  children: ReactNode;
}

export const AiRestoreDetectionProvider: React.FC<AiRestoreDetectionProviderProps> = ({ children }) => {
  const [selectedModelValueState, setSelectedModelValueState] = useState<string>('');
  const [aiRestoreProcessingResultsState, setAiRestoreProcessingResultsState] = useState<AiRestoreResult | null>(null);
  const [selectedDisplayModeState, setSelectedDisplayModeState] = useState<AiRestoreDisplayMode>('composite');
  const [batchProcessingIntervalState, setBatchProcessingIntervalState] = useState<number>(1000); // Added
  const [availableAiRestoreModelsState, setAvailableAiRestoreModelsState] = useState<VisionModel[]>([]); // Use VisionModel[]
  const [aiRestoreModelsLoadingState, setAiRestoreModelsLoadingState] = useState<boolean>(false);
  const [aiRestoreModelsErrorState, setAiRestoreModelsErrorState] = useState<string | null>(null);
  const [autoInferenceEnabledState, setAutoInferenceEnabledStateActual] = useState<boolean>(false); // <--- KEPT: Actual state variable
  const [isInferringState, setIsInferringState] = useState<boolean>(false); // 默认不在推理中
  const [isBatchProcessingState, setIsBatchProcessingState] = useState<boolean>(false); // 默认不在批量处理中

  const setSelectedModelValue = (value: string) => setSelectedModelValueState(value);
  const setAiRestoreProcessingResults = (results: AiRestoreResult | null) => setAiRestoreProcessingResultsState(results);
  const setSelectedDisplayMode = (mode: AiRestoreDisplayMode) => setSelectedDisplayModeState(mode);
  const setBatchProcessingInterval = (interval: number) => setBatchProcessingIntervalState(interval);
  const setAvailableAiRestoreModels = (models: VisionModel[]) => setAvailableAiRestoreModelsState(models); // Use VisionModel[]
  const setAiRestoreModelsLoading = (loading: boolean) => setAiRestoreModelsLoadingState(loading);
  const setAiRestoreModelsError = (error: string | null) => setAiRestoreModelsErrorState(error);
  const setAutoInferenceEnabled = (enabled: boolean) => setAutoInferenceEnabledStateActual(enabled); // <--- KEPT: Actual setter function
  const setIsInferring = (inferring: boolean) => setIsInferringState(inferring); // 设置单图推理状态
  const setIsBatchProcessing = (processing: boolean) => setIsBatchProcessingState(processing); // 设置批量处理状态

  const contextValue = useMemo(() => ({
    selectedModelValue: selectedModelValueState,
    setSelectedModelValue,
    aiRestoreProcessingResults: aiRestoreProcessingResultsState,
    setAiRestoreProcessingResults,
    selectedDisplayMode: selectedDisplayModeState,
    setSelectedDisplayMode,
    batchProcessingInterval: batchProcessingIntervalState,
    setBatchProcessingInterval,
    autoInferenceEnabled: autoInferenceEnabledState, // Use the new state variable
    setAutoInferenceEnabled, // Use the new setter
    isInferring: isInferringState, // 单图推理状态
    setIsInferring, // 设置单图推理状态
    isBatchProcessing: isBatchProcessingState, // 批量处理状态
    setIsBatchProcessing, // 设置批量处理状态
    availableAiRestoreModels: availableAiRestoreModelsState,
    setAvailableAiRestoreModels,
    aiRestoreModelsLoading: aiRestoreModelsLoadingState,
    setAiRestoreModelsLoading,
    aiRestoreModelsError: aiRestoreModelsErrorState,
    setAiRestoreModelsError,
  }), [
    selectedModelValueState,
    aiRestoreProcessingResultsState,
    selectedDisplayModeState,
    batchProcessingIntervalState,
    autoInferenceEnabledState, // Add to dependency array
    isInferringState, // Add to dependency array
    isBatchProcessingState, // Add to dependency array
    availableAiRestoreModelsState,
    aiRestoreModelsLoadingState,
    aiRestoreModelsErrorState,
  ]);

  return (
    <AiRestoreDetectionContext.Provider value={contextValue}>
      {children}
    </AiRestoreDetectionContext.Provider>
  );
};

// --- Hook for consuming context ---
export const useAiRestoreDetectionParams = (): AiRestoreDetectionContextType => {
  const context = useContext(AiRestoreDetectionContext);
  if (context === undefined) {
    throw new Error('useAiRestoreDetectionParams must be used within an AiRestoreDetectionProvider');
  }
  return context;
};