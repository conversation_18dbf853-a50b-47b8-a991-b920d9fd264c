import os
import sys
import cv2
import copy
import numpy as np
import time
import logging
from PIL import Image # Keep PIL for potential image manipulations if needed by draw_ocr_box_txt

# --- BEGIN: Adjust imports to reference the sub-module's tools correctly ---
# This assumes 'libs/proj_ai_ocr_license_plate' is structured such that 'tools' is a package within it.
# We need to make sure Python's import mechanism can find these.
# One way is to temporarily add the 'libs/proj_ai_ocr_license_plate' directory to sys.path
# OR ensure that 'proj_ai_ocr_license_plate' itself is a package and can be imported from.

# Assuming 'Backend_Django' is the project root and in PYTHONPATH, or the app is structured correctly
# This might need adjustment based on your exact Python path setup when running Django.
# For now, let's assume a helper function might adjust sys.path or these are directly importable.

# Option 1: Relative imports if this file is considered part of the submodule (less likely for Django app)
# from ..libs.proj_ai_ocr_license_plate.tools.infer import utility as ocr_utility
# from ..libs.proj_ai_ocr_license_plate.tools.infer import predict_rec as ocr_predict_rec
# from ..libs.proj_ai_ocr_license_plate.tools.infer import predict_det as ocr_predict_det
# from ..libs.proj_ai_ocr_license_plate.tools.infer import predict_cls as ocr_predict_cls
# from ..libs.proj_ai_ocr_license_plate.ppocr.utils.utility import check_and_read
# from ..libs.proj_ai_ocr_license_plate.ppocr.utils.logging import get_logger
# from ..libs.proj_ai_ocr_license_plate.tools.infer.utility import draw_ocr_box_txt, get_rotate_crop_image, get_minarea_rect_crop

# Option 2: Add 'libs/proj_ai_ocr_license_plate' to sys.path before these imports
# This should ideally be done once at a higher level if possible, or carefully managed.
# For direct inclusion here (less ideal but functional for a single predictor file):
import sys
from django.conf import settings # To get BASE_DIR

# Path to the proj_ai_ocr_license_plate library
# Assuming 'libs' is directly under BASE_DIR (Backend_Django)
# And 'proj_ai_ocr_license_plate' is under 'libs'
paddle_ocr_lib_path = os.path.join(settings.BASE_DIR, 'libs', 'proj_ai_ocr_license_plate')

# Temporarily add the library and its 'tools' directory to sys.path
# This is a common way to handle submodules or vendored libraries not in the standard path
# Ensure these paths are correct relative to your Django project structure
original_sys_path = list(sys.path) # Save original path
if paddle_ocr_lib_path not in sys.path:
    sys.path.insert(0, paddle_ocr_lib_path)

# Now try to import from the submodule's structure
try:
    from tools.infer import utility as ocr_utility
    from tools.infer import predict_rec as ocr_predict_rec
    from tools.infer import predict_det as ocr_predict_det
    from tools.infer import predict_cls as ocr_predict_cls
    from ppocr.utils.utility import get_image_file_list, check_and_read
    from ppocr.utils.logging import get_logger # Re-use or get a new logger
    from tools.infer.utility import (
        draw_ocr_box_txt, # For visualization, maybe optional in API
        get_rotate_crop_image,
        get_minarea_rect_crop,
        # slice_generator, merge_fragmented # If slicing is needed
    )
except ImportError as e:
    # Restore sys.path if imports fail, to avoid side effects
    sys.path = original_sys_path
    raise ImportError(f"Could not import from PaddleOCR submodule. Check path: {paddle_ocr_lib_path}. Error: {e}")
finally:
    # It's generally better to manage sys.path changes carefully.
    # For a Django app, this path manipulation might be better handled in __init__.py or apps.py
    # For now, we'll restore it here to keep this module self-contained for import testing.
    # However, for the lifetime of the predictor instance, these paths need to be available.
    # So, we might not restore it immediately if the predictor is long-lived.
    # For now, let's assume the path addition is for the scope of these imports.
    # If predictor class instances are held, the path should remain added or managed.
    # To be safe for Django, let's assume it's added and then removed after module load if not needed by instances.
    # Actually, the predictor instances (TextDetector etc.) will need these paths.
    # So, the sys.path modification should persist as long as these classes are used.
    # A cleaner way is to make proj_ai_ocr_license_plate a proper package.
    pass # Keep sys.path modified for now, assuming TextDetector etc. might need it at runtime

# --- END: Adjust imports ---

# It's good practice to use Django's logging system or configure a logger specifically for this module
# For simplicity, reusing the OCR's logger getter but could be Django's logger.
logger = get_logger() # Or use logging.getLogger(__name__) and configure it.

class PaddleOCRSystemPredictor:
    def __init__(self, det_model_dir, rec_model_dir, use_gpu=False,
                 rec_image_shape="3,48,320", # Default from PP-OCRv3
                 use_angle_cls=False,
                 cls_model_dir=None, # Required if use_angle_cls is True
                 drop_score=0.5,
                 # IMPORTANT: rec_char_dict_path must now be provided, ideally from settings
                 rec_char_dict_path=None, # Expect this to be provided in kwargs
                 # Add other necessary params from args based on run_infer.ps1 or TextSystem defaults
                 det_limit_side_len=960, # Default from utility.parse_args()
                 det_limit_type='max',   # Default
                 det_db_thresh=0.3,      # Default
                 det_db_box_thresh=0.6,  # Default
                 **kwargs): # For any other args the underlying classes might need

        # Create a lightweight "args" object or Namespace to pass to the original classes
        # This mimics the 'args' object that TextSystem and its components expect.
        args_dict = {
            "det_model_dir": det_model_dir,
            "rec_model_dir": rec_model_dir,
            "use_gpu": use_gpu,
            "rec_image_shape": rec_image_shape,
            "use_angle_cls": use_angle_cls,
            "cls_model_dir": cls_model_dir,
            "drop_score": drop_score,
            
            "rec_char_dict_path": rec_char_dict_path, # Use the provided dictionary path
            
            "det_limit_side_len": det_limit_side_len,
            "det_limit_type": det_limit_type,
            "det_db_thresh": det_db_thresh,
            "det_db_box_thresh": det_db_box_thresh,
            "det_box_type": "quad", # Default from utility.parse_args(), assumes 'quad' is generally used.
            
            # Defaults for other params usually set by utility.parse_args()
            # These might need to be exposed as __init__ params if they need to be configurable per instance
            "enable_mkldnn": False, # utility.parse_args() default
            "cpu_threads": 10,      # utility.parse_args() default
            "precision": "fp32",    # utility.parse_args() default
            "benchmark": False,     # utility.parse_args() default
            "save_log_path": "./log_output/", # utility.parse_args() default
            "show_log": True,       # utility.parse_args() default (can be overridden by Django logging)
            "use_tensorrt": False,  # utility.parse_args() default
            "gpu_id": 0,            # utility.parse_args() default
            "rec_batch_num": 6,     # utility.parse_args() default
            "use_space_char": True, # utility.parse_args() default
            "vis_font_path": os.path.join(paddle_ocr_lib_path, "doc/fonts/simfang.ttf"), # For drawing, if used
            "crop_res_save_dir": "./output", # If saving cropped images is ever needed
            "save_crop_res": False, # Default to False, as we usually just want results
            "warmup": False, # No warmup needed for API calls typically
            
            # Add any other args from utility.parse_args() that TextDetector, TextRecognizer, TextClassifier might need
            # For example, from predict_det.TextDetector:
            "det_db_unclip_ratio": 1.5, # default
            "det_db_score_mode": "slow", # default
            "use_dilation": False, # default

            # For TextRecognizer:
            "rec_algorithm": "SVTR_LCNet", # default in PP-OCRv3/v4, but check if your model matches
            
            # For TextClassifier:
            "cls_image_shape": "3, 48, 192", # default
            "label_list": ['0', '180'], # default
            "cls_batch_num": 6, # default
            "cls_thresh": 0.9, # default
        }
        # Allow overriding any of these via **kwargs
        args_dict.update(kwargs)

        # Convert dict to a Namespace or a simple class for attribute access
        class ArgsNamespace:
            def __init__(self, **entries):
                self.__dict__.update(entries)

        self.args_ns = ArgsNamespace(**args_dict)

        # Validate that rec_char_dict_path was provided
        if not self.args_ns.rec_char_dict_path:
             raise ValueError("rec_char_dict_path must be provided when initializing PaddleOCRSystemPredictor.")
        if not os.path.exists(self.args_ns.rec_char_dict_path):
             raise FileNotFoundError(f"Recognizer dictionary file not found at path: {self.args_ns.rec_char_dict_path}")

        if not self.args_ns.show_log:
            logger.setLevel(logging.INFO) # Match original TextSystem log level setting

        # Initialize the underlying PaddleOCR components
        self.text_detector = ocr_predict_det.TextDetector(self.args_ns)
        self.text_recognizer = ocr_predict_rec.TextRecognizer(self.args_ns) # TextRecognizer will load the dict from self.args_ns.rec_char_dict_path
        self.use_angle_cls = self.args_ns.use_angle_cls
        self.drop_score = self.args_ns.drop_score
        if self.use_angle_cls:
            if not self.args_ns.cls_model_dir:
                raise ValueError("cls_model_dir must be provided if use_angle_cls is True.")
            self.text_classifier = ocr_predict_cls.TextClassifier(self.args_ns)
        
        # self.crop_image_res_index = 0 # Not needed if not saving crop images

        # Perform a warmup if specified (e.g., during app startup if an instance is pre-loaded)
        if self.args_ns.warmup:
            self._warmup()
            
    def _warmup(self):
        logger.info("Performing warmup for PaddleOCRSystemPredictor...")
        # Create a dummy image for warmup
        # Dimensions can be typical, e.g., 640x640 or based on expected input
        dummy_image = np.random.uniform(0, 255, [640, 640, 3]).astype(np.uint8)
        try:
            for _ in range(2): # Warm up a few times
                self.predict(dummy_image, perform_angle_cls=self.use_angle_cls)
            logger.info("Warmup completed.")
        except Exception as e:
            logger.error(f"Error during warmup: {e}", exc_info=True)


    # Keep the sorted_boxes function as it's used by the __call__ logic
    def _sorted_boxes(self, dt_boxes):
        """
        Sort text boxes in order from top to bottom, left to right
        args:
            dt_boxes(array):detected text boxes with shape [N, 4, 2]
        return:
            sorted boxes(array) with shape [N, 4, 2]
        """
        num_boxes = dt_boxes.shape[0]
        # Ensure it's a list of arrays for sorting, each array being a box [4,2]
        # The original sort key (x[0][1], x[0][0]) implies each element x is a box,
        # and x[0] is the top-left point [x_coord, y_coord].
        sorted_boxes_list = sorted(dt_boxes, key=lambda x: (x[0][1], x[0][0]))
        
        # The rest of the sorting logic to handle nearly aligned boxes
        # This part seems to refine sorting for boxes on almost the same horizontal line.
        _boxes = list(sorted_boxes_list) # Work with a mutable list copy

        for i in range(num_boxes - 1):
            # Iterate backwards from i to 0 for bubble-sort like adjustments
            for j in range(i, -1, -1): 
                # If box j+1 is "close" vertically to box j, and box j+1 is to the left of box j
                if abs(_boxes[j + 1][0][1] - _boxes[j][0][1]) < 10 and \
                   _boxes[j + 1][0][0] < _boxes[j][0][0]:
                    # Swap them
                    tmp = _boxes[j]
                    _boxes[j] = _boxes[j + 1]
                    _boxes[j + 1] = tmp
                else:
                    # If conditions not met, inner loop for this i is done
                    break
        return np.array(_boxes) if _boxes else np.array([])


    def predict(self, image_input, perform_angle_cls=None):
        """
        Performs OCR on the given image.
        Args:
            image_input: Can be a file path (str) or a NumPy array (OpenCV BGR format).
            perform_angle_cls (bool, optional): Override for using angle classification. 
                                               If None, uses the instance's self.use_angle_cls.
        Returns:
            tuple: (filter_boxes, filter_rec_res, time_dict)
                   filter_boxes: List of ndarray, each representing a detected text box [4, 2].
                   filter_rec_res: List of tuples, each (text_string, confidence_score).
                   time_dict: Dictionary 성능 측정 시간.
        """
        img = None
        if isinstance(image_input, str): # If input is a path
            try:
                # Read file as bytes and decode with OpenCV
                with open(image_input, 'rb') as f:
                    img_bytes = f.read()
                if not img_bytes:
                    logger.error(f"File at path {image_input} is empty or could not be read as bytes.")
                    return None, None, {"det": 0, "rec": 0, "cls": 0, "all": 0}
                
                np_arr = np.frombuffer(img_bytes, np.uint8)
                img = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
                
                if img is None:
                    logger.error(f"cv2.imdecode failed for image at path: {image_input}. The file might be corrupted or not a supported image format by OpenCV.")
                    # As a fallback, try PIL if cv2.imdecode fails (though less likely if bytes were read)
                    try:
                        from PIL import Image
                        pil_img = Image.open(image_input).convert('RGB')
                        img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
                        if img is not None:
                             logger.info(f"Successfully loaded image {image_input} using PIL fallback.")
                        else:
                             logger.error(f"PIL also failed to load image: {image_input}")
                    except Exception as e_pil:
                        logger.error(f"Error loading image {image_input} with PIL fallback: {e_pil}")
                        img = None # Ensure img is None
            except FileNotFoundError:
                logger.error(f"Image file not found at path: {image_input}")
                return None, None, {"det": 0, "rec": 0, "cls": 0, "all": 0}
            except Exception as e:
                logger.error(f"Error reading image file {image_input}: {e}", exc_info=True)
                return None, None, {"det": 0, "rec": 0, "cls": 0, "all": 0}

        elif isinstance(image_input, np.ndarray):
            img = image_input # Assume BGR format if NumPy array
            logger.debug("Processing image provided as NumPy array.")
        else:
            logger.error("Invalid image_input type. Must be a file path (str) or NumPy array.")
            return None, None, {"det": 0, "rec": 0, "cls": 0, "all": 0}

        if img is None: # This check is now more critical after attempting to load
            logger.error(f"Failed to load image from input: {image_input}. Cannot proceed with prediction.")
            return [], [], {"det": 0, "rec": 0, "cls": 0, "all": 0} # Return empty lists for consistency

        # Determine if to use angle classification for this call
        use_cls_current_call = self.use_angle_cls if perform_angle_cls is None else perform_angle_cls
        
        time_dict = {"det": 0, "rec": 0, "cls": 0, "all": 0}
        start_time = time.time()

        ori_im = img.copy()
        
        # --- Text Detection ---
        dt_boxes, det_elapse = self.text_detector(img) # Assuming text_detector takes cv2 image
        time_dict["det"] = det_elapse

        if dt_boxes is None or dt_boxes.size == 0:
            logger.debug(f"No text boxes found. Detection elapsed: {det_elapse:.4f}s")
            time_dict["all"] = time.time() - start_time
            return [], [], time_dict # Return empty lists for consistency
        
        logger.debug(f"Detected {len(dt_boxes)} text boxes. Detection elapsed: {det_elapse:.4f}s")
        
        # --- Crop images from detected boxes ---
        img_crop_list = []
        # Ensure dt_boxes is correctly sorted before cropping
        # The original TextSystem.__call__ sorts dt_boxes.
        dt_boxes_sorted = self._sorted_boxes(dt_boxes)

        for bno in range(len(dt_boxes_sorted)):
            tmp_box = copy.deepcopy(dt_boxes_sorted[bno])
            if self.args_ns.det_box_type == "quad": # 'quad' means points are [tl, tr, br, bl]
                img_crop = get_rotate_crop_image(ori_im, tmp_box)
            else: # 'minAreaRect'
                img_crop = get_minarea_rect_crop(ori_im, tmp_box)
            img_crop_list.append(img_crop)

        # --- Angle Classification (Optional) ---
        if use_cls_current_call:
            if not hasattr(self, 'text_classifier'):
                logger.warning("Angle classification (use_angle_cls) is True, but TextClassifier was not initialized (e.g. missing cls_model_dir). Skipping classification.")
            else:
                img_crop_list, angle_list, cls_elapse = self.text_classifier(img_crop_list)
                time_dict["cls"] = cls_elapse
                logger.debug(f"Classified {len(img_crop_list)} text regions. Classification elapsed: {cls_elapse:.4f}s")
        
        if not img_crop_list: # If all crops were filtered out or none detected
            logger.debug("No image crops to recognize after detection/classification.")
            time_dict["all"] = time.time() - start_time
            return [], [], time_dict

        # --- Text Recognition ---
        if len(img_crop_list) > 1000 : # Match original warning
            logger.debug(f"Recognition will process {len(img_crop_list)} crops. Time and memory cost may be large.")

        rec_res, rec_elapse = self.text_recognizer(img_crop_list)
        time_dict["rec"] = rec_elapse
        logger.debug(f"Recognized {len(rec_res)} text regions. Recognition elapsed: {rec_elapse:.4f}s")

        # --- Filter results by drop_score ---
        filter_boxes_final = []
        filter_rec_res_final = []
        for i in range(len(rec_res)):
            box = dt_boxes_sorted[i] # Use the sorted boxes corresponding to rec_res
            text, score = rec_res[i] # rec_res elements are (text, score)
            if score >= self.drop_score:
                filter_boxes_final.append(box)
                filter_rec_res_final.append(rec_res[i]) # Keep as (text, score)
        
        # Construct the 'all' part of time_dict to be a dictionary
        total_elapsed_time = time.time() - start_time
        time_dict["all"] = {
            'total': total_elapsed_time,
            'total_excluding_nms_crop': time_dict.get("det", 0) + time_dict.get("rec", 0) # Assuming NMS/crop is part of det/rec or not separately timed here
        }
        if use_cls_current_call and "cls" in time_dict: # If classification was done
             time_dict["all"]['total_excluding_cls_etc'] = time_dict.get("det", 0) + time_dict.get("rec", 0) # Or more precise if cls is separate

        logger.debug(f"Total prediction time: {total_elapsed_time:.4f}s")
        
        return filter_boxes_final, filter_rec_res_final, time_dict
