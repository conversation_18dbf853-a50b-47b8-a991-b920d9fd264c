/**
 * 前端网络配置管理模块
 * 
 * 统一管理前端的网络相关配置，包括API地址、端口等。
 * 支持环境变量覆盖和动态配置。
 */

// 环境变量接口
interface EnvironmentConfig {
  VITE_API_BASE_URL?: string;
  VITE_BACKEND_URL?: string;
  VITE_BACKEND_HOST?: string;
  VITE_BACKEND_PORT?: string;
  VITE_FRONTEND_HOST?: string;
  VITE_FRONTEND_PORT?: string;
  VITE_USE_HTTPS?: string;
  VITE_ENVIRONMENT?: string;
}

// 网络配置接口
interface NetworkConfig {
  apiBaseUrl: string;
  backendUrl: string;
  backendHost: string;
  backendPort: number;
  frontendHost: string;
  frontendPort: number;
  useHttps: boolean;
  environment: string;
  wsUrl: string;
}

// 后端配置接口（从后端API获取）
interface BackendNetworkInfo {
  backend_urls?: {
    development?: string;
    production?: string;
    docker?: string;
  };
  frontend_urls?: {
    development?: string;
    production?: string;
    docker?: string;
  };
  ports?: {
    backend?: { django?: number };
    frontend?: { dev_server?: number; production?: number };
  };
}

class NetworkConfigManager {
  private static instance: NetworkConfigManager;
  private config: NetworkConfig | null = null;
  private backendConfig: BackendNetworkInfo | null = null;
  private configPromise: Promise<NetworkConfig> | null = null;

  private constructor() {}

  static getInstance(): NetworkConfigManager {
    if (!NetworkConfigManager.instance) {
      NetworkConfigManager.instance = new NetworkConfigManager();
    }
    return NetworkConfigManager.instance;
  }

  /**
   * 获取环境变量配置
   */
  private getEnvConfig(): EnvironmentConfig {
    return {
      VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
      VITE_BACKEND_URL: import.meta.env.VITE_BACKEND_URL,
      VITE_BACKEND_HOST: import.meta.env.VITE_BACKEND_HOST,
      VITE_BACKEND_PORT: import.meta.env.VITE_BACKEND_PORT,
      VITE_FRONTEND_HOST: import.meta.env.VITE_FRONTEND_HOST,
      VITE_FRONTEND_PORT: import.meta.env.VITE_FRONTEND_PORT,
      VITE_USE_HTTPS: import.meta.env.VITE_USE_HTTPS,
      VITE_ENVIRONMENT: import.meta.env.VITE_ENVIRONMENT,
    };
  }

  /**
   * 从后端获取网络配置信息
   */
  private async fetchBackendConfig(): Promise<BackendNetworkInfo> {
    try {
      // 尝试从后端获取网络配置信息
      const response = await fetch('/api/vision/config/network-info/', {
        method: 'GET',
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 从后端获取网络配置成功:', data);
        return data;
      } else {
        console.warn('⚠️ 无法从后端获取网络配置，使用默认配置');
        return {};
      }
    } catch (error) {
      console.warn('⚠️ 获取后端网络配置失败，使用默认配置:', error);
      return {};
    }
  }

  /**
   * 检测当前环境
   */
  private detectEnvironment(): string {
    const envConfig = this.getEnvConfig();
    
    // 优先使用环境变量指定的环境
    if (envConfig.VITE_ENVIRONMENT) {
      return envConfig.VITE_ENVIRONMENT;
    }

    // 根据当前URL检测环境
    const hostname = window.location.hostname;
    const port = window.location.port;

    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      if (port === '5173' || port === '4173') {
        return 'development';
      } else if (port === '8080') {
        return 'docker';
      }
    }

    return 'production';
  }

  /**
   * 生成默认配置
   */
  private generateDefaultConfig(): NetworkConfig {
    const envConfig = this.getEnvConfig();
    const environment = this.detectEnvironment();
    const useHttps = envConfig.VITE_USE_HTTPS?.toLowerCase() === 'true';
    const protocol = useHttps ? 'https' : 'http';
    const wsProtocol = useHttps ? 'wss' : 'ws';

    // 默认端口配置
    let backendPort = 9000; // 与后端配置保持一致
    let frontendPort = environment === 'development' ? 5173 : 8080;
    let backendHost = 'localhost';
    let frontendHost = 'localhost';

    // 环境变量覆盖
    if (envConfig.VITE_BACKEND_PORT) {
      backendPort = parseInt(envConfig.VITE_BACKEND_PORT, 10);
    }
    if (envConfig.VITE_FRONTEND_PORT) {
      frontendPort = parseInt(envConfig.VITE_FRONTEND_PORT, 10);
    }
    if (envConfig.VITE_BACKEND_HOST) {
      backendHost = envConfig.VITE_BACKEND_HOST;
    }
    if (envConfig.VITE_FRONTEND_HOST) {
      frontendHost = envConfig.VITE_FRONTEND_HOST;
    }

    // 从后端配置获取端口信息
    if (this.backendConfig?.ports) {
      if (this.backendConfig.ports.backend?.django) {
        backendPort = this.backendConfig.ports.backend.django;
      }
      if (environment === 'development' && this.backendConfig.ports.frontend?.dev_server) {
        frontendPort = this.backendConfig.ports.frontend.dev_server;
      } else if (this.backendConfig.ports.frontend?.production) {
        frontendPort = this.backendConfig.ports.frontend.production;
      }
    }

    const backendUrl = envConfig.VITE_BACKEND_URL || `${protocol}://${backendHost}:${backendPort}`;

    // API基础URL配置
    let apiBaseUrl = envConfig.VITE_API_BASE_URL;
    if (!apiBaseUrl) {
      if (environment === 'development') {
        // 开发环境使用相对路径，通过Vite代理
        apiBaseUrl = '/api';
      } else {
        // 生产环境直接访问后端
        apiBaseUrl = `${backendUrl}/api`;
      }
    }

    return {
      apiBaseUrl,
      backendUrl,
      backendHost,
      backendPort,
      frontendHost,
      frontendPort,
      useHttps,
      environment,
      wsUrl: `${wsProtocol}://${backendHost}:${backendPort}/ws/scanner/`,
    };
  }

  /**
   * 初始化配置
   */
  private async initializeConfig(): Promise<NetworkConfig> {
    try {
      // 获取后端配置
      this.backendConfig = await this.fetchBackendConfig();
      
      // 生成最终配置
      this.config = this.generateDefaultConfig();
      
      console.log('✅ 前端网络配置初始化完成:', this.config);
      return this.config;
    } catch (error) {
      console.error('❌ 前端网络配置初始化失败:', error);
      // 使用默认配置
      this.config = this.generateDefaultConfig();
      return this.config;
    }
  }

  /**
   * 获取网络配置（异步）
   */
  async getConfig(): Promise<NetworkConfig> {
    if (this.config) {
      return this.config;
    }

    if (!this.configPromise) {
      this.configPromise = this.initializeConfig();
    }

    return this.configPromise;
  }

  /**
   * 获取网络配置（同步，可能返回默认值）
   */
  getConfigSync(): NetworkConfig {
    if (this.config) {
      return this.config;
    }

    // 返回基于环境变量的默认配置
    return this.generateDefaultConfig();
  }

  /**
   * 重新加载配置
   */
  async reloadConfig(): Promise<NetworkConfig> {
    this.config = null;
    this.configPromise = null;
    return this.getConfig();
  }

  /**
   * 验证配置
   */
  validateConfig(config: NetworkConfig): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!config.apiBaseUrl) {
      issues.push('API基础URL未配置');
    }

    if (!config.backendUrl) {
      issues.push('后端URL未配置');
    }

    if (config.backendPort < 1 || config.backendPort > 65535) {
      issues.push('后端端口号无效');
    }

    if (config.frontendPort < 1 || config.frontendPort > 65535) {
      issues.push('前端端口号无效');
    }

    return {
      valid: issues.length === 0,
      issues,
    };
  }

  /**
   * 获取配置信息摘要
   */
  getConfigSummary(): string {
    const config = this.getConfigSync();
    return `Environment: ${config.environment}, Backend: ${config.backendUrl}, Frontend: ${config.frontendHost}:${config.frontendPort}`;
  }
}

// 导出单例实例
export const networkConfig = NetworkConfigManager.getInstance();

// 导出类型
export type { NetworkConfig, BackendNetworkInfo };

// 导出便捷函数
export const getNetworkConfig = () => networkConfig.getConfig();
export const getNetworkConfigSync = () => networkConfig.getConfigSync();
export const reloadNetworkConfig = () => networkConfig.reloadConfig();
