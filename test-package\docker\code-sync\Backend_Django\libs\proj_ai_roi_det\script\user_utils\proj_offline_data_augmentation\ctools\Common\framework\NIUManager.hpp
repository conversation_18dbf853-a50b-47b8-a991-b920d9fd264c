#ifndef _NIU_MANAGER_H
#define _NIU_MANAGER_H

//-----------------------------------------------------------------------------
//  Includes

#include <memory>
#include <unordered_set>

#include "autoconf.h"
#include "NIU.hpp"
#include "ModelLoader.hpp"

#ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_SSC377
#include "AIEngineSSC377.hpp"
#endif /* #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_SSC377 */

#ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_MNN
#include "AIEngineMNN.hpp"
#endif /* #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_SSC377 */

#ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_PADDLE_LITE
#include "AIEnginePaddleLite.hpp"
#endif /* #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_PADDLE_LITE */
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

/**
 * @brief    
 *           NIU管理器基类
 *           
 * @date     2024-03-04 Created by HuangJP
 */
class BaseNIUManager
{
public:
    /**
     * @brief    
     *           获取空闲NIU
     *           
     * @retval   指向空闲NIU的共享指针，若无空闲NIU，则返回空指针
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    std::shared_ptr<BaseNIU> Get_Idle(void)
    {
        std::shared_ptr<BaseNIU> niu = nullptr;

        Mutex_Lock(); // 互斥锁上锁

        // 判断是否有空闲NIU
        if (!idle.empty())
        {
            niu = *idle.begin(); // 获取空闲NIU
            idle.erase(niu); // 从空闲NIU列表中移除获取到的NIU
            in_use.emplace(niu); // 将获取到的NIU添加到使用中的NIU列表
        }

        Mutex_Unlock(); // 互斥锁解锁

        return niu;
    }

    /**
     * @brief    
     *           返还使用中的NIU
     *           
     * @param    指向使用中NIU的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    int Give_Back(std::shared_ptr<BaseNIU> niu)
    {
        Mutex_Lock(); // 互斥锁上锁

        // 判断返还NIU是否不在使用中的NIU列表中
        if (in_use.find(niu) == in_use.end())
        {
            LOGE("Failed to give back NIU, which could not be found in the list of currently used NIU");
            return AIENGINE_INVALID_PARAM;
        }

        in_use.erase(niu); // 将NIU从使用列表中移除
        idle.emplace(niu); // 将NIU添加到空闲列表

        Mutex_Unlock(); // 互斥锁解锁

        return AIENGINE_NO_ERROR;
    }

protected:
    bool is_ready; // 是否准备好

	std::unordered_set<std::shared_ptr<BaseNIU>> idle; // 空闲NIU列表
	std::unordered_set<std::shared_ptr<BaseNIU>> in_use; // 使用中的NIU列表

    virtual int Mutex_Lock(void) = 0; //互斥锁上锁
    virtual int Mutex_Unlock(void) = 0; // 互斥锁解锁

    /**
     * @brief    
     *           BaseNIUManager析构函数
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    virtual ~BaseNIUManager()
    {

    }

    /**
     * @brief    
     *           BaseNIUManager构造函数
     *           
     * @param    model_tag:         模型标签
     * @param    num_sessions:      创建会话个数
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    BaseNIUManager(std::string model_tag, int num_sessions)
    {
        is_ready = false;
        auto model_data = BaseModelLoader::Get_Model_Data(model_tag);
        if (model_data == nullptr)
        {
            LOGE("Failed to get model data.");
            return;
        }

        // 创建指定数量NIU
        for (int i = 0; i < num_sessions; i++)
        {
            std::shared_ptr<BaseNIU> niu = nullptr;

            switch (model_data->inference_framework)
            {
                case BaseModelLoader::AIENGINE_INFERENCE_FRAMEWORK_SSC377:
                    #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_SSC377
                    niu = std::static_pointer_cast<BaseNIU>(std::shared_ptr<AIEngineSSC377::NIU>(AIEngineSSC377::NIU::Construct(model_tag), AIEngineSSC377::NIU::Destruct));
                    #else
                    LOGE("SSC377 inference framework didn't enabled, please check the 'autoconf.h' file.");
                    #endif /* #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_SSC377 */
                    break;
                case BaseModelLoader::AIENGINE_INFERENCE_FRAMEWORK_MNN:
                    #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_MNN
                    niu = std::static_pointer_cast<BaseNIU>(std::shared_ptr<AIEngineMNN::NIU>(AIEngineMNN::NIU::Construct(model_tag), AIEngineMNN::NIU::Destruct));
                    #else
                    LOGE("MNN inference framework didn't enabled, please check the 'autoconf.h' file.");
                    #endif /* #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_MNN */
                    break;
                case BaseModelLoader::AIENGINE_INFERENCE_FRAMEWORK_PADDLE_LITE:
                    #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_PADDLE_LITE
                    niu = std::static_pointer_cast<BaseNIU>(std::shared_ptr<AIEnginePaddleLite::NIU>(AIEnginePaddleLite::NIU::Construct(model_tag), AIEnginePaddleLite::NIU::Destruct));
                    #else
                    LOGE("Paddle-Lite inference framework didn't enabled, please check the 'autoconf.h' file.");
                    #endif /* #ifdef MACR_AIENGINE_INFERENCE_FRAMEWORK_PADDLE_LITE */
                    break;
                default:
                    LOGE("Unsupported inference framework.");
                    break;
            }

            // 判断NIU是否创建失败
            if (niu == nullptr)
            {
                break;
            }

            idle.emplace(niu); // 添加空闲NIU
        }

        // 判断是否创建足够数量的NIU
        if (idle.size() != (size_t)num_sessions)
        {
            return; // 未创建足够数量的NIU，错误退出
        }

        is_ready = true; // 通知初始化成功
    }
};

#if __unix__ // 遵循POSIX标准
#include "pthread.h"
/**
 * @brief    
 *           POSIX标准的NIUManager实现
 *           
 * @date     2024-03-05 Created by HuangJP
 */
class NIUManager : public BaseNIUManager
{
public:
    /**
     * @brief    
     *           NIUManager对象销毁函数
     *           
     * @param    manager:       指向NIUManager的指针
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    static void Destruct(NIUManager *manager)
    {
        delete manager;
    }

    /**
     * @brief    
     *           销毁指定模型的NIUManager
     *           
     * @param    model_tag:         模型标签
     *           
     * @retval   错误码
     *           
     * @date     2024-03-19 Created by HuangJP
     */
    static int Destroy(std::string model_tag)
    {
        // 判断管理器是否未创建
        if (managers.find(model_tag) == managers.end())
        {
            LOGE("Unable to destroy non-existent niu manager.");
            return AIENGINE_INSTANCE_NOT_FOUND_ERROR;
        }

        managers.erase(model_tag);
        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           创建NIUManager
     *           
     * @param    model_tag:         模型标签
     * @param    num_sessions:      创建会话个数
     *           
     * @retval   错误码
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    static int Create(std::string model_tag, int num_sessions)
    {
        // 判断管理器是否已经创建
        if (managers.find(model_tag) != managers.end())
        {
            return AIENGINE_NO_ERROR;
        }
        
        // 创建NIU管理器
        std::shared_ptr<NIUManager> manager = std::shared_ptr<NIUManager>(new (std::nothrow)NIUManager(model_tag, num_sessions), Destruct);
        if (manager == nullptr)
        {
            LOGE("Unable to create NIU manager: out of memory");
            return AIENGINE_OUT_OF_MEMORY;
        }

        if (manager->is_ready == false)
        {
            LOGE("Failed to create NIU manager");
            return AIENGINE_CONSTRUCT_ERROR;
        }

        managers[model_tag] = manager;

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           互斥锁上锁函数
     *           
     * @retval   错误码
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    int Mutex_Lock(void) override
    {
        int ret = pthread_mutex_lock(&mutex);
        if (ret != 0)
        {
            LOGE("Failed to lock mutex");
            return AIENGINE_THREAD_ERROR;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           互斥锁解锁函数
     *           
     * @retval   错误码
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    int Mutex_Unlock(void) override
    {
        int ret = pthread_mutex_unlock(&mutex);
        if (ret != 0)
        {
            LOGE("Failed to unlock mutex");
            return AIENGINE_THREAD_ERROR;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           获取NIUManager实例
     *           
     * @param    model_tag:     模型标签
     *           
     * @retval   指向NIUManager实例的指针，若获取失败，则返回空指针
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    static std::shared_ptr<NIUManager> Get_Instance(std::string model_tag)
    {
        if (managers.find(model_tag) == managers.end())
        {
            return nullptr;
        }

        return managers[model_tag];
    }

protected:
    pthread_mutex_t mutex; // 互斥锁
    static std::unordered_map<std::string, std::shared_ptr<NIUManager>> managers; // 存储NIUManager的map

    /**
     * @brief    
     *           NIUManager析构函数
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    ~NIUManager()
    {
        pthread_mutex_destroy(&mutex); // 销毁互斥锁
    }

    /**
     * @brief    
     *           NIUManager构造函数
     *           
     * @param    model_tag:         模型标签
     * @param    num_sessions:      创建会话个数
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    NIUManager(std::string model_tag, int num_sessions): BaseNIUManager(model_tag, num_sessions)
    {
        pthread_mutex_init(&mutex, nullptr); // 初始化互斥锁
    }

};
#endif /* __unix__ */

#endif
//-----------------------------------------------------------------------------
//  End of file