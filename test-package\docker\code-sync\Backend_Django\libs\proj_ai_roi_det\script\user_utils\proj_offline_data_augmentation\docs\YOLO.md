<div align="center">
    <h1>Y<PERSON>O</h1>
    YOLO模型训练离线增强脚本
</div>

# DatasetYOLO

## 方法预览

| 函数名                                      | 功能                                      |
| ------------------------------------------- | ----------------------------------------- |
| [Distribute_Data_Set](#Distribute_Data_Set) | 基于Labelme标签划分训练集、验证集和测试集 |
| [Invert_Image](#Invert_Image)               | 对图片做反色处理                          |
| [Crop_Ref](#Crop_Ref)                       | 以目标为中心裁剪图片                      |
| [Rotate](#Rotate)                           | 旋转图像                                  |

## 方法说明

### Distribute_Data_Set

- 功能

基于Labelme标签划分训练集、验证集和测试集。

- 声明

```python
def Distribute_Data_Set(
        self,
        src_pathes: dict[list[str], float],
        train: float,
        val: float,
        dst_path: str,
        append_mode: bool = False,
        only_label: bool = False,
        seed: int = 20000330,
        ) -> tuple[str] | None
```

- 返回值

训练集、验证集和测试集位置，失败时返回None。

- 形参

| 参数名称    | 描述                                                         |
| ----------- | ------------------------------------------------------------ |
| src_pathes  | 数据集路径字典及分配比例                                     |
| train       | 训练数据集比例（0~1.0）                                      |
| val         | 验证数据集比例（0~1.0）                                      |
| dst_path    | 目标生成路径                                                 |
| append_mode | 是否采用追加模式，追加模式下会在保留原有数据集的基础上增加新数据 |
| only_label  | 只保存标签文件                                           |
| seed        | 随机种子，用于确保数据集划分的结果不变                       |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
yolo.Distribute_Data_Set({
    "datasets/AI_Cap/z_Mixed_test_0909-Done-Full": 1.0,
    }, 1.0, 0.0, "datasets/Done/DataSet", seed=20240908)
```

### Invert_Image

- 功能

对图片做反色处理。

- 声明

```python
def Invert_Image(
        self,
        src_pathes: list[str],
        dst_path: str,
    ) -> str | None:
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称   | 描述         |
| ---------- | ------------ |
| src_pathes | 数据集路径   |
| dst_path   | 目标生成路径 |

- 示例

```python
yolo = SegmentYOLO(["1D", "2D_QR", "2D_DM"])
for data_type in ["train", "val", "test"]:
    yolo.Invert_Image([
        f"datasets/Done/DataSet/{data_type}",
        ], f"datasets/Done/Inverted/{data_type}")
```

### Crop_Ref

- 功能

以目标为中心裁剪图片。

- 声明

```python
def Crop_Ref(
        self,
        src_pathes: list[str],
        size: tuple[int, int],
        ref_classes: list[str],
        dst_path: str,
    	maintain_ratio: float = 0.3,
    	mode: str = "fix",
    ) -> str | None:
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称       | 描述                                                         |
| -------------- | ------------------------------------------------------------ |
| src_pathes     | 数据集路径                                                   |
| size           | 目标尺寸（H, W）                                             |
| ref_classes    | 参考的类别                                                   |
| dst_path       | 目标生成路径                                                 |
| maintain_ratio | 目标保留比例，当目标裁剪后保留下来的面积比例小于该值时，舍弃目标 |
| mode           | 裁剪模式，可选[<br />"fix": 按照目标尺寸裁剪图片，裁剪出来的图片大小与目标尺寸一致,<br />"dilate": 基于实例尺寸外扩目标尺寸裁剪图像，裁剪得到的图片大小等于实例尺寸加上目标尺寸,<br />] |

- 示例

```python
yolo = SegmentYOLO(["1D", "2D_QR", "2D_DM"])
for data_type in ["train", "val", "test"]:
    yolo.Crop_Ref([
        f"datasets/Done/DataSet/{data_type}",
        ], [320, 320], ["1D", "2D_QR", "2D_DM"], f"datasets/Done/Crop_Ref/{data_type}")
```

### Rotate

- 功能

旋转图像。

- 声明

```python
def Rotate(
        self,
        src_pathes: list[str],
        quantity: int,
        dst_path: str,
        seed: int = 20241007,
        angle_range: float = (-180, 180),
    ) -> str | None:
```

- 返回值 

增强数据存储路径，失败时返回None。

- 形参

| 参数名称    | 描述                                 |
| ----------- | ------------------------------------ |
| src_pathes  | 数据集路径                           |
| quantity    | 生成图片数量                         |
| dst_path    | 目标生成路径                         |
| seed        | 随机种子，用于确保每次旋转的结果相同 |
| angle_range | 旋转范围（单位：度）                 |

- 示例

```python
yolo = SegmentYOLO(["1D", "2D_QR", "2D_DM"])
for data_type in ["train", "val", "test"]:
    yolo.Rotate([
        f"datasets/Done/DataSet/{data_type}",
        ], 5, f"datasets/Done/Rotate/{data_type}")
```

# ObjectDetectionYOLO

> 继承自[DatasetYOLO](#DatasetYOLO)

## 方法预览

| 函数名                                           | 功能                             |
| ------------------------------------------------ | -------------------------------- |
| [YOLO_To_Labelme](#YOLO_To_Labelme)              | 将YOLO标签转为Labelme标签        |
| [Labelme_To_YOLO](#Labelme_To_YOLO {#Detection}) | 将Labelme标签转为YOLO标签        |
| [Resize_Image](#Resize_Image)                    | 缩放图像                         |
| [Horizontal_Joint](#Horizontal_Joint)            | 横向拼接图像                     |
| [Vertical_Crop](#Vertical_Crop)                  | 垂直方向裁剪图像                 |
| [Horizontal_Crop_Ref](#Horizontal_Crop_Ref)      | 水平方向裁剪图像（保证类别完整） |
| [Horizontal_Crop](#Horizontal_Crop)              | 水平方向裁剪图像                 |
| [Add_Quantized_Noise](#Add_Quantized_Noise)      | 添加量化噪声                     |

## 方法说明

### YOLO_To_Labelme

- 说明

将YOLO标签转为Labelme标签。

- 声明

```python
def YOLO_To_Labelme(
        self,
        src_pathes: list[str],
        dst_path: str
    ) -> str | None:
```

- 返回值

转换后的数据存储路径，失败时返回None。

- 形参

| 参数名称   | 描述         |
| ---------- | ------------ |
| src_pathes | 数据集路径   |
| dst_path   | 目标生成路径 |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped"])
yolo.YOLO_To_Labelme(["datasets/Prelabeled/UncappedLong"], "datasets/New")
```

### Labelme_To_YOLO {#Detection}

- 说明

将Labelme标签转为YOLO标签（目标检测形式）。

- 声明

```python
def Labelme_To_YOLO(
        self,
        src_pathes: list[str],
        dst_path: str
    ) -> str | None:
```

- 返回值

转换后的数据存储路径，失败时返回None。

- 形参

| 参数名称   | 描述         |
| ---------- | ------------ |
| src_pathes | 数据集路径   |
| dst_path   | 目标生成路径 |

- 示例

```python
yolo = ObjectDetectionYOLO(["Capped", "Uncapped", "Cap"])
for data_type in ["train", "val", "test"]:
    yolo.Labelme_To_YOLO([
        f"datasets/Done/Resize/{data_type}",
        ], f"datasets/Train/{data_type}")
```

### Resize_Image

- 说明

缩放图像。

- 声明

```python
def Resize_Image(
        self,
        src_pathes: list[str],
        size: tuple[int, int],
        dst_path: str,
        keep_ratio: bool = True,
        mode: str = "integer_factor",
        interpolation_method: str = "nearest",
        min_obj_w: float = 1.0,
    ) -> str | None:
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称               | 描述                                                         |
| --------------------- | ------------------------------------------------------------ |
| src_pathes            | 数据集路径                                                   |
| size                  | 目标缩放尺寸（H, W）                                         |
| dst_path              | 目标生成路径                                                 |
| keep_ratio            | 保持高度和宽度方向上的缩放系数一致                               |
| mode                  | 缩放模式，可选[<br />"integer_factor": 使用整数倍数缩放,<br />]               |
| interpolation_method  | 插值方法，可选[<br />"nearest": 最邻近插值,<br />"linear": 双线性插值,<br />] |
| min_obj_w             | 最小目标宽度，宽度值低于该值的目标会被丢弃                                     |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
for data_type in ["train", "val", "test"]:
    yolo.Resize_Image([
        f"datasets/Done/Horizontal_Ref/{data_type}",
        ], (480, 96), f"datasets/Done/Resize/{data_type}")
```

### Horizontal_Joint

- 说明

参考某个类别横向拼接图像，需要确保指定数据集中的图像高度跟宽度保持一致。

- 声明

```python
def Horizontal_Joint(
        self,
        src_pathes: list[str],
        ref_classes: list[str],
        quantity: int,
        dst_path: str,
        gap: int = 5,
        min_obj_w: float = 1.0,
        seed: int = 20240907,
    ):
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称    | 描述                                       |
| ----------- | ------------------------------------------ |
| src_pathes  | 数据集路径                                 |
| ref_classes | 拼接参考的类别                             |
| quantity    | 生成图片数量                               |
| dst_path    | 目标生成路径                               |
| gap         | 两张拼接图片中间预留的间隙                 |
| min_obj_w   | 最小目标宽度，宽度值低于该值的目标会被丢弃 |
| seed        | 随机种子，用于确保每次拼接的结果相同       |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
for data_type in ["train", "val", "test"]:
    yolo.Horizontal_Joint([
            f"datasets/Done/Vertical_Crop_Middle/{data_type}",
        ], ["Capped", "Uncapped"], 36, f"datasets/Done/Horizontal_Joint/{data_type}", min_obj_w=16)
```

### Vertical_Crop

- 说明

垂直方向裁剪图像。

- 声明

```python
def Vertical_Crop(
        self,
        src_pathes: list[str],
        tar_w: int,
        retention_ratio: float,
        quantity: int,
        dst_path: str,
        mode: str = "sliding",
        min_obj_w: float = 1.0,
    ) -> str | None:
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称        | 描述                                                         |
| --------------- | ------------------------------------------------------------ |
| src_pathes      | 数据集路径                                                   |
| tar_w           | 目标宽度                                                     |
| retention_ratio | 目标最小保留比例，裁剪后保留的宽度比例低于此值的目标会被丢弃（如果未被裁剪，则保留比例为100%） |
| quantity        | 生成图片数量                                                 |
| dst_path        | 目标生成路径                                                 |
| mode            | 裁剪方法，可选[<br />"sliding": 滑窗裁剪,<br />"middle": 中心裁剪，仅生成一张图片,<br />] |
| min_obj_w       | 最小目标宽度，宽度值低于该值的目标会被丢弃                   |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
for data_type in ["train", "val", "test"]:
    yolo.Vertical_Crop([
            f"datasets/Done/DataSet/{data_type}",
        ],
        192, 0.42, 8, f"datasets/Done/Vertical_Crop_Middle/{data_type}", mode="middle", min_obj_w=10.0)
```

### Horizontal_Crop_Ref

- 说明

水平方向裁剪图像（保证类别完整）。

- 声明

```python
def Horizontal_Crop_Ref(
        self,
        src_pathes: list[str],
        ref_cls: str,
        start_x: int,
        width: int,
        dst_path: str,
        maintain_classes: dict["cls":dict["ratio":float,"relevance":list[str]]] = {},
        mode: str = "below_ref",
        min_obj_h: float = 1.0,
    ) -> str | None:
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称         | 描述                                                         |
| ---------------- | ------------------------------------------------------------ |
| src_pathes       | 数据集路径                                                   |
| ref_cls          | 参考类别                                                     |
| start_x          | 裁剪起始x轴坐标                                              |
| width            | 截取区域宽度                                                 |
| dst_path         | 目标生成路径                                                 |
| maintain_classes | 保证完整的类别，索引：类别，值：保持比例和相关联的标签       |
| mode             | 裁剪方法，可选[<br />"below_ref": 裁剪参考类别以下的图片,<br />] |
| min_obj_h        | 最小目标高度，高度值低于该值的目标会被丢弃                   |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
for data_type in ["train", "val", "test"]:
    yolo.Horizontal_Crop_Ref([
        f"datasets/Done/Vertical_Crop_Middle/{data_type}",
        f"datasets/Done/Horizontal_Joint/{data_type}",
        ], "Barcode", 0, 192, f"datasets/Done/Horizontal_Ref/{data_type}", min_obj_h=32,
        maintain_classes={"Cap":{"ratio":0.9, "relevance":["Capped"]}})
```

### Horizontal_Crop

- 说明

水平方向裁剪图像。

- 声明

```python
def Horizontal_Crop(
        self,
        src_pathes: list[str],
        retention_ratio: float,
        quantity: int,
        dst_path: str,
        mode: str = "bottom_base",
        min_obj_h: float = 1.0,
    ) -> str | None:
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称        | 描述                                                         |
| --------------- | ------------------------------------------------------------ |
| src_pathes      | 数据集路径                                                   |
| retention_ratio | 目标最小保留比例，裁剪后保留的高度比例低于此值的目标会被丢弃（如果未被裁剪，则保留比例为100%） |
| quantity        | 生成图片数量                                                 |
| dst_path        | 目标生成路径                                                 |
| mode            | 裁剪方法，可选[<br />"bottom_base": 裁剪至图像底部,<br />]   |
| min_obj_h       | 最小目标高度，高度值低于该值的目标会被丢弃                   |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
for data_type in ["train", "val", "test"]:
    yolo.Horizontal_Crop([
        f"datasets/Done/Vertical_Crop_Middle/{data_type}",
        f"datasets/Done/Horizontal_Joint/{data_type}",
        ], 0.9, 10, f"datasets/Done/Horizontal_Crop/{data_type}", min_obj_h=32)
```

### Add_Quantized_Noise

- 说明

添加量化噪声。

- 声明

```python
def Add_Quantized_Noise(
        self,
        src_pathes: list[str],
        proportion: float,
        bits: int,
        white_noise_intensity: int,
        dst_path: str,
        append_mode: bool = False,
        seed: int = 20240901,
    ) -> str | None:
```

- 返回值

增强数据存储路径，失败时返回None。

- 形参

| 参数名称              | 描述                                                         |
| --------------------- | ------------------------------------------------------------ |
| src_pathes            | 数据集路径                                                   |
| proportion            | 加噪图片占比                                                 |
| bits                  | 模拟RAW8图片缩减至多少bits时产生的量化噪声                   |
| white_noise_intensity | 白噪声强度                                                   |
| dst_path              | 目标生成路径                                                 |
| append_mode           | 是否采用追加模式，追加模式下会在保留原有数据集的基础上增加新数据 |
| seed                  | 随机种子，用于确保使用相同的图片加噪                         |

- 示例

```python
yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
yolo.Add_Quantized_Noise([
    "datasets/Done/DataSet/train",
], 1.0, 6, 32, "datasets/Done/DataSet/train", append_mode=True)
```

# SegmentYOLO

> 继承自[DatasetYOLO](#DatasetYOLO)

## 方法预览

| 函数名                                         | 功能                          |
| ---------------------------------------------- | ----------------------------- |
| [Labelme_To_YOLO](#Labelme_To_YOLO {#Segment}) | 将Labelme标签转为YOLO分割标签 |

## 方法说明

### Labelme_To_YOLO {#Segment}

- 说明

将Labelme标签转为YOLO分割标签。

- 声明

```python
def Labelme_To_YOLO(
        self,
        src_pathes: list[str],
        dst_path: str
    ) -> str | None:
```

- 返回值

转换后的数据存储路径，失败时返回None。

- 形参

| 参数名称   | 描述         |
| ---------- | ------------ |
| src_pathes | 数据集路径   |
| dst_path   | 目标生成路径 |

- 示例

```python
yolo = SegmentYOLO(["1D", "2D_QR", "2D_DM"])
for data_type in ["train", "val", "test"]:
    yolo.Labelme_To_YOLO([
        f"datasets/Done/DataSet/{data_type}",
        ], f"datasets/Train/{data_type}")
```

