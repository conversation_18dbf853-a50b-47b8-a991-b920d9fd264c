# Ultralytics YOLO 🚀, AGPL-3.0 license

from ultralytics.utils import SETTINGS

try:
    assert SETTINGS["raytune"] is True  # verify integration is enabled
    import ray
    from ray import tune
    from ray.air import session

except (ImportErro<PERSON>, AssertionError):
    tune = None


def on_fit_epoch_end(trainer):
    """Sends training metrics to <PERSON>ne at end of each epoch."""
    if ray.train._internal.session._get_session():  # replacement for deprecated ray.tune.is_session_enabled()
        metrics = trainer.metrics
        metrics["epoch"] = trainer.epoch
        session.report(metrics)


callbacks = (
    {
        "on_fit_epoch_end": on_fit_epoch_end,
    }
    if tune
    else {}
)
