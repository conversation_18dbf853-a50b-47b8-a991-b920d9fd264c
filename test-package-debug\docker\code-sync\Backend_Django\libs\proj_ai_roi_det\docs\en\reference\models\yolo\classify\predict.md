---
description: Learn about the ClassificationPredictor class for YOLO models at Ultralytics. Get details on initialization, preprocessing, and postprocessing for classification tasks.
keywords: YOL<PERSON>, ClassificationPredictor, Ultralytics, model prediction, preprocess, postprocess, deep learning, machine learning
---

# Reference for `ultralytics/models/yolo/classify/predict.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/classify/predict.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.classify.predict.ClassificationPredictor

<br><br>
