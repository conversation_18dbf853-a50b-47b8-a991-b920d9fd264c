import cv2
import math
import random
import onnxruntime
import numpy as np

def grid_nms(scores, grid=4, threshold=8):
    # 对特征点做NMS
    c, h, w = scores.shape
    scores_grid = scores.reshape(-1, grid, grid)
    max_grid = []
    # 遍历每个grid
    for grid in scores_grid:
        max_value = np.max(grid)
        if max_value < threshold:
            max_grid.append(np.zeros_like(grid))
            continue

        grid_with_max = np.where(grid == max_value, grid, 0)
        max_grid.append(grid_with_max)

    socres_nms = np.array(max_grid).reshape(c, h, w)
    return socres_nms

def extract_keypoints(scores):
    keypoints = []
    for i in range(scores.shape[1]):
        for j in range(scores.shape[2]):
            if scores[0, i, j] > 0:
                keypoints.append({"score": scores[0, i, j], "coord": (j, i)})
    return keypoints

def extract_descriptors(keypoints, descriptors, img_shape):
    n, c, h, w = descriptors.shape
    # 逐个特征点提取描述子
    for keypoint in keypoints:
        # 获取关键点在原图中的坐标
        x_orig, y_orig = keypoint["coord"]
        
        # 将原图坐标映射到描述子特征图坐标
        # 注意：img_shape是(1, 1, H, W)格式，所以img_shape[2]是高度，img_shape[3]是宽度
        x_desc = (x_orig / img_shape[3]) * w  # 映射到描述子宽度
        y_desc = (y_orig / img_shape[2]) * h  # 映射到描述子高度
        
        # 确保坐标在有效范围内
        x_desc = max(0, min(x_desc, w - 1))
        y_desc = max(0, min(y_desc, h - 1))
        
        # 双线性插值的四个邻近点
        x0 = int(math.floor(x_desc))
        y0 = int(math.floor(y_desc))
        x1 = min(x0 + 1, w - 1)
        y1 = min(y0 + 1, h - 1)
        
        # 计算插值权重
        dx = x_desc - x0
        dy = y_desc - y0
        
        # 获取四个邻近点的描述子
        d00 = descriptors[0, :, y0, x0]  # 左上
        d01 = descriptors[0, :, y0, x1]  # 右上  
        d10 = descriptors[0, :, y1, x0]  # 左下
        d11 = descriptors[0, :, y1, x1]  # 右下
        
        # 双线性插值
        descriptor = (1 - dx) * (1 - dy) * d00 + \
                    dx * (1 - dy) * d01 + \
                    (1 - dx) * dy * d10 + \
                    dx * dy * d11
        
        # L2归一化
        norm = np.linalg.norm(descriptor)
        if norm > 0:
            descriptor = descriptor / norm
        
        keypoint["descriptor"] = descriptor

def debug_descriptors(keypoints, name=""):
    """
    调试描述子质量
    """
    if not keypoints:
        print(f"{name} 没有关键点")
        return
        
    descriptors_list = []
    for kp in keypoints:
        if "descriptor" in kp:
            descriptors_list.append(kp["descriptor"])
    
    if not descriptors_list:
        print(f"{name} 没有描述子")
        return
        
    descriptors_array = np.array(descriptors_list)
    
    print(f"\n=== {name} 描述子统计 ===")
    print(f"描述子数量: {len(descriptors_list)}")
    print(f"描述子维度: {descriptors_array.shape[1]}")
    print(f"描述子范围: [{descriptors_array.min():.4f}, {descriptors_array.max():.4f}]")
    print(f"描述子均值: {descriptors_array.mean():.4f}")
    print(f"描述子标准差: {descriptors_array.std():.4f}")
    
    # 检查是否有全零描述子
    zero_descriptors = np.sum(np.sum(np.abs(descriptors_array), axis=1) < 1e-6)
    print(f"全零描述子数量: {zero_descriptors}")
    
    # 检查L2范数
    norms = np.linalg.norm(descriptors_array, axis=1)
    print(f"L2范数范围: [{norms.min():.4f}, {norms.max():.4f}]")
    print(f"L2范数均值: {norms.mean():.4f}")
    
    # 计算描述子之间的相似性
    if len(descriptors_list) > 1:
        # 计算前10个描述子之间的余弦相似度
        n_sample = min(10, len(descriptors_list))
        sample_desc = descriptors_array[:n_sample]
        similarities = np.dot(sample_desc, sample_desc.T)
        # 去除对角线元素
        similarities = similarities[np.triu_indices_from(similarities, k=1)]
        print(f"描述子相似度范围: [{similarities.min():.4f}, {similarities.max():.4f}]")
        print(f"描述子相似度均值: {similarities.mean():.4f}")

def ransac_homography(matches, keypoints1, keypoints2, threshold=3.0, max_iterations=1000, min_inliers=4):
    """
    使用RANSAC算法估计单应性矩阵并过滤匹配
    Args:
        matches: 匹配对列表
        keypoints1: 第一张图的关键点
        keypoints2: 第二张图的关键点
        threshold: 内点阈值（像素）
        max_iterations: 最大迭代次数
        min_inliers: 最小内点数量
    Returns:
        best_homography: 最佳单应性矩阵
        inlier_matches: 内点匹配
    """
    if len(matches) < min_inliers:
        return None, []
    
    best_homography = None
    best_inliers = []
    max_inlier_count = 0
    
    # 提取匹配点坐标
    pts1 = np.array([keypoints1[match[0]]["coord"] for match in matches], dtype=np.float32)
    pts2 = np.array([keypoints2[match[1]]["coord"] for match in matches], dtype=np.float32)
    
    for iteration in range(max_iterations):
        # 随机选择4个点
        if len(matches) < 4:
            break
            
        random_indices = random.sample(range(len(matches)), 4)
        sample_pts1 = pts1[random_indices]
        sample_pts2 = pts2[random_indices]
        
        # 计算单应性矩阵
        try:
            H = cv2.getPerspectiveTransform(sample_pts1, sample_pts2)
        except:
            continue
        
        # 计算所有点的重投影误差
        pts1_homogeneous = np.column_stack([pts1, np.ones(len(pts1))])
        projected_pts = H @ pts1_homogeneous.T
        projected_pts = projected_pts[:2] / projected_pts[2]  # 归一化
        projected_pts = projected_pts.T
        
        # 计算误差
        errors = np.linalg.norm(pts2 - projected_pts, axis=1)
        inliers = errors < threshold
        inlier_count = np.sum(inliers)
        
        # 更新最佳结果
        if inlier_count > max_inlier_count:
            max_inlier_count = inlier_count
            best_homography = H
            best_inliers = [matches[i] for i in range(len(matches)) if inliers[i]]
    
    return best_homography, best_inliers

def transform_rotated_box(box_points, homography):
    """
    使用单应性矩阵变换旋转框
    Args:
        box_points: 旋转框的四个角点 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
        homography: 单应性矩阵
    Returns:
        transformed_points: 变换后的四个角点
    """
    if homography is None:
        return None
    
    # 转换为齐次坐标
    points = np.array(box_points, dtype=np.float32)
    points_homogeneous = np.column_stack([points, np.ones(len(points))])
    
    # 应用单应性变换
    transformed = homography @ points_homogeneous.T
    transformed = transformed[:2] / transformed[2]  # 归一化
    transformed = transformed.T
    
    return transformed.astype(int)

def draw_rotated_box(img, box_points, color=(0, 255, 0), thickness=2):
    """
    在图像上绘制旋转框
    Args:
        img: 图像
        box_points: 四个角点
        color: 颜色
        thickness: 线条粗细
    """
    if box_points is None:
        return img
    
    # 绘制四条边
    for i in range(4):
        pt1 = tuple(box_points[i])
        pt2 = tuple(box_points[(i + 1) % 4])
        cv2.line(img, pt1, pt2, color, thickness)
    
    # 绘制角点
    for pt in box_points:
        cv2.circle(img, tuple(pt), 5, color, -1)
    
    return img

def interactive_box_selection(image_path):
    """
    交互式选择旋转框
    Args:
        image_path: 图像路径
    Returns:
        box_points: 四个角点坐标
    """
    img = cv2.imread(image_path)
    clone = img.copy()
    points = []
    
    def mouse_callback(event, x, y, flags, param):
        nonlocal points, img
        
        if event == cv2.EVENT_LBUTTONDOWN:
            if len(points) < 4:
                points.append([x, y])
                cv2.circle(img, (x, y), 5, (0, 0, 255), -1)
                cv2.putText(img, str(len(points)), (x+10, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                
                # 如果有两个点，画线
                if len(points) >= 2:
                    cv2.line(img, tuple(points[-2]), tuple(points[-1]), (0, 255, 0), 2)
                
                # 如果有四个点，闭合框
                if len(points) == 4:
                    cv2.line(img, tuple(points[-1]), tuple(points[0]), (0, 255, 0), 2)
                
                cv2.imshow("Select Box Points", img)
        
        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右键重置
            points = []
            img = clone.copy()
            cv2.imshow("Select Box Points", img)
    
    cv2.namedWindow("Select Box Points")
    cv2.setMouseCallback("Select Box Points", mouse_callback)
    cv2.imshow("Select Box Points", img)
    
    print("请在图像上点击四个点来定义旋转框（按顺序点击）")
    print("右键重置，ESC键完成选择")
    
    while True:
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC键
            break
        elif key == ord('r'):  # R键重置
            points = []
            img = clone.copy()
            cv2.imshow("Select Box Points", img)
    
    cv2.destroyAllWindows()
    
    if len(points) == 4:
        return np.array(points)
    else:
        print("未选择完整的四个点")
        return None

def match_descriptors(keypoints1, keypoints2, max_distance=0.7):
    """
    基于描述子进行特征匹配
    Args:
        keypoints1: 第一张图的关键点列表
        keypoints2: 第二张图的关键点列表  
        max_distance: 最大匹配距离阈值
    Returns:
        matches: 匹配对列表，每个元素为(idx1, idx2, distance)
    """
    matches = []
    
    for i, kp1 in enumerate(keypoints1):
        if "descriptor" not in kp1:
            continue
            
        best_distance = float('inf')
        second_best_distance = float('inf')
        best_match_idx = -1
        
        for j, kp2 in enumerate(keypoints2):
            if "descriptor" not in kp2:
                continue
                
            # 计算欧氏距离
            distance = np.linalg.norm(kp1["descriptor"] - kp2["descriptor"])
            
            if distance < best_distance:
                second_best_distance = best_distance
                best_distance = distance
                best_match_idx = j
            elif distance < second_best_distance:
                second_best_distance = distance
        
        # 添加匹配对
        if best_match_idx != -1 and best_distance < max_distance:
            matches.append((i, best_match_idx, best_distance))
    
    return matches

def filter_matches(matches, ratio_threshold=0.8):
    """
    使用Lowe's ratio test过滤匹配
    Args:
        matches: 原始匹配列表
        ratio_threshold: 比值阈值
    Returns:
        filtered_matches: 过滤后的匹配列表
    """
    # 按第一个关键点索引分组
    matches_by_kp1 = {}
    for match in matches:
        idx1, idx2, distance = match
        if idx2 not in matches_by_kp1:
            matches_by_kp1[idx2] = []
        matches_by_kp1[idx2].append(match)
    
    filtered_matches = []
    for idx1, kp2_matches in matches_by_kp1.items():
        if len(kp2_matches) < 2:
            # 如果只有一个匹配，直接添加
            filtered_matches.extend(kp2_matches)
            continue
            
        # 按距离排序
        kp2_matches.sort(key=lambda x: x[2])
        best_match = kp2_matches[0]
        second_best_match = kp2_matches[1]
        
        # Lowe's ratio test
        if best_match[2] / second_best_match[2] < ratio_threshold:
            filtered_matches.append(best_match)
    
    return filtered_matches

def visualize_matches(img1_path, img2_path, keypoints1, keypoints2, matches):
    """
    可视化匹配结果
    Args:
        img1_path: 第一张图片路径
        img2_path: 第二张图片路径
        keypoints1: 第一张图的关键点
        keypoints2: 第二张图的关键点
        matches: 匹配对列表
    """
    # 读取图片
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    # 创建拼接图片
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    
    # 统一高度
    if h1 > h2:
        img2 = cv2.resize(img2, (int(w2 * h1 / h2), h1))
    elif h2 > h1:
        img1 = cv2.resize(img1, (int(w1 * h2 / h1), h2))
    
    h = max(h1, h2)
    combined_img = np.zeros((h, w1 + w2, 3), dtype=np.uint8)
    combined_img[:img1.shape[0], :img1.shape[1]] = img1
    combined_img[:img2.shape[0], img1.shape[1]:] = img2
    
    # 绘制匹配线
    colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
    
    for i, (idx1, idx2, distance) in enumerate(matches):
        color = colors[i % len(colors)]
        
        # 获取关键点坐标
        pt1 = keypoints1[idx1]["coord"]
        pt2 = keypoints2[idx2]["coord"]
        pt2_shifted = (pt2[0] + img1.shape[1], pt2[1])  # 第二张图的坐标需要偏移
        
        # 绘制关键点
        cv2.circle(combined_img, pt1, 5, color, 2)
        cv2.circle(combined_img, pt2_shifted, 5, color, 2)
        
        # 绘制连接线
        cv2.line(combined_img, pt1, pt2_shifted, color, 2)
        
        # 显示匹配距离
        mid_point = ((pt1[0] + pt2_shifted[0]) // 2, (pt1[1] + pt2_shifted[1]) // 2)
        cv2.putText(combined_img, f"{distance:.3f}", mid_point, 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    # 显示结果
    cv2.imshow(f"Matches ({len(matches)} found)", combined_img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return combined_img

def inference(model, image_path, N=100):
    # 读取图片
    img_color = cv2.imread(image_path)
    img = cv2.cvtColor(img_color, cv2.COLOR_BGR2GRAY)

    # 获取模型输入
    inputs = model.get_inputs()
    n, c, h, w = inputs[0].shape
    # 计算缩放比例
    w_ratio = img.shape[1] / w
    h_ratio = img.shape[0] / h
    
    # 图片预处理
    img_color = cv2.resize(img_color, (w, h), interpolation=cv2.INTER_NEAREST)
    img = cv2.resize(img, (w, h), interpolation=cv2.INTER_NEAREST)

    # 图片转换为tensor
    # img = img.transpose(2, 0, 1)
    img = img.reshape(1, c, h, w)
    # 归一化
    img = img.astype(np.float32)
    img = img / 255.0

    # 输入模型
    input_name = inputs[0].name
    outputs = model.run(None, {input_name: img})
    scores, descriptors = outputs

    # 对特征点做NMS
    scores = scores * 255
    scores = grid_nms(scores)
    keypoints = extract_keypoints(scores)

    # # 可视化scores
    # scores = scores.squeeze(0)
    # scores = scores.reshape(h, w)
    # cv2.imshow("scores", scores)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()

    # 提取前N个特征点
    keypoints = sorted(keypoints, key=lambda x: x["score"], reverse=True)[:N]

    # # 可视化特征点
    # for keypoint in keypoints:
    #     cv2.circle(img_color, keypoint["coord"], 3, (0, 0, 255), 1)
    # cv2.imshow("img", img_color)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()

    # 提取描述子
    extract_descriptors(keypoints, descriptors, img.shape)

    # 映射回原图坐标
    for keypoint in keypoints:
        keypoint["coord"] = (int(keypoint["coord"][0] * w_ratio), int(keypoint["coord"][1] * h_ratio))

    return keypoints

def run_box_transformation_demo(image1_path, image2_path, homography):
    """
    运行旋转框变换演示
    """
    print("\n=== 旋转框变换演示 ===")
    print("1. 使用预设示例框")
    print("2. 交互式选择框")
    print("3. 跳过")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 示例旋转框
        img1 = cv2.imread(image1_path)
        h1, w1 = img1.shape[:2]
        center_x, center_y = w1//2, h1//2
        box_size = min(w1, h1) // 4  # 自适应大小
        
        example_box = np.array([
            [center_x - box_size//2, center_y - box_size//2],
            [center_x + box_size//2, center_y - box_size//2],
            [center_x + box_size//2, center_y + box_size//2],
            [center_x - box_size//2, center_y + box_size//2]
        ])
        
        print(f"使用示例旋转框: {example_box.tolist()}")
        return example_box
        
    elif choice == "2":
        # 交互式选择
        print("请在图像上点击四个点来定义旋转框...")
        selected_box = interactive_box_selection(image1_path)
        return selected_box
        
    else:
        return None

def visualize_box_transformation(image1_path, image2_path, original_box, transformed_box):
    """
    可视化旋转框变换结果
    """
    if original_box is None or transformed_box is None:
        return
    
    # 读取图像
    img1_with_box = cv2.imread(image1_path)
    img2_with_box = cv2.imread(image2_path)
    
    # 在原图上绘制原始框
    img1_with_box = draw_rotated_box(img1_with_box, original_box, (0, 255, 0), 3)
    cv2.putText(img1_with_box, "Original Box", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    
    # 在目标图上绘制变换后的框
    img2_with_box = draw_rotated_box(img2_with_box, transformed_box, (0, 0, 255), 3)
    cv2.putText(img2_with_box, "Transformed Box", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    
    # 创建对比图
    h1, w1 = img1_with_box.shape[:2]
    h2, w2 = img2_with_box.shape[:2]
    
    # 统一高度
    if h1 > h2:
        img2_with_box = cv2.resize(img2_with_box, (int(w2 * h1 / h2), h1))
    elif h2 > h1:
        img1_with_box = cv2.resize(img1_with_box, (int(w1 * h2 / h1), h2))
    
    h = max(h1, h2)
    comparison_img = np.zeros((h, img1_with_box.shape[1] + img2_with_box.shape[1], 3), dtype=np.uint8)
    comparison_img[:img1_with_box.shape[0], :img1_with_box.shape[1]] = img1_with_box
    comparison_img[:img2_with_box.shape[0], img1_with_box.shape[1]:] = img2_with_box
    
    # 添加分割线
    cv2.line(comparison_img, (img1_with_box.shape[1], 0), 
             (img1_with_box.shape[1], h), (255, 255, 255), 2)
    
    cv2.imshow("Box Transformation Result", comparison_img)
    print("按任意键关闭窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return comparison_img

if __name__ == "__main__":
    # 加载onnx模型
    model = onnxruntime.InferenceSession("superpoint.onnx")

    # 推理参数
    image1 = r"sample4\\1.bmp"
    image2 = r"sample4\\3.bmp"
    N = 100  # 可以尝试增加到300-500
    template_keypoints = inference(model, image1, N)
    target_keypoints = inference(model, image2, N)
    
    # 调试描述子质量
    debug_descriptors(template_keypoints, "模板图")
    debug_descriptors(target_keypoints, "目标图")
    
    # 特征匹配
    print(f"\n模板图关键点数量: {len(template_keypoints)}")
    print(f"目标图关键点数量: {len(target_keypoints)}")
    
    # 尝试不同的匹配参数
    match_params = [
        {"max_distance": 0.3, "ratio_threshold": 0.8, "name": "严格匹配"},
        {"max_distance": 0.5, "ratio_threshold": 0.8, "name": "中等匹配"},
        {"max_distance": 0.7, "ratio_threshold": 0.9, "name": "宽松匹配"},
    ]
    
    best_matches = []
    best_param_name = ""
    
    for param in match_params:
        print(f"\n--- {param['name']} ---")
        matches = match_descriptors(template_keypoints, target_keypoints, 
                                  max_distance=param['max_distance'])
        print(f"初始匹配数量: {len(matches)}")
        
        filtered_matches = filter_matches(matches, ratio_threshold=param['ratio_threshold'])
        print(f"过滤后匹配数量: {len(filtered_matches)}")
        
        if len(filtered_matches) > len(best_matches):
            best_matches = filtered_matches
            best_param_name = param['name']
    
    print(f"\n最佳匹配参数: {best_param_name}")
    print(f"最佳匹配数量: {len(best_matches)}")
    
    # RANSAC过滤
    print(f"\n=== RANSAC过滤 ===")
    homography, ransac_matches = ransac_homography(
        best_matches, template_keypoints, target_keypoints, 
        threshold=5.0, max_iterations=2000, min_inliers=4
    )
    
    if homography is not None:
        print(f"RANSAC后匹配数量: {len(ransac_matches)}")
        print(f"内点比例: {len(ransac_matches)/len(best_matches)*100:.1f}%")
        
        # 计算RANSAC匹配质量统计
        ransac_distances = [match[2] for match in ransac_matches]
        print(f"RANSAC匹配质量统计:")
        print(f"平均距离: {np.mean(ransac_distances):.4f}")
        print(f"距离标准差: {np.std(ransac_distances):.4f}")
        print(f"最小距离: {min(ransac_distances):.4f}")
        print(f"最大距离: {max(ransac_distances):.4f}")
        
        # 可视化RANSAC过滤后的匹配
        visualize_matches(image1, image2, template_keypoints, target_keypoints, ransac_matches)
        
        # 交互式旋转框变换
        selected_box = run_box_transformation_demo(image1, image2, homography)
        
        if selected_box is not None:
            # 变换旋转框到目标图像
            transformed_box = transform_rotated_box(selected_box, homography)
            
            if transformed_box is not None:
                print(f"变换后的旋转框: {transformed_box.tolist()}")
                
                # 可视化结果
                visualize_box_transformation(image1, image2, selected_box, transformed_box)
                
                # 计算框的几何信息
                original_center = np.mean(selected_box, axis=0)
                transformed_center = np.mean(transformed_box, axis=0)
                
                print(f"\n几何变换信息:")
                print(f"原始框中心: ({original_center[0]:.1f}, {original_center[1]:.1f})")
                print(f"变换后中心: ({transformed_center[0]:.1f}, {transformed_center[1]:.1f})")
                print(f"中心位移: ({transformed_center[0]-original_center[0]:.1f}, {transformed_center[1]-original_center[1]:.1f})")
                
            else:
                print("旋转框变换失败")
        
        # 输出单应性矩阵
        print(f"\n单应性矩阵:")
        print(homography)
        
    else:
        print("RANSAC失败，无法估计单应性矩阵")
        print("可能原因：匹配点太少或匹配质量不好")
        
        # 可视化原始匹配结果
        if len(best_matches) > 0:
            visualize_matches(image1, image2, template_keypoints, target_keypoints, best_matches[:20])
    
    print(f"\n=== 处理完成 ===")
    
    # 如果你想要交互式选择旋转框，可以取消注释下面的代码：
    """
    # 交互式旋转框选择和变换
    if homography is not None:
        print("\n是否要交互式选择旋转框？(y/n): ", end="")
        choice = input().lower()
        if choice == 'y':
            selected_box = interactive_box_selection(image1)
            if selected_box is not None:
                transformed_box = transform_rotated_box(selected_box, homography)
                if transformed_box is not None:
                    # 可视化结果...
                    pass
    """
