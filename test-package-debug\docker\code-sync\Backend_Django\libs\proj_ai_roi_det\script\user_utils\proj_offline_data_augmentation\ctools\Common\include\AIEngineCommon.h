/* 设计文档：docs/implement/Common.md */
#ifndef _AIE_COMMON_H
#define _AIE_COMMON_H

//-----------------------------------------------------------------------------
//  Includes

#include "stdint.h"
//-----------------------------------------------------------------------------
//  Definitions

//!!!! 这部分结构体如果增加成员或改变成员，需要同步给解码器
//!===============================================
struct DetectionBBoxInfo { // Detection Bounding Box Information
//? 坐标格式(xmin，ymin，xmax，ymax)，当图片坐标原点位于左上角时，(xmin, ymin)是左上角点的坐标，(xmax, ymax)是右下角的坐标。
    float xmin;     // x_left
    float ymin;     // y_top
    float xmax;     // x_right
    float ymax;     // y_bottom
    float score;    // 置信度
    int classID;    // 类别ID
};

typedef struct QuadPoints { // 四边形四个点的坐标，按顺时针排列，基准点在左上角
    int x0, y0;     // 左上角点坐标
    int x1, y1;     // 右上角点坐标
    int x2, y2;     // 右下角点坐标
    int x3, y3;     // 左下角点坐标
}QuadPoints;

typedef uint32_t unicode_t;     // utf-8编码
typedef struct OCRCharacter{    // OCR单个字符识别结果
    unicode_t character;        // 字符信息
    float score;                // 字符置信度
}OCRCharacter;

typedef struct Point { // 单个点的坐标
    int x;          // x轴坐标
    int y;          // y轴坐标
}Point;
//!===============================================

typedef struct CoordinateVOC { // VOC格式坐标
//? 坐标格式(xmin，ymin，xmax，ymax)，当图片坐标原点位于左上角时，(xmin, ymin)是左上角点的坐标，(xmax, ymax)是右下角的坐标。
    float xmin;     // x_left
    float ymin;     // y_top
    float xmax;     // x_right
    float ymax;     // y_bottom
}CoordinateVOC;

typedef struct CoordinateYOLO { // YOLO格式坐标
//? 坐标格式(center_x, center_y, width, height)，当图片坐标原点位于左上角时，(center_x-width/2, center_y-height/2)是左上角点的坐标，(center_x+width/2, center_y+height/2)是右下角的坐标。
    float center_x; // 中心点x轴坐标
    float center_y; // 中心点y轴坐标
    float width;    // 矩形框的宽度
    float height;   // 矩形框的高度
}CoordinateYOLO;

// 图像位深度枚举，请按照对应位数定义枚举数值，如RAW10图像枚举值对应为10
typedef enum {
    BIT_DEPTH_RAW8  = 8,                                                // RAW8图像
    BIT_DEPTH_RAW10 = 10,                                               // RAW10图像
    BIT_DEPTH_RAW12 = 12,                                               // RAW12图像
}bit_depth_t;

// 前处理类型列表：前处理类型影响图像检测区域大小及检测的精确度，统一要求检测区域不能在原图区域外
typedef enum {
    PREP_TYPE_FULL_IMG_RESIZE = 0,                                      // 全图resize，检测区域为整张图片，必要时对原图做缩放，处理结果通常会丢失细节
    PREP_TYPE_ROI_CETNER,                                               // 中心挖图，检测区域为图片正中心等同于输入张量大小（如320x320）的区域，处理时不做缩放，不存在细节丢失
    PREP_TYPE_ROI_DEP_ON_COORD,                                         // 根据ROI坐标执行挖图，检测区域坐标和大小由[roi_coord]成员变量指定，要求检测区域尺寸不大于输入张量大小（如320x320），处理时不做缩放，不存在细节丢失
    PREP_TYPE_ROI_RESIZE_ADAPTIVE_DEP_ON_COORD,                         // 根据ROI坐标执行挖图自适应采样，检测区域坐标和大小由[roi_coord]成员变量指定，自动根据检测区域尺寸和输入张量尺寸调整resize倍数，必要时对原图做缩放，处理结果通常存在细节丢失
    PREP_TYPE_ROI_RESIZE_2X_DEP_ON_CENTER_COORD,                        // 根据中心坐标执行挖图采样，检测区域坐标由使用[center_coord]成员变量指定（仅使用center_x和center_y），检测区域大小等同于输入张量大小的两倍，处理时做缩放，存在细节丢失
    PREP_TYPE_ROI_DEP_ON_QUAD_POINTS,                                   // 对指定四边形区域的图像做旋转摆正，四边形区域坐标[quad_points]成员变量指定
    PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_CLOCKWISE,              // 对指定四边形区域的图像做旋转摆正，四边形区域坐标[quad_points]成员变量指定，90_DEGREES_CLOCKWISE表示原始图像顺时针旋转了90°
}prep_type_t;

// 输入图片格式列表
typedef enum {
    COLOR_FORMAT_GRAY = 0,                                              // 灰度图片
}color_format_t;

// 任务类型：
typedef enum {
    TASK_TYPE_DETECTION = 0,                                            // 目标检测任务
    TASK_TYPE_SEMANTIC_SEGMENTATION,                                    // 语义分割任务
    TASK_TYPE_OCR_DETECTION,                                            // 字符检测任务
    TASK_TYPE_OCR_RECOGNIZE,                                            // 字符识别任务
}task_type_t;

typedef enum {
    POSTPROCESS_METHOD_DETECTION_YOLOV7 = 0,                            // YOLOv7目标检测后处理
    POSTPROCESS_METHOD_DETECTION_YOLOV7_CUSTOM,                         // YOLOv7目标检测后处理（自定义后处理方法）
    POSTPROCESS_METHOD_DETECTION_YOLOV8,                                // YOLOv8目标检测后处理
    POSTPROCESS_METHOD_DETECTION_YOLOV8_TRANSPOSE,                      // YOLOv8目标检测后处理（模型带Transpose）
    POSTPROCESS_METHOD_DETECTION_YOLOV10,                               // YOLOv10目标检测后处理
    POSTPROCESS_METHOD_SEMANTIC_SEGMENTATION_YOLOV8,                    // YOLOv8语义分割后处理
    POSTPROCESS_METHOD_OCR_DETECTION_POCRV3,                            // PaddleOCRv3字符检测后处理
    POSTPROCESS_METHOD_OCR_RECOGNIZE_POCRV4_CTC_LABEL_DECODE,           // PaddleOCRv4字符识别CTCLabelDecode后处理
    POSTPROCESS_METHOD_OCR_RECOGNIZE_POCRV4_CUSTOM,                     // PaddleOCRv4字符识别后处理（自定义后处理方法）
}postprocess_method_t; // 模型后处理方法枚举

// 错误码
typedef enum {
    // AIEngine Internal Error
    AIENGINE_NO_ERROR                   = 0,                            // 无错误
    AIENGINE_GOT_NULLPTR                = 1,                            // 获取到空指针，通过函数获取到空指针时，返回此错误码
    AIENGINE_OUT_OF_MEMORY              = 2,                            // 无法申请内存空间
    AIENGINE_INVALID_PARAM              = 3,                            // 无效的传入参数，函数传入参数无效时返回此错误码
    AIENGINE_INFERENCE_FAILED           = 4,                            // 推理失败
    AIENGINE_THREAD_ERROR               = 5,                            // 出现线程控制相关的错误
    AIENGINE_OPEN_FILE_ERROR            = 6,                            // 打开文件失败
    AIENGINE_CONSTRUCT_ERROR            = 7,                            // 构造对象失败
    AIENGINE_INVALID_MODEL_DATA         = 8,                            // 无效的模型数据，当模型数据存在问题时（如参数不完整）时返回此错误码

    // User Error
    AIENGINE_TASK_TYPE_ERROR            = 30,                           // 用户所指定的模型不适用于目标任务
    AIENGINE_INPUT_DATA_ERROR           = 31,                           // 用户输入数据有误，例如使用到的传入参数为空指针
    AIENGINE_ROI_COORD_ERROR            = 32,                           // 用户传入的ROI坐标有误，例如超出了原图区域，或坐标严格限制的情况下超出该限制
    AIENGINE_MODEL_NOT_FOUND_ERROR      = 33,                           // 找不到用户指定的模型
    AIENGINE_INTERFACE_ERROR            = 34,                           // 调用错误的接口，例如单输入单输出的模型调用了多输入多输出的接口
    AIENGINE_GET_SERVICE_ERROR          = 35,                           // 获取服务失败，主要是指安卓平台客户端获取服务失败
    AIENGINE_VERSION_VERIFY_ERROR       = 36,                           // 版本校验失败，例如安卓平台服务端/客户端版本不一致
    AIENGINE_INSTANCE_NOT_FOUND_ERROR   = 37,                           // 找不到实例，例如"NIUManager"、"NIU"等类的实例未创建
} ai_engine_error_code_t;

// 前处理数据
typedef struct PreprocessedData{
    struct CoordinateVOC coord; // ROI检测时，使用的ROI坐标

    float scale_h; // 高度方向缩放系数
    float scale_w; // 宽度方向缩放系数
    int start_y; // 检测区域y轴起始坐标
    int start_x; // 检测区域x轴起始坐标
    int det_h; // 检测区域高度
    int det_w; // 检测区域宽度

    int resize_n; // 输入张量批大小
    int resize_h; // 输入张量高度
    int resize_w; // 输入张量宽度
    int resize_c; // 输入张量通道数
    void *input_tensor_ptr; // 指向输入张量的指针
}PreprocessedData;

// 输入图像数据信息结构体
struct ImageInfo {
    struct CoordinateVOC roi_coord;                                     // [输入]ROI坐标
    struct CoordinateYOLO center_coord;                                 // [输入]中心坐标
    struct QuadPoints quad_points;                                      // [输入]四个点围成的四边形坐标

    void *img_data_pt;                                                  // [输入]图片存储地址
    int img_height;                                                     // [输入]图像高度
    int img_width;                                                      // [输入]图像宽度

    color_format_t color_format;                                        // [输入]输入图片颜色格式
    bit_depth_t bit_depth;                                              // [输入]灰度图像位深度

    prep_type_t prep_type;                                              // [输入]前处理类型
    int8_t sub_bit_range;                                               // [输入]灰度图像子位深度提取范围
};

// 注意：此处宏定义的值对应着逻辑右移位数，例如：提取Bit2~Bit9的灰度值时，需要将灰度值整体右移2位。请勿修改此处定义的值，后续有需求增加新的提取范围时，请按正确的右移位数设置宏定义值
//============================================
#define MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE       (-1)                // 子位深度提取范围：自适应
#define MACR_SUB_BIT_EXTRACTED_RANGE_00_07          (0)                 // 子位深度提取范围：Bit00~Bit07
#define MACR_SUB_BIT_EXTRACTED_RANGE_01_08          (1)                 // 子位深度提取范围：Bit01~Bit08
#define MACR_SUB_BIT_EXTRACTED_RANGE_02_09          (2)                 // 子位深度提取范围：Bit02~Bit09
#define MACR_SUB_BIT_EXTRACTED_RANGE_03_10          (3)                 // 子位深度提取范围：Bit03~Bit10
#define MACR_SUB_BIT_EXTRACTED_RANGE_04_11          (4)                 // 子位深度提取范围：Bit04~Bit11
//============================================

//* 模型命名 | 注：模型命名应作为关键字包含在模型文件名中，以确保NPU模块能找到对应模型文件
#define AI_MODEL_ROI_DETECT             "AI_ROI_Dete"                   // 目标检测模型，条码检测模型
#define AI_MODEL_CONTOUR_DETECT         "AI_Contour_Det"                // 实例分割模型，轮廓检测模型
#define AI_MODEL_FINDER_320             "AI_Finder_320x"                // 目标检测模型，QR码定位标识模型（320x320）
#define AI_MODEL_FINDER_640             "AI_Finder_640x"                // 目标检测模型，QR码定位标识次型（640x640）
#define AI_MODEL_DOT_320x320            "AI_Dot_320x320"                // 目标检测模型，圆点检测模型（320x320）
#define AI_MODEL_OCR_DET_320x320        "AI_OCR_Det_320x320"            // OCR字符检测模型（320x320）
#define AI_MODEL_OCR_REC_960x48         "AI_OCR_Rec_960x48"             // OCR字符识别模型（960x48）
#define AI_MODEL_OCR_DET_CHNLP          "AI_OCR_Det_CHNLP"              // 中国车牌检测模型
#define AI_MODEL_OCR_REC_CHNLP          "AI_OCR_Rec_CHNLP"              // 中国车牌识别模型
//-----------------------------------------------------------------------------
//  Declarations


#endif
//-----------------------------------------------------------------------------
//  End of file
