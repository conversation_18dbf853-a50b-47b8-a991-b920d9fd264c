import React, { useState, useEffect, useRef } from 'react';
import {
    Form,
    Select,
    Button,
    Typography,
    Space,
    message, // For displaying feedback
    Spin,    // For loading indicator
    Alert,   // Added Alert for additional information
    Tooltip, // <-- Add Tooltip import
    Radio, // Added Radio
    Modal, // Added Modal for save confirmation
    Slider, // Added Slider for batch interval
    InputNumber, // Added InputNumber for batch interval
    Row, // Added Row for layout
    Col, // Added Col for layout
    Switch, // 添加Switch组件
    Divider, // 添加Divider组件
} from 'antd';
import { useQuery } from '@tanstack/react-query';
import { useImageWorkspace, DisplayableDetection, ImageInfo } from '../../contexts/ImageWorkspaceContext'; // Import ImageInfo
import { detectOcrPaddle, OcrResultItem, CustomApiError, OcrDetectionApiResponse, getOcrTasksForReactQuery } from '../../services/api'; // Import the new API function and types
import { useOcrDetectionParams, OcrDisplayMode } from '../../contexts/OcrDetectionContext';
import { useFunctionPanel } from '../../contexts/FunctionPanelContext';
import { SaveOutlined, SyncOutlined } from '@ant-design/icons'; // For tooltips
import compositeViewImage from '../../assets/OCR-合成视图.png'; // Import composite view image
import directRenderImage from '../../assets/OCR-直接渲染.png'; // Import direct render image
import { calculateCanvasRotationTransform, MIN_ADAPTIVE_FONT_SIZE, MAX_ADAPTIVE_FONT_SIZE, TEXT_PADDING_FACTOR } from '../../utils/textRotationUtils'; // Import text rotation utilities
import { calculatePolygonDimensions } from '../../utils/textRotationUtils'; // Import calculatePolygonDimensions

const { Title } = Typography;
const { Option } = Select;

// Interface for batch result items, similar to BarcodeDetectionPanel
interface BatchOcrResultItem {
    index: number;
    fileName: string;
    textResultsCount: number; // Number of text items found
    imageDataUrl: string | null; // Captured image data URL with overlays
    processed: boolean;
    error?: string;
}

// Helper function to create the composite OCR image
async function createOcrCompositeImage(
  originalImageFile: File,
  ocrApiResponse: OcrDetectionApiResponse,
  originalWidth: number,
  originalHeight: number
): Promise<string> {
  const canvas = document.createElement('canvas');
  canvas.width = originalWidth * 2;
  canvas.height = originalHeight;
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.error('Failed to get canvas 2D context');
    throw new Error('Failed to get canvas context');
  }

  const img = new Image();
  const objectURL = URL.createObjectURL(originalImageFile);

  await new Promise<void>((resolve, reject) => {
    img.onload = () => {
      URL.revokeObjectURL(objectURL);
      resolve();
    };
    img.onerror = (err) => {
      URL.revokeObjectURL(objectURL);
      console.error('Image load error for composite:', err);
      reject(new Error('Failed to load image for composite view'));
    };
    img.src = objectURL;
  });

  // 1. Draw original image on the left half
  ctx.drawImage(img, 0, 0, originalWidth, originalHeight);

  const highlightColor = 'rgba(255, 255, 0, 0.3)';
  // 2. Draw detection boxes (semi-transparent yellow) on the LEFT half
  ctx.fillStyle = highlightColor;
  ocrApiResponse.results.forEach(item => {
    const polygon = item.box;
    if (polygon && polygon.length >= 3) {
      ctx.beginPath();
      ctx.moveTo(polygon[0][0], polygon[0][1]);
      for (let i = 1; i < polygon.length; i++) {
        ctx.lineTo(polygon[i][0], polygon[i][1]);
      }
      ctx.closePath();
      ctx.fill();
    }
  });

  // 3. Fill right half with white background
  ctx.fillStyle = 'white';
  ctx.fillRect(originalWidth, 0, originalWidth, originalHeight);

  // 4. Draw corresponding detection boxes on the RIGHT half & collect bounds
  const rightCanvasHighlightAreas: Array<{
    x: number, 
    y: number, 
    width: number, 
    height: number,
    polygon: [number, number][], // 添加原始多边形信息以便计算旋转
    originalPolygon: [number, number][], // 添加原始（未平移）多边形信息用于字体计算
    originalText: string; // 原始文本内容
  }> = [];
  ctx.fillStyle = highlightColor;
  ocrApiResponse.results.forEach(item => {
    const polygon = item.box;
    if (polygon && polygon.length >= 3) {
      ctx.beginPath();
      const translatedPolygon: Array<[number, number]> = polygon.map(p => [p[0] + originalWidth, p[1]]);
      ctx.moveTo(translatedPolygon[0][0], translatedPolygon[0][1]);
      let minX_poly = translatedPolygon[0][0];
      let maxX_poly = translatedPolygon[0][0];
      let minY_poly = translatedPolygon[0][1];
      let maxY_poly = translatedPolygon[0][1];

      for (let i = 1; i < translatedPolygon.length; i++) {
        ctx.lineTo(translatedPolygon[i][0], translatedPolygon[i][1]);
        minX_poly = Math.min(minX_poly, translatedPolygon[i][0]);
        maxX_poly = Math.max(maxX_poly, translatedPolygon[i][0]);
        minY_poly = Math.min(minY_poly, translatedPolygon[i][1]);
        maxY_poly = Math.max(maxY_poly, translatedPolygon[i][1]);
      }
      ctx.closePath();
      ctx.fill();
      rightCanvasHighlightAreas.push({
          x: minX_poly,
          y: minY_poly,
          width: maxX_poly - minX_poly,
          height: maxY_poly - minY_poly,
          polygon: translatedPolygon, // 保存平移后的多边形坐标
          originalPolygon: polygon, // 保存原始多边形坐标用于字体计算
          originalText: item.text, // 保存原始文本内容
      });
    }
  });

  // Helper to find optimal font size for a given text and polygon on canvas
  const findOptimalFontSizeForCanvas = (
    context: CanvasRenderingContext2D,
    text: string,
    polygon: [number, number][], // Original polygon for dimensions
    maxFontSize: number
  ): number => {
    if (!text || polygon.length < 4) {
      return MIN_ADAPTIVE_FONT_SIZE;
    }

    const { width: boxWidth, height: boxHeight } = calculatePolygonDimensions(polygon);

    // Available space for text, considering padding
    const availableWidth = boxWidth * TEXT_PADDING_FACTOR;
    const availableHeight = boxHeight * TEXT_PADDING_FACTOR;

    if (availableWidth <= 0 || availableHeight <= 0) {
      return MIN_ADAPTIVE_FONT_SIZE;
    }

    let low = MIN_ADAPTIVE_FONT_SIZE;
    let high = maxFontSize; // Start with the maximum allowed font size
    let optimalFontSize = MIN_ADAPTIVE_FONT_SIZE;

    // Binary search for the largest font size that fits
    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      context.font = `${mid}pt Arial`; // Set font to measure

      const metrics = context.measureText(text);
      const textWidth = metrics.width;
      const textHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent; // Get actual height

      // Check if text fits within the available width and height
      // For rotated text, the textWidth is the length along the detection box's primary axis
      // and textHeight is the thickness. We compare them with boxWidth and boxHeight.
      if (textWidth <= availableWidth && textHeight <= availableHeight) {
        optimalFontSize = mid; // This font size fits, try larger
        low = mid + 1;
      } else {
        high = mid - 1; // This font size doesn't fit, try smaller
      }
    }
    return optimalFontSize;
  };

  // 5. Draw recognized text (with rotation) and index on the RIGHT half
  const indexFontSize = 10;
  const textPadding = 5; // General padding

  ocrApiResponse.results.forEach((item, index) => {
    if (index < rightCanvasHighlightAreas.length) {
      const highlightArea = rightCanvasHighlightAreas[index];

      // --- Draw Index Number ---
      ctx.font = `${indexFontSize}pt Arial`;
      ctx.fillStyle = '#333333';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';
      const indexText = `${index + 1}.`;
      ctx.fillText(indexText, highlightArea.x + textPadding, highlightArea.y + textPadding);

      // --- Draw Rotated Text (following polygon orientation) ---
      // 使用 findOptimalFontSizeForCanvas 来获取精确的自适应字体大小
      const optimalFontSize = findOptimalFontSizeForCanvas(ctx, item.text, highlightArea.originalPolygon, MAX_ADAPTIVE_FONT_SIZE);
      
      ctx.font = `${optimalFontSize}pt Arial`;
      ctx.fillStyle = 'black';
      
      // 计算文本旋转参数（基于平移后的多边形）
      const rotationTransform = calculateCanvasRotationTransform(highlightArea.polygon);
      
      // 保存当前 canvas 状态
      ctx.save();
      
      // 移动到旋转中心点并应用旋转
      ctx.translate(rotationTransform.centerX, rotationTransform.centerY);
      ctx.rotate(rotationTransform.rotationAngle);
      
      // 设置文本对齐方式
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // 在旋转后的坐标系中绘制文本（相对于旋转中心）
      ctx.fillText(item.text, 0, 0);
      
      // 恢复 canvas 状态
      ctx.restore();
    }
  });

  return canvas.toDataURL('image/png');
}

const OcrDetectionPanel: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage(); // Ensure messageApi is obtained here
    // 添加一个ref来跟踪当前处理的图像索引和图像信息
    const processingImageRef = useRef<{
        index: number;
        imageInfo: ImageInfo | null;
        file: File | null;
    }>({
        index: -1,
        imageInfo: null,
        file: null
    });

    const {
        currentImageInfo,
        imageList,
        currentImageIndex,
        setCompositeImageDisplay,
        restoreOriginalImageDisplay,
        restoreOriginalForAiRestore, // 新增：专门用于OCR的状态恢复
        originalImageInfo,
        setDetectionResults,
        getDisplayedViewDataURL, // Added for saving direct render view
        setCurrentImageIndex, // Added for batch processing
        forceRefreshImage, // 添加强制刷新图像函数
        setIsBatchProcessingActive, // Ensure this is destructured
    } = useImageWorkspace();
    const { selectedFunction } = useFunctionPanel();
    const {
        selectedTaskValue,
        setSelectedTaskValue,
        setOcrProcessingResults,
        selectedDisplayMode, // Added
        setSelectedDisplayMode, // Added
        batchProcessingInterval, // Added
        setBatchProcessingInterval, // Added
        autoInferenceEnabled, // 添加自动推理状态
        setAutoInferenceEnabled, // 添加设置自动推理状态的方法
        isInferring, // 单图推理状态
        setIsInferring, // 设置单图推理状态
        isBatchProcessing, // 批量处理状态
        setIsBatchProcessing, // 设置批量处理状态
        // Get task loading states from context
        availableOcrTasks,
        setAvailableOcrTasks,
        ocrTasksLoading,
        setOcrTasksLoading,
        ocrTasksError,
        setOcrTasksError,
    } = useOcrDetectionParams();

    // 使用react-query获取OCR任务
    const {
        isLoading: reactQueryTasksLoadingInitial,
        error: reactQueryTasksError,
        data: fetchedTasksData,
        refetch: refetchOcrTasks,
    } = useQuery<any[], CustomApiError>({
        queryKey: ['ocrTasks'],
        queryFn: getOcrTasksForReactQuery,
    });

    // 同步react-query状态到context
    useEffect(() => {
        setOcrTasksLoading(reactQueryTasksLoadingInitial);
    }, [reactQueryTasksLoadingInitial, setOcrTasksLoading]);

    useEffect(() => {
        if (fetchedTasksData) {
            setAvailableOcrTasks(fetchedTasksData);
            setOcrTasksError(null);
            // 设置默认选择的任务
            if (fetchedTasksData.length > 0) {
                if (!selectedTaskValue || !fetchedTasksData.some(t => t.task_name === selectedTaskValue)) {
                    setSelectedTaskValue(fetchedTasksData[0].task_name);
                }
            }
        }
    }, [fetchedTasksData, setAvailableOcrTasks, setSelectedTaskValue, selectedTaskValue, setOcrTasksError]);

    useEffect(() => {
        if (reactQueryTasksError) {
            const errMsg = reactQueryTasksError.message || '获取OCR任务列表失败';
            setOcrTasksError(errMsg);
            setAvailableOcrTasks([]);
        }
    }, [reactQueryTasksError, setOcrTasksError, setAvailableOcrTasks]);

    // 移除本地状态，使用Context中的状态
    // const [loading, setLoading] = useState(false);
    // const [isBatchOcrLoading, setIsBatchOcrLoading] = useState<boolean>(false); // For batch OCR
    const [batchOcrResults, setBatchOcrResults] = useState<{
        totalImages: number;
        processedImages: number;
        totalTextResults: number;
        resultItems: BatchOcrResultItem[];
    }>({
        totalImages: 0,
        processedImages: 0,
        totalTextResults: 0,
        resultItems: [],
    });
    const [showOcrSaveConfirmModal, setShowOcrSaveConfirmModal] = useState<boolean>(false);
    const [isSavingOcrBatch, setIsSavingOcrBatch] = useState<boolean>(false);

    // 用于跟踪当前面板状态，避免重复加载
    const currentPanelRef = useRef<string | null>(null);

    const [form] = Form.useForm();



    // 监听面板切换，每次切换到OCR检测面板时显示任务加载成功提示（完全仿照AI复原面板逻辑）
    useEffect(() => {
        const currentKey = selectedFunction?.key as string;
        if (currentKey === 'ocr-detection' && currentPanelRef.current !== currentKey && fetchedTasksData && fetchedTasksData.length > 0 && !ocrTasksLoading) {
            console.log('[OcrDetectionPanel] 面板切换到OCR检测，任务已加载');
            currentPanelRef.current = currentKey;
            messageApi.success('OCR模型列表已成功加载！');
        } else if (currentKey !== 'ocr-detection') {
            // 当切换到其他面板时，重置状态
            currentPanelRef.current = null;
        }
    }, [selectedFunction?.key, fetchedTasksData, ocrTasksLoading, messageApi]);

    // 设置默认选择的任务，无需表单同步
    useEffect(() => {
        if (!ocrTasksLoading && availableOcrTasks.length > 0) {
            if (!selectedTaskValue && availableOcrTasks[0]) {
                setSelectedTaskValue(availableOcrTasks[0].task_name);
            }
        }
    }, [availableOcrTasks, ocrTasksLoading, selectedTaskValue, setSelectedTaskValue]);

    const runOcrInference = async () => {
        await handleSingleImageOcrInference();
    };

    useEffect(() => {
        // @ts-ignore
        window.runOcrInference = runOcrInference;

        return () => {
            // @ts-ignore
            delete window.runOcrInference;
        };
    }, [currentImageInfo, selectedTaskValue, selectedDisplayMode, availableOcrTasks]);

    useEffect(() => {
        form.setFieldsValue({
            display_mode: selectedDisplayMode,
            batch_processing_interval: batchProcessingInterval,
            auto_inference: autoInferenceEnabled,
        });
    }, [selectedDisplayMode, batchProcessingInterval, autoInferenceEnabled, form]);

    const handleSingleImageOcrInference = async () => {
        if (!currentImageInfo || currentImageIndex < 0 || !imageList[currentImageIndex]) {
            messageApi.error('请先加载一张图片。');
            return;
        }
        if (!selectedTaskValue) {
            messageApi.error('请选择一个OCR模型/任务。');
            return;
        }
        if (!selectedDisplayMode) {
            messageApi.error('请选择结果展示方式。');
            return;
        }

        setIsInferring(true);
        setOcrProcessingResults(null);

        const imageFileToProcess = imageList[currentImageIndex];

        // 关键修复：每次OCR处理前都确保恢复到原始图像状态
        // 使用专门的OCR状态恢复函数，保持originalImageInfo不被清空
        if (originalImageInfo) {
            console.log('[OcrDetectionPanel] 检测到存在原始图像备份，使用OCR专用恢复函数');
            await restoreOriginalForAiRestore();
            await new Promise(resolve => setTimeout(resolve, 300)); // 等待状态更新完成
        }

        // 额外验证：检查当前图像名称是否包含OCR视图标识
        if (currentImageInfo?.name.includes('(OCR View)')) {
            console.warn('[OcrDetectionPanel] 检测到OCR视图状态，强制恢复原始图像');
            await restoreOriginalForAiRestore();
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        if (!imageFileToProcess || !currentImageInfo) {
            messageApi.error('无法获取当前图片信息进行处理。');
            setOcrProcessingResults(null);
            setIsInferring(false);
            return;
        }

        try {
            const response = await detectOcrPaddle({
                image: imageFileToProcess,
                ocr_task_name: selectedTaskValue,
            });

            messageApi.success(`OCR 任务 '${selectedTaskValue}' 处理成功！`); // Use selectedTaskValue for message

            const displayableResults: DisplayableDetection[] = response.results.map((item: OcrResultItem, idx: number) => {
                const xCoords = item.box.map(p => p[0]);
                const yCoords = item.box.map(p => p[1]);
                const calculatedAabbBox: [number, number, number, number] = [
                    Math.min(...xCoords), Math.min(...yCoords),
                    Math.max(...xCoords), Math.max(...yCoords)
                ];
                return {
                    id: `ocr-s-${idx}-${Date.now()}`,
                    box: calculatedAabbBox,
                    polygon: item.box,
                    label: item.text,
                    confidence: item.confidence,
                    type: 'ocr_line',
                    displayIndex: idx + 1,
                };
            });

            setOcrProcessingResults(displayableResults);
            await new Promise(resolve => requestAnimationFrame(resolve));

            if (selectedDisplayMode === 'composite') {
                // 获取原始图像信息，确保使用正确的尺寸
                const originalInfo = originalImageInfo || currentImageInfo;
                const baseName = originalInfo.name.replace(/\s*\((OCR View|Restored|Barcode View|Composite AI Restore)\)\s*$/i, '');

                console.log('[OcrDetectionPanel] 使用图像尺寸:', {
                    width: originalInfo.width,
                    height: originalInfo.height,
                    source: originalImageInfo ? 'originalImageInfo' : 'currentImageInfo'
                });

                const compositeDataUrl = await createOcrCompositeImage(
                    imageFileToProcess,
                    response,
                    originalInfo.width,  // 使用原始图像宽度
                    originalInfo.height  // 使用原始图像高度
                );
                setCompositeImageDisplay(
                    compositeDataUrl,
                    originalInfo.width * 2,  // 合成图宽度 = 原图宽度 × 2
                    originalInfo.height,     // 合成图高度 = 原图高度
                    `${baseName} (OCR View)`
                );
            } else {
                // 直接渲染模式：确保在原始图像上显示检测结果
                if (currentImageInfo?.name.includes('(OCR View)')) {
                    console.log('[OcrDetectionPanel] 单图推理：恢复原始图像显示');
                    await restoreOriginalForAiRestore(); // 使用专用恢复函数
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                if (setDetectionResults) {
                    console.log('[OcrDetectionPanel] 单图推理：设置检测结果');
                    await setDetectionResults(displayableResults);
                }
            }
        } catch (error) {
            console.error('OCR Inference error:', error);
            const errorMessage = error instanceof CustomApiError ? `OCR 检测失败: ${error.message} (状态: ${error.status || 'N/A'})`
                               : error instanceof Error ? `OCR 检测失败: ${error.message}`
                               : 'OCR 检测失败，发生未知错误。';
            messageApi.error(errorMessage);
            if (!(error instanceof CustomApiError && error.message.includes("composite"))) {
                 setOcrProcessingResults(null);
            }
        } finally {
            setIsInferring(false);
        }
    };

    const handleMultiImageOcrDetection = async () => {
        console.log('[OCR Debug] 开始多图推理，当前状态:', {
            currentImageIndex,
            imageListLength: imageList?.length || 0,
            hasCurrentImageInfo: !!currentImageInfo,
            hasOriginalImageInfo: !!originalImageInfo,
            selectedDisplayMode,
            currentImageName: currentImageInfo?.name
        });

        if (!imageList || imageList.length <= 1) {
            messageApi.error('请先通过"打开文件夹"加载多张图片后再执行多图推理！');
            return;
        }
        if (!selectedTaskValue) {
            messageApi.error('请选择一个OCR模型/任务。');
            return;
        }

        setIsBatchProcessing(true);
        const batchOcrMessageKey = 'batchOcrDetection';
        messageApi.loading({ content: `开始批量OCR检测 ${imageList.length} 张图片...`, key: batchOcrMessageKey, duration: 0 });

        if (setIsBatchProcessingActive) setIsBatchProcessingActive(true);

        if (originalImageInfo && currentImageInfo?.name.includes('(OCR View)')) {
            console.log('[OCR Debug] 批量处理前恢复原始图像');
            await restoreOriginalImageDisplay(); // 批量处理开始前可以清空状态
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        console.log('[OCR Debug] 清除检测结果和OCR处理结果');
        setDetectionResults(null);
        setOcrProcessingResults(null);

        let totalTextResultsAcrossImages = 0;
        let imagesProcessedCount = 0;
        const totalImagesToProcess = imageList.length;

        const resultItemsInit: BatchOcrResultItem[] = imageList.map((file, index) => ({
            index,
            fileName: file.name,
            textResultsCount: 0,
            imageDataUrl: null,
            processed: false,
        }));

        setBatchOcrResults({
            totalImages: totalImagesToProcess,
            processedImages: 0,
            totalTextResults: 0,
            resultItems: resultItemsInit,
        });

        const processNextOcrImage = async (idx: number) => {
            if (idx >= totalImagesToProcess) {
                if (setCurrentImageIndex) {
                    processingImageRef.current = { index: -1, imageInfo: null, file: null };
                    if (setDetectionResults) await setDetectionResults(null);
                    setOcrProcessingResults(null);
                    if (originalImageInfo && currentImageInfo?.name?.includes('(OCR View)')) {
                        console.log('[OCR Debug] 批量处理完成后恢复原始图像');
                        await restoreOriginalImageDisplay();
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                    if (forceRefreshImage) {
                        const refreshSuccess = await forceRefreshImage(0);
                        if (!refreshSuccess) {
                            setCurrentImageIndex(0);
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    } else {
                        setCurrentImageIndex(0);
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
                setIsBatchProcessing(false);
                messageApi.success({
                    content: `批量OCR检测完成！共处理 ${imagesProcessedCount} 张图片，发现 ${totalTextResultsAcrossImages} 个文本结果。`,
                    key: batchOcrMessageKey,
                    duration: 5,
                });
                setBatchOcrResults(prev => ({
                    ...prev,
                    processedImages: imagesProcessedCount,
                    totalTextResults: totalTextResultsAcrossImages,
                }));
                if (imagesProcessedCount > 0) {
                    setShowOcrSaveConfirmModal(true);
                }
                if (setIsBatchProcessingActive) setIsBatchProcessingActive(false);
                return;
            }

            const currentFile = imageList[idx];
            let currentImgInfo = await new Promise<ImageInfo | null>(resolve => {
                const img = new Image();
                const objUrl = URL.createObjectURL(currentFile);
                img.onload = () => {
                    resolve({ name: currentFile.name, url: objUrl, width: img.width, height: img.height, type: currentFile.type });
                    URL.revokeObjectURL(objUrl);
                };
                img.onerror = () => {
                    URL.revokeObjectURL(objUrl);
                    resolve(null);
                };
                img.src = objUrl;
            });

            if (!currentFile || !currentImgInfo) {
                setBatchOcrResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[idx] = { ...updatedItems[idx], processed: true, error: '无法加载图片信息' };
                    return { ...prev, resultItems: updatedItems };
                });
                imagesProcessedCount++;
                setTimeout(() => processNextOcrImage(idx + 1), batchProcessingInterval);
                return;
            }

            processingImageRef.current = { index: idx, imageInfo: currentImgInfo, file: currentFile };

            // 批量处理：简化图像切换逻辑，确保稳定切换
            console.log(`[OcrDetectionPanel] 批量处理：切换到图像 ${idx + 1}/${totalImagesToProcess}: ${currentFile.name}`);

            // 首先清理之前的状态
            if (originalImageInfo) {
                console.log('[OcrDetectionPanel] 批量处理：清理之前的合成视图状态');
                await restoreOriginalImageDisplay();
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // 切换到目标图像
            if (setCurrentImageIndex) {
                setCurrentImageIndex(idx);
                await new Promise(resolve => setTimeout(resolve, 800)); // 增加等待时间确保图像切换完成
            }

            if (setDetectionResults) await setDetectionResults(null);
            setOcrProcessingResults(null);

            messageApi.loading({
                content: `批量处理中 ${idx + 1}/${totalImagesToProcess}: ${currentFile.name}`,
                key: batchOcrMessageKey,
                duration: 0,
            });

            try {
                const response = await detectOcrPaddle({
                    image: currentFile,
                    ocr_task_name: selectedTaskValue,
                });

                totalTextResultsAcrossImages += response.results.length;
                imagesProcessedCount++;

                const displayableResults: DisplayableDetection[] = response.results.map((item: OcrResultItem, itemIdx: number) => {
                    const xCoords = item.box.map(p => p[0]);
                    const yCoords = item.box.map(p => p[1]);
                    return {
                        id: `ocr-b-${idx}-${itemIdx}-${Date.now()}`,
                        box: [Math.min(...xCoords), Math.min(...yCoords), Math.max(...xCoords), Math.max(...yCoords)],
                        polygon: item.box,
                        label: item.text,
                        confidence: item.confidence,
                        type: 'ocr_line',
                        displayIndex: itemIdx + 1,
                    };
                });

                setOcrProcessingResults(displayableResults);

                let capturedImageDataUrl: string | null = null;
                if (selectedDisplayMode === 'composite') {
                    console.log('[OcrDetectionPanel] 批量处理使用图像尺寸:', {
                        width: currentImgInfo.width,
                        height: currentImgInfo.height,
                        fileName: currentFile.name
                    });

                    capturedImageDataUrl = await createOcrCompositeImage(currentFile, response, currentImgInfo.width, currentImgInfo.height);
                    if (setCompositeImageDisplay) {
                        const baseName = currentImgInfo.name.replace(/\s*\((OCR View|Restored|Barcode View|Composite AI Restore)\)\s*$/i, '');
                        setCompositeImageDisplay(
                            capturedImageDataUrl,
                            currentImgInfo.width * 2,
                            currentImgInfo.height,
                            `${baseName} (OCR View)`
                        );
                    }

                    // 批量处理：等待合成视图显示完成
                    await new Promise(resolve => setTimeout(resolve, 300));
                } else {
                    const processingImage = processingImageRef.current;
                    if (restoreOriginalImageDisplay && currentImageInfo?.name.includes('(OCR View)')) {
                        await restoreOriginalImageDisplay();
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                    if (setDetectionResults) {
                        await setDetectionResults(null);
                        await new Promise(resolve => requestAnimationFrame(resolve));
                        await new Promise(resolve => setTimeout(resolve, 100));
                        if (currentImageIndex !== idx) {
                            if (forceRefreshImage) {
                                const refreshSuccess = await forceRefreshImage(idx);
                                if (!refreshSuccess && setCurrentImageIndex) {
                                    setCurrentImageIndex(idx);
                                    await new Promise(resolve => setTimeout(resolve, 300));
                                }
                            } else if (setCurrentImageIndex) {
                                setCurrentImageIndex(idx);
                                await new Promise(resolve => setTimeout(resolve, 300));
                            }
                        }
                        const updatedDisplayableResults = displayableResults.map(result => ({
                            ...result,
                            id: `${result.id}-${processingImage.index}`
                        }));
                        await setDetectionResults(updatedDisplayableResults);
                    }
                    await new Promise(resolve => requestAnimationFrame(resolve));
                    await new Promise(resolve => setTimeout(resolve, 200));
                    if (getDisplayedViewDataURL) capturedImageDataUrl = await getDisplayedViewDataURL();
                }

                setBatchOcrResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[idx] = {
                        index: idx,
                        fileName: currentFile.name,
                        textResultsCount: response.results.length,
                        imageDataUrl: capturedImageDataUrl,
                        processed: true,
                    };
                    return {
                        ...prev,
                        processedImages: imagesProcessedCount,
                        totalTextResults: totalTextResultsAcrossImages,
                        resultItems: updatedItems,
                    };
                });
                 messageApi.success({ content: `图片 ${currentFile.name} 处理完成，发现 ${response.results.length} 个文本。`, key: `${batchOcrMessageKey}_s${idx}`, duration: 2 });

            } catch (error) {
                imagesProcessedCount++;
                const apiError = error as CustomApiError;
                console.error(`OCR检测失败 (图片: ${currentFile.name}):`, apiError);
                setOcrProcessingResults(null);
                setBatchOcrResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[idx] = {
                        index: idx,
                        fileName: currentFile.name,
                        textResultsCount: 0,
                        imageDataUrl: null,
                        processed: true,
                        error: apiError.message || '未知错误',
                    };
                    return { ...prev, processedImages: imagesProcessedCount, resultItems: updatedItems };
                });
                 messageApi.error({ content: `图片 ${currentFile.name} 检测失败: ${apiError.message || '未知错误'}`, key: `${batchOcrMessageKey}_e${idx}`, duration: 3 });
            }
            setTimeout(() => processNextOcrImage(idx + 1), batchProcessingInterval);
        };

        processNextOcrImage(0);
    };

    const handleSaveAllOcrResults = async () => {
        const validResults = batchOcrResults.resultItems.filter(item => item.processed && item.imageDataUrl);
        if (validResults.length === 0) {
            messageApi.info('没有可供保存的有效OCR结果图像。');
            setShowOcrSaveConfirmModal(false);
            return;
        }

        setIsSavingOcrBatch(true);
        const saveMessageKey = 'batchSaveOcr';
        messageApi.loading({ content: '正在准备批量保存OCR结果...', key: saveMessageKey, duration: 0 });

        setTimeout(async () => {
            try {
                let savedCount = 0;
                for (let i = 0; i < validResults.length; i++) {
                    const item = validResults[i];
                    if (!item.imageDataUrl) continue;

                    const nameWithoutExt = item.fileName.substring(0, item.fileName.lastIndexOf('.')) || item.fileName;
                    const saveFileName = `${nameWithoutExt}_ocr_${selectedDisplayMode}_result.png`;

                    const link = document.createElement('a');
                    link.href = item.imageDataUrl;
                    link.download = saveFileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    savedCount++;

                    if (i < validResults.length - 1) {
                         messageApi.loading({ content: `正在保存 ${i + 1}/${validResults.length}: ${saveFileName}`, key: saveMessageKey, duration: 0 });
                         await new Promise(resolve => setTimeout(resolve, 300));
                    }
                }
                 messageApi.success({ content: `成功保存了 ${savedCount} 个OCR结果图像。`, key: saveMessageKey, duration: 3 });
            } catch (err) {
                console.error('批量保存OCR结果时出错:', err);
                 messageApi.error({ content: '批量保存OCR结果时发生错误。 ', key: saveMessageKey, duration: 3 });
            } finally {
                setIsSavingOcrBatch(false);
                setShowOcrSaveConfirmModal(false);
            }
        }, 100);
    };

    const multiImageTooltipTitle = !imageList || imageList.length <= 1
        ? "需要至少两张图片才能进行多图推理。请通过'打开文件夹'加载多张图片。"
        : autoInferenceEnabled
        ? "启用'切换时智能检测'时不能使用多图推理。请先关闭智能检测功能。"
        : "";

    return (
        <Spin spinning={isInferring || isBatchProcessing || ocrTasksLoading} tip={ocrTasksLoading ? "加载OCR任务中..." : (isBatchProcessing ? "批量OCR推理中..." : "OCR推理中...")}>
            {contextHolder}
            <div style={{ padding: '16px' }}>
                <Title level={4} style={{ textAlign: 'center', marginBottom: '16px' }}>OCR检测</Title>
                {!currentImageInfo && !ocrTasksLoading && (
                    <Alert
                        message="请先在左侧图像工作区加载或选择一张图片后再进行检测。"
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                    />
                )}
                {originalImageInfo && currentImageInfo?.name.includes('(OCR View)') && selectedDisplayMode === 'composite' && !isBatchProcessing &&(
                    <Alert
                        message="OCR复合视图已激活"
                        description="当前显示的是处理后的合成图像。如需重新进行单图推理，面板将自动使用原图。"
                        type="info"
                        showIcon
                        style={{ marginBottom: '12px' }}
                    />
                )}
                 <Modal
                    title="批量OCR检测完成"
                    open={showOcrSaveConfirmModal}
                    onCancel={() => setShowOcrSaveConfirmModal(false)}
                    footer={[
                        <Button key="close" onClick={() => setShowOcrSaveConfirmModal(false)}>
                            关闭
                        </Button>,
                        <Button
                            key="saveAllOcr"
                            type="primary"
                            icon={<SaveOutlined />}
                            loading={isSavingOcrBatch}
                            onClick={handleSaveAllOcrResults}
                            disabled={batchOcrResults.resultItems.filter(item => item.imageDataUrl).length === 0}
                        >
                            保存所有推理结果图像
                        </Button>,
                    ]}
                >
                    <p>批量OCR检测已完成！</p>
                    <p>共处理 {batchOcrResults.processedImages}/{batchOcrResults.totalImages} 张图片，检测到 {batchOcrResults.totalTextResults} 个文本结果。</p>
                    {batchOcrResults.resultItems.filter(item => item.imageDataUrl).length > 0 ?
                        <p>您可以点击下方按钮将所有成功处理并捕获的图像结果保存到本地。</p> :
                        <p>没有成功捕获到可供保存的图像结果。</p>
                    }
                     <Alert
                        message="保存提示"
                        description="批量保存时，浏览器可能会依次提示下载多个文件，请确认您的浏览器允许多文件下载。"
                        type="info"
                        showIcon
                        style={{ marginTop: '10px' }}
                    />
                </Modal>
                <Form
                    form={form}
                    layout="horizontal"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 14 }}
                    labelAlign="left"
                >
                    {ocrTasksError && !ocrTasksLoading && (
                        <Form.Item style={{ marginBottom: '16px' }}>
                            <Alert
                                message="模型加载错误"
                                description={
                                    <div style={{ 
                                        display: 'flex', 
                                        alignItems: 'flex-start', 
                                        gap: '12px', 
                                        flexWrap: 'wrap',
                                        width: '100%'
                                    }}>
                                        <span style={{ 
                                            wordBreak: 'break-word', 
                                            flex: '1', 
                                            minWidth: '0',
                                            lineHeight: '1.5'
                                        }}>
                                            {ocrTasksError}
                                        </span>
                                        <Button 
                                            size="small" 
                                            type="primary" 
                                            style={{ flexShrink: 0 }}
                                            onClick={() => {
                                                messageApi.loading({ content: '正在刷新模型列表...', key: 'refreshModels' });
                                                refetchOcrTasks().then(() => {
                                                    messageApi.success({ content: '模型列表已刷新！', key: 'refreshModels' });
                                                }).catch(() => {
                                                    messageApi.error({ content: '刷新模型列表失败。', key: 'refreshModels' });
                                                });
                                            }}
                                        >
                                            重试
                                        </Button>
                                    </div>
                                }
                                type="error"
                                showIcon
                                closable
                                onClose={() => setOcrTasksError(null)}
                            />
                        </Form.Item>
                    )}

                    <Form.Item
                        label="模型选择"
                        tooltip="选择用于OCR检测的模型/任务"
                    >
                        <Space.Compact style={{ width: '100%' }}>
                            <Select
                                value={selectedTaskValue}
                                onChange={(value) => setSelectedTaskValue(value)}
                                style={{ width: '100%' }}
                                loading={ocrTasksLoading}
                                disabled={isInferring || isBatchProcessing || ocrTasksLoading || availableOcrTasks.length === 0}
                                placeholder={ocrTasksLoading ? "加载OCR任务中..." : (availableOcrTasks.length === 0 ? (ocrTasksError || "无可用OCR任务") : "选择一个OCR任务")}
                            >
                                {availableOcrTasks.map(task => {
                                    // 优先使用 display_name，如果为空则回退到 task_name
                                    const displayName = task.display_name || task.task_name;
                                    return <Option key={task.task_name} value={task.task_name}>{displayName}</Option>;
                                })}
                            </Select>
                            <Tooltip title="刷新OCR模型列表">
                                <Button
                                    icon={<SyncOutlined />}
                                    onClick={() => {
                                        messageApi.loading({ content: '正在刷新模型列表...', key: 'refreshModels' });
                                        refetchOcrTasks().then(() => {
                                            messageApi.success({ content: '模型列表已刷新！', key: 'refreshModels' });
                                        }).catch(() => {
                                            messageApi.error({ content: '刷新模型列表失败。', key: 'refreshModels' });
                                        });
                                    }}
                                    loading={ocrTasksLoading && fetchedTasksData !== undefined}
                                    disabled={isInferring || isBatchProcessing || (reactQueryTasksLoadingInitial && !fetchedTasksData)}
                                />
                            </Tooltip>
                        </Space.Compact>
                    </Form.Item>

                    <Form.Item
                        label={
                            <span>
                                结果展示方式
                            </span>
                        }
                        name="display_mode"
                        tooltip={
                            <div>
                                <div style={{ marginBottom: '16px' }}>
                                    <p><strong>合成视图:</strong></p>
                                    <p>左侧显示原始图像，右侧为处理后的图像，包含识别的文本和高亮区域。</p>
                                    <img src={compositeViewImage} alt="合成视图示例" style={{ maxWidth: '230px', marginTop: '8px', border: '1px solid #eee' }} />
                                </div>
                                <div>
                                    <p><strong>直接渲染视图:</strong></p>
                                    <p>识别结果（文本框和内容）直接叠加显示在原始图像上。</p>
                                    <img src={directRenderImage} alt="直接渲染视图示例" style={{ maxWidth: '230px', marginTop: '8px', border: '1px solid #eee' }} />
                                </div>
                            </div>
                        }
                        rules={[{ required: true, message: '请选择结果展示方式!' }]}
                        initialValue="composite"
                    >
                        <Radio.Group
                            onChange={(e) => setSelectedDisplayMode(e.target.value as OcrDisplayMode)}
                            value={selectedDisplayMode}
                            disabled={isInferring || isBatchProcessing}
                            optionType="button"
                            buttonStyle="solid"
                        >
                            <Radio.Button value="composite">合成视图</Radio.Button>
                            <Radio.Button value="direct_render">直接渲染</Radio.Button>
                        </Radio.Group>
                    </Form.Item>

                     <Form.Item
                        label="多图推理间隔"
                        name="batch_processing_interval"
                        tooltip="多图推理时每张图片处理之间的等待时间（毫秒）"
                        rules={[{ required: true, message: '请设置多图推理间隔!' }]}
                    >
                        <Row gutter={8} align="middle">
                            <Col span={12}>
                                <Slider
                                    min={0}
                                    max={5000}
                                    step={100}
                                    onChange={(value: number) => setBatchProcessingInterval(value)}
                                    value={batchProcessingInterval}
                                    disabled={isInferring || isBatchProcessing}
                                    marks={{ 1000: '1s', 3000: '3s', 5000: '5s' }}
                                    style={{ marginRight: '8px' }}
                                />
                            </Col>
                            <Col span={12}>
                                <InputNumber
                                    min={0}
                                    max={5000}
                                    step={100}
                                    style={{ width: '100%' }}
                                    onChange={(value: number | null) => { if (value !== null) setBatchProcessingInterval(value); }}
                                    value={batchProcessingInterval}
                                    disabled={isInferring || isBatchProcessing}
                                    addonAfter="ms"
                                    size="small"
                                    controls={false}
                                />
                            </Col>
                        </Row>
                    </Form.Item>

                    <Divider style={{ margin: '12px 0' }} />

                    <Form.Item
                        label="切换时智能检测"
                        name="auto_inference"
                        valuePropName="checked"
                        tooltip="启用后，切换图片时自动执行OCR检测，检测完成前不可继续切换"
                    >
                        <Switch
                            checked={autoInferenceEnabled}
                            onChange={(checked) => setAutoInferenceEnabled(checked)}
                            disabled={
                                isInferring ||
                                isBatchProcessing ||
                                ocrTasksLoading || // Disable if tasks are loading
                                availableOcrTasks.length === 0 || // Disable if no tasks available
                                !imageList ||
                                imageList.length <= 1
                            }
                        />
                    </Form.Item>

                    <Form.Item wrapperCol={{ offset: 0, span: 24 }} style={{ marginTop: '24px' }}>
                        <Space style={{ width: '100%', justifyContent: 'center' }}>
                            <Button
                                type="primary"
                                onClick={handleSingleImageOcrInference}
                                disabled={isInferring || isBatchProcessing || !currentImageInfo || ocrTasksLoading || availableOcrTasks.length === 0}
                            >
                                {originalImageInfo && currentImageInfo?.name.includes('(OCR View)') && !isBatchProcessing ? '重新推理原图' : '单图推理'}
                            </Button>
                            <Tooltip title={multiImageTooltipTitle}>
                                <span>
                                    <Button
                                        onClick={handleMultiImageOcrDetection}
                                        disabled={isInferring || isBatchProcessing || !imageList || imageList.length <= 1 || ocrTasksLoading || availableOcrTasks.length === 0 || autoInferenceEnabled}
                                    >
                                        多图推理
                                    </Button>
                                </span>
                            </Tooltip>
                        </Space>
                    </Form.Item>
                </Form>
            </div>
        </Spin>
    );
};

export default OcrDetectionPanel;